package com.neo.user.app.tennant.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.api.code.GlobalResponseCodeEnum;
import com.neo.user.app.userinfo.impl.UserServiceImpl;
import com.neo.user.client.tenant.api.DepartmentService;
import com.neo.user.client.tenant.dto.DepartmentInfoDTO;
import com.neo.user.client.tenant.dto.UserDepartmentDTO;
import com.neo.user.client.userinfo.dto.UserInfoDTO;
import com.neo.user.domain.entity.DepartmentLeader;
import com.neo.user.domain.entity.UserDepartment;
import com.neo.user.domain.gateway.IDepartmentLeaderRepository;
import com.neo.user.domain.gateway.IUserDepartmentRepository;
import com.neo.user.domain.gateway.department.IDepartmentRepository;
import com.neo.user.domain.gateway.department.dto.DepartmentDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DepartmentServiceImpl implements DepartmentService {

    @Autowired
    private IDepartmentRepository iDepartmentRepository;

    @Autowired
    private IUserDepartmentRepository iUserDepartmentRepository;

    @Autowired
    private IDepartmentLeaderRepository iDepartmentLeaderRepository;

    @Autowired
    private UserServiceImpl userServiceImpl;

    @Override
    public SingleResponse<DepartmentInfoDTO> getDeptInfoById(Long tenantId, Long deptId) {

        SingleResponse<DepartmentDTO> departmentDTOSingleResponse = iDepartmentRepository.get(tenantId, deptId, true);
        if (!departmentDTOSingleResponse.isSuccess()) {
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }
        DepartmentInfoDTO departmentInfoDTO = convertDepartmentInfoDTO(departmentDTOSingleResponse.getData());

        LambdaQueryWrapper<DepartmentLeader> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DepartmentLeader::getTenantId, tenantId);
        queryWrapper.eq(DepartmentLeader::getDeptId, deptId);
        queryWrapper.eq(DepartmentLeader::getIsDeleted, 0);
        Optional<DepartmentLeader> one = iDepartmentLeaderRepository.getOneOpt(queryWrapper);
        if (one.isPresent()) {
            Long leaderId = one.get().getLeaderId();
            SingleResponse<UserInfoDTO> response = userServiceImpl.queryUserByUserId(leaderId);
            if (response.isSuccess() && response.getData() != null) {
                departmentInfoDTO.setLeaderId(leaderId);
                departmentInfoDTO.setLeaderName(response.getData().getUnick());
            }
        }
        return SingleResponse.buildSuccess(departmentInfoDTO);
    }

    @Override
    public SingleResponse<Map<Long, DepartmentInfoDTO>> getDeptInfoMapByIds(Long tenantId , List<Long> deptIds) {
        MultiResponse<DepartmentDTO> response = iDepartmentRepository.multiGet(tenantId, deptIds, true);
        if (!response.isSuccess() || CollectionUtils.isEmpty(response.getData())) {
            return SingleResponse.buildSuccess(new HashMap<>());
        }
        Map<Long, DepartmentInfoDTO> map = new HashMap<>();

        LambdaQueryWrapper<DepartmentLeader> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DepartmentLeader::getTenantId, tenantId);
        queryWrapper.in(DepartmentLeader::getDeptId, deptIds);
        queryWrapper.in(DepartmentLeader::getIsDeleted, 0);
        List<DepartmentLeader> list = iDepartmentLeaderRepository.list(queryWrapper);
        List<Long> leaderIds = list.stream().map(DepartmentLeader::getLeaderId).toList();
        Map<Long, UserInfoDTO> deptLeaderMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(leaderIds)) {
            SingleResponse<Map<Long, UserInfoDTO>> mapResponse = userServiceImpl.queryMapByUserIds(leaderIds);
            if (mapResponse.isSuccess() && mapResponse.getData() != null) {
                Map<Long, UserInfoDTO> userInfoDTOMap = mapResponse.getData();
                list.forEach(departmentLeader -> {
                    deptLeaderMap.put(departmentLeader.getDeptId(), userInfoDTOMap.get(departmentLeader.getLeaderId()));
                });
            }
        }
        response.getData().forEach(a -> {
            DepartmentInfoDTO departmentInfoDTO = convertDepartmentInfoDTO(a);
            if (deptLeaderMap.containsKey(departmentInfoDTO.getDeptId())) {
                UserInfoDTO leader = deptLeaderMap.get(departmentInfoDTO.getDeptId());
                departmentInfoDTO.setLeaderId(leader.getUserId());
                departmentInfoDTO.setLeaderName(leader.getUnick());
            }
            map.put(a.getDeptId(), departmentInfoDTO);
        });
        return SingleResponse.buildSuccess(map);
    }

    @Override
    public SingleResponse<Boolean> checkUserDepartment(Long tenantId, Long userId, List<Long> deptIds, Boolean includeSubDept) {
        if (tenantId == null || userId == null || CollectionUtils.isEmpty(deptIds) || includeSubDept == null) {
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }
        Set<Long> deptIdSet = new HashSet<>(deptIds);
        if (includeSubDept) {
            deptIds.forEach(a->{
                MultiResponse<Long> allSubDeptIds = iDepartmentRepository.getAllSubDeptIds(tenantId, a);
                if (allSubDeptIds.isSuccess()) {
                    deptIdSet.addAll(allSubDeptIds.getData());
                }
            });
        }
        LambdaQueryWrapper<UserDepartment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserDepartment::getTenantId, tenantId);
        wrapper.eq(UserDepartment::getUserId, userId);
        wrapper.in(UserDepartment::getDeptId, deptIdSet);
        wrapper.eq(UserDepartment::getIsDeleted, 0);
        long count = iUserDepartmentRepository.count(wrapper);
        if (count > 0){
            return SingleResponse.buildFailure(true);
        }
        return SingleResponse.buildSuccess(false);
    }

    @Override
    public MultiResponse<Long> getAllUserIdByDeptId(Long tenantId, Long deptId, Boolean includeSubDept) {
        if (tenantId == null || deptId == null || includeSubDept == null) {
            return MultiResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }
        Set<Long> deptIdSet = new HashSet<>();
        deptIdSet.add(deptId);
        if (includeSubDept) {
            MultiResponse<Long> allSubDeptIds = iDepartmentRepository.getAllSubDeptIds(tenantId, deptId);
            if (allSubDeptIds.isSuccess()) {
                deptIdSet.addAll(allSubDeptIds.getData());
            }
        }
        LambdaQueryWrapper<UserDepartment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserDepartment::getTenantId, tenantId);
        wrapper.in(UserDepartment::getDeptId, deptIdSet);
        wrapper.eq(UserDepartment::getIsDeleted, 0);
        List<UserDepartment> list = iUserDepartmentRepository.list(wrapper);
        if (!CollectionUtils.isEmpty(list)){
            List<Long> userIds = list.stream().map(UserDepartment::getUserId).collect(Collectors.toList());
            return MultiResponse.of(userIds);
        }
        return MultiResponse.of(new ArrayList<>());
    }

    @Override
    public MultiResponse<Long> getAllUserIdByDeptIds(Long tenantId, List<Long> deptIds, Boolean includeSubDept) {
        if (tenantId == null || includeSubDept == null) {
            return MultiResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }
        if (CollectionUtils.isEmpty(deptIds)) {
            return MultiResponse.of(new ArrayList<>());
        }
        Set<Long> deptIdSet = new HashSet<>();
        deptIdSet.addAll(deptIds);
        if (includeSubDept) {
            for(Long deptId:deptIds){
                MultiResponse<Long> allSubDeptIds = iDepartmentRepository.getAllSubDeptIds(tenantId, deptId);
                if (allSubDeptIds.isSuccess()) {
                    deptIdSet.addAll(allSubDeptIds.getData());
                }
            }
        }
        LambdaQueryWrapper<UserDepartment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserDepartment::getTenantId, tenantId);
        wrapper.in(UserDepartment::getDeptId, deptIdSet);
        wrapper.eq(UserDepartment::getIsDeleted, 0);
        List<UserDepartment> list = iUserDepartmentRepository.list(wrapper);
        if (!CollectionUtils.isEmpty(list)){
            List<Long> userIds = list.stream().map(UserDepartment::getUserId).collect(Collectors.toList());
            return MultiResponse.of(userIds);
        }
        return MultiResponse.of(new ArrayList<>());
    }

    @Override
    public MultiResponse<Long> getDeptIdsByLeaderId(Long tenantId, Long leaderUserId) {
        LambdaQueryWrapper<DepartmentLeader> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DepartmentLeader::getTenantId, tenantId);
        wrapper.eq(DepartmentLeader::getLeaderId, leaderUserId);
        wrapper.eq(DepartmentLeader::getIsDeleted, 0);
        List<DepartmentLeader> list = iDepartmentLeaderRepository.list(wrapper);
        if (!CollectionUtils.isEmpty(list)){
            return MultiResponse.of(list.stream().map(DepartmentLeader::getDeptId).toList());
        }
        return MultiResponse.of(new ArrayList<>());
    }

    @Override
    public MultiResponse<UserDepartmentDTO> getAllUserDeptByDeptId(Long tenantId, Long deptId, Boolean includeSubDept) {
        if (tenantId == null || deptId == null || includeSubDept == null) {
            return MultiResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }
        Set<Long> deptIdSet = new HashSet<>();
        deptIdSet.add(deptId);
        if (includeSubDept) {
            MultiResponse<Long> allSubDeptIds = iDepartmentRepository.getAllSubDeptIds(tenantId, deptId);
            if (allSubDeptIds.isSuccess()) {
                deptIdSet.addAll(allSubDeptIds.getData());
            }
        }
        LambdaQueryWrapper<UserDepartment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserDepartment::getTenantId, tenantId);
        wrapper.in(UserDepartment::getDeptId, deptIdSet);
        wrapper.eq(UserDepartment::getIsDeleted, 0);
        List<UserDepartment> list = iUserDepartmentRepository.list(wrapper);
        List<UserDepartmentDTO> userDepartmentDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)){
            list.forEach(userDepartment -> {
                UserDepartmentDTO userDepartmentDTO = convertUserDepartmentDTO(userDepartment);
                userDepartmentDTOList.add(userDepartmentDTO);
            });
            return MultiResponse.of(userDepartmentDTOList);
        }
        return MultiResponse.of(new ArrayList<>());
    }

    @Override
    public MultiResponse<UserDepartmentDTO> getAllUserDeptByDeptIds(Long tenantId, List<Long> deptIds, Boolean includeSubDept) {
        if (tenantId == null || includeSubDept == null) {
            return MultiResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }
        if (CollectionUtils.isEmpty(deptIds)) {
            return MultiResponse.of(new ArrayList<>());
        }
        Set<Long> deptIdSet = new HashSet<>();
        deptIdSet.addAll(deptIds);
        if (includeSubDept) {
            for(Long deptId:deptIds){
                MultiResponse<Long> allSubDeptIds = iDepartmentRepository.getAllSubDeptIds(tenantId, deptId);
                if (allSubDeptIds.isSuccess()) {
                    deptIdSet.addAll(allSubDeptIds.getData());
                }
            }
        }
        LambdaQueryWrapper<UserDepartment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserDepartment::getTenantId, tenantId);
        wrapper.in(UserDepartment::getDeptId, deptIdSet);
        wrapper.eq(UserDepartment::getIsDeleted, 0);
        List<UserDepartment> list = iUserDepartmentRepository.list(wrapper);
        List<UserDepartmentDTO> userDepartmentDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)){
            list.stream().forEach(userDepartment -> {
                UserDepartmentDTO userDepartmentDTO = convertUserDepartmentDTO(userDepartment);
                userDepartmentDTOList.add(userDepartmentDTO);
            });
            return MultiResponse.of(userDepartmentDTOList);
        }
        return MultiResponse.of(new ArrayList<>());
    }

    @Override
    public MultiResponse<Long> getDeptIdsByExtra(Long tenantId, String extra, Boolean includeSubDept) {
        MultiResponse<Long> response = iDepartmentRepository.getDeptIdsByExtra(tenantId, extra);
        if (CollectionUtils.isEmpty(response.getData())) {
            return response;
        }
        List<Long> deptIds = response.getData();
        Set<Long> deptIdSet = new HashSet<>();
        deptIdSet.addAll(deptIds);
        if (includeSubDept) {
            for(Long deptId:deptIds){
                MultiResponse<Long> allSubDeptIds = iDepartmentRepository.getAllSubDeptIds(tenantId, deptId);
                if (allSubDeptIds.isSuccess()) {
                    deptIdSet.addAll(allSubDeptIds.getData());
                }
            }
        }
        return MultiResponse.of(deptIdSet);
    }

    @Override
    public MultiResponse<Long> getSubDeptIdsByDeptId(Long tenantId, Long deptId) {
        MultiResponse<Long> allSubDeptIds = iDepartmentRepository.getAllSubDeptIds(tenantId, deptId);
        if (allSubDeptIds.isSuccess()) {
            return allSubDeptIds;
        }
        return MultiResponse.of(new ArrayList<>());
    }

    @Override
    public SingleResponse<Map<Long, List<Long>>> batchGetSubDeptIds(Long tenantId, List<Long> deptIds) {
        SingleResponse<Map<Long, List<Long>>> response = iDepartmentRepository.batchGetSubDeptIds(tenantId, deptIds);
        if (response.isSuccess()) {
            return response;
        }
        return SingleResponse.buildSuccess(new HashMap<>());
    }

    private DepartmentInfoDTO convertDepartmentInfoDTO(DepartmentDTO departmentDTO) {
        DepartmentInfoDTO departmentInfoDTO = new DepartmentInfoDTO();
        departmentInfoDTO.setName(departmentDTO.getName());
        departmentInfoDTO.setParentId(departmentDTO.getParentId());
        departmentInfoDTO.setDeptId(departmentDTO.getDeptId());
        departmentInfoDTO.setTenantId(departmentDTO.getTenantId());
        return departmentInfoDTO;
    }

    private UserDepartmentDTO convertUserDepartmentDTO(UserDepartment userDepartment) {
        UserDepartmentDTO userDepartmentDTO = new UserDepartmentDTO();
        userDepartmentDTO.setTenantId(userDepartment.getTenantId());
        userDepartmentDTO.setUserId(userDepartment.getUserId());
        userDepartmentDTO.setDeptId(userDepartment.getDeptId());
        return userDepartmentDTO;
    }
}
