package com.neo.user.adapter.tesla.tenant;

import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.user.app.tennant.service.impl.DepartmentServiceImpl;
import com.neo.user.client.tenant.api.DepartmentService;
import com.neo.user.client.tenant.dto.DepartmentInfoDTO;
import com.neo.user.client.tenant.dto.UserDepartmentDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("departmentService")
@Slf4j
public class DepartmentTeslaService implements DepartmentService {

    @Autowired
    private DepartmentServiceImpl departmentServiceImpl;

    @Override
    public SingleResponse<DepartmentInfoDTO> getDeptInfoById(Long tenantId, Long deptId) {
        return departmentServiceImpl.getDeptInfoById(tenantId, deptId);
    }

    @Override
    public SingleResponse<Map<Long, DepartmentInfoDTO>> getDeptInfoMapByIds(Long tenantId , List<Long> deptIds) {
        return departmentServiceImpl.getDeptInfoMapByIds(tenantId , deptIds);
    }

    @Override
    public SingleResponse<Boolean> checkUserDepartment(Long tenantId, Long userId, List<Long> deptIds, Boolean includeSubDept) {
        return departmentServiceImpl.checkUserDepartment(tenantId, userId, deptIds, includeSubDept);
    }

    @Override
    public MultiResponse<Long> getAllUserIdByDeptId(Long tenantId, Long deptId, Boolean includeSubDept) {
        return departmentServiceImpl.getAllUserIdByDeptId(tenantId, deptId, includeSubDept);
    }

    @Override
    public MultiResponse<Long> getAllUserIdByDeptIds(Long tenantId, List<Long> deptIds, Boolean includeSubDept) {
        return departmentServiceImpl.getAllUserIdByDeptIds(tenantId, deptIds, includeSubDept);
    }

    @Override
    public MultiResponse<Long> getDeptIdsByLeaderId(Long tenantId, Long leaderUserId) {
        return departmentServiceImpl.getDeptIdsByLeaderId(tenantId, leaderUserId);
    }

    @Override
    public MultiResponse<UserDepartmentDTO> getAllUserDeptByDeptId(Long tenantId, Long deptId, Boolean includeSubDept) {
        return departmentServiceImpl.getAllUserDeptByDeptId(tenantId, deptId, includeSubDept);
    }

    @Override
    public MultiResponse<UserDepartmentDTO> getAllUserDeptByDeptIds(Long tenantId, List<Long> deptIds, Boolean includeSubDept) {
        return departmentServiceImpl.getAllUserDeptByDeptIds(tenantId, deptIds, includeSubDept);
    }

    @Override
    public MultiResponse<Long> getDeptIdsByExtra(Long tenantId, String extra, Boolean includeSubDept) {
        return departmentServiceImpl.getDeptIdsByExtra(tenantId, extra, includeSubDept);
    }

    @Override
    public MultiResponse<Long> getSubDeptIdsByDeptId(Long tenantId, Long deptId) {
        return departmentServiceImpl.getSubDeptIdsByDeptId(tenantId, deptId);
    }

    @Override
    public SingleResponse<Map<Long, List<Long>>> batchGetSubDeptIds(Long tenantId, List<Long> deptIds) {
        return departmentServiceImpl.batchGetSubDeptIds(tenantId, deptIds);
    }

}
