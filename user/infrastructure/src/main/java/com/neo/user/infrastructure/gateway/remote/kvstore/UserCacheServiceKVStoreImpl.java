package com.neo.user.infrastructure.gateway.remote.kvstore;

import com.neo.cache.CacheService;
import com.neo.user.domain.gateway.cache.UserCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service("userCacheServiceKVStoreImpl")
@Slf4j
public class UserCacheServiceKVStoreImpl implements UserCacheService {

    @Autowired
    private CacheService cacheService;

    @Override
    public boolean setex(String key, String value, int expireSeconds) {
        return cacheService.setex(key, value, expireSeconds);
    }

    @Override
    public Object get(String key) {
        return cacheService.get(key);
    }

    @Override
    public int delete(String key) {
        return cacheService.del(key) ? 1 : 0;
    }

    @Override
    public Long increx(String key, long l, int expireSeconds) {
        return cacheService.increx(key, l, expireSeconds);
    }
}
