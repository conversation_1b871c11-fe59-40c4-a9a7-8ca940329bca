package com.neo.user.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.user.domain.entity.UserLoginLog;
import com.neo.user.domain.gateway.IUserLoginLogRepository;
import com.neo.user.infrastructure.mapper.UserLoginLogMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @apiNote 用户操作日志表
* @since 2024-01-03 15:35:04
*/
@Service
public class UserLoginLogRepositoryImpl extends ServiceImpl<UserLoginLogMapper,UserLoginLog>
    implements IUserLoginLogRepository {

}
