package com.neo.user.infrastructure.gateway.remote.wechat;

import com.neo.user.domain.gateway.dto.ThirdPartySessionKeyInfoDTO;
import com.neo.user.domain.gateway.dto.WechatSessionKeyInfoDTO;
import com.neo.user.domain.gateway.thirdparty.ThirdPartyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class WechatMiniProgramServiceImpl implements ThirdPartyService {

    @Autowired
    private WechatOpenServiceImpl wechatOpenServiceImpl;

    @Override
    public ThirdPartySessionKeyInfoDTO getAccessToken(String appId, String code) {
        //todo
        WechatSessionKeyInfoDTO xcxAccessToken = wechatOpenServiceImpl.getXcxAccessToken(appId, code);
        if (xcxAccessToken != null) {
            ThirdPartySessionKeyInfoDTO thirdPartySessionKeyInfoDTO = new ThirdPartySessionKeyInfoDTO();
            thirdPartySessionKeyInfoDTO.setAppId(appId);
            thirdPartySessionKeyInfoDTO.setOpenId(xcxAccessToken.getOpenId());
            thirdPartySessionKeyInfoDTO.setUnionId(xcxAccessToken.getUnionId());
            thirdPartySessionKeyInfoDTO.setSessionKey(xcxAccessToken.getSessionKey());
            return thirdPartySessionKeyInfoDTO;
        }
        return null;
    }
}
