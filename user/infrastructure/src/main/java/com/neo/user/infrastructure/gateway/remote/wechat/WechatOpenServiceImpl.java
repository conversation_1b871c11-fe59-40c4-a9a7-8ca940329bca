package com.neo.user.infrastructure.gateway.remote.wechat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.neo.user.client.enums.ThirdPartyEnum;
import com.neo.user.client.userinfo.dto.EnumThirdPartyAppInfoDTO;
import com.neo.user.domain.gateway.cache.EnumThirdPartyAppInfoCacheService;
import com.neo.user.domain.gateway.dto.WechatSessionKeyInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WechatOpenServiceImpl {

    @Autowired
    private EnumThirdPartyAppInfoCacheService enumThirdPartyAppInfoLocalCacheImpl;

    /**
     * 小程序登录
     * https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-login/code2Session.html
     * @param appId
     * @param code
     * @return
     */
    public WechatSessionKeyInfoDTO getXcxAccessToken(String appId, String code){
        EnumThirdPartyAppInfoDTO thirdPartyAppInfoDTO = enumThirdPartyAppInfoLocalCacheImpl.getByAppIdAndThirdPlatform(appId, ThirdPartyEnum.WECHAT_XCX.getPlatform());
        if (thirdPartyAppInfoDTO == null) {
            return null;
        }

        WechatSessionKeyInfoDTO wechatSessionKeyInfoDTO = new WechatSessionKeyInfoDTO();
        wechatSessionKeyInfoDTO.setAppId(appId);

        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String url = String.format("https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code", thirdPartyAppInfoDTO.getAppKey(),
                thirdPartyAppInfoDTO.getAppSecret(), code);

        try {
            HttpGet request = new HttpGet(url);
            response = httpClient.execute(request);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                log.error("【微信】小程序登录错误：" + url + " 返回：" + response.toString());
                return null;
            }
            String res = EntityUtils.toString(response.getEntity(), "utf-8");
            JSONObject jsonObject = JSON.parseObject(res);
            //这里与接口文档不一致，正确返回时，结构体里没有errcode字段，异常返回时，才有errcode和errmsg
            if (jsonObject.getString("session_key") != null && jsonObject.getString("openid") != null
                    && jsonObject.getString("unionid") != null) {
                wechatSessionKeyInfoDTO.setSessionKey(jsonObject.getString("session_key"));
                wechatSessionKeyInfoDTO.setOpenId(jsonObject.getString("openid"));
                wechatSessionKeyInfoDTO.setUnionId(jsonObject.getString("unionid"));
                return wechatSessionKeyInfoDTO;
            } else {
                log.error("【微信】小程序登录失败：" + url + " 返回："  + res);
                return null;
            }
        } catch (Exception e) {
            log.error("【微信】小程序登录异常：" + url + " 异常："  + e.getMessage(), e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                httpClient.close();
            } catch (Exception e2) {
                log.error("【微信】小程序登录异常：" + url + " 异常：" + e2.getMessage(), e2);
            }
        }
        return null;
    }

}
