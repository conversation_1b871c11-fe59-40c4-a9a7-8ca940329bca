package com.neo.user.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.user.domain.entity.UserPwd;
import com.neo.user.domain.gateway.IUserPwdRepository;
import com.neo.user.infrastructure.mapper.UserPwdMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @apiNote 
* @since 2024-01-03 15:35:05
*/
@Service
public class UserPwdRepositoryImpl extends ServiceImpl<UserPwdMapper,UserPwd>
    implements IUserPwdRepository{

}