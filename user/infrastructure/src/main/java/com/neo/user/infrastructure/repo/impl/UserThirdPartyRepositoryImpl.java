package com.neo.user.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.user.domain.entity.UserThirdParty;
import com.neo.user.domain.gateway.IUserThirdPartyRepository;
import com.neo.user.infrastructure.mapper.UserThirdPartyMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @apiNote 第三方平台绑定表
* @since 2024-01-03 15:35:05
*/
@Service
public class UserThirdPartyRepositoryImpl extends ServiceImpl<UserThirdPartyMapper,UserThirdParty>
    implements IUserThirdPartyRepository{

}