package com.neo.user.infrastructure.gateway.remote.saas.qywx;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.neo.cache.CacheService;
import com.neo.user.client.enums.ThirdPartyEnum;
import com.neo.user.domain.entity.Tenant;
import com.neo.user.domain.entity.TenantThirdParty;
import com.neo.user.domain.gateway.ITenantRepository;
import com.neo.user.domain.gateway.ITenantThirdPartyRepository;
import com.neo.user.domain.gateway.department.dto.DepartmentDTO;
import com.neo.user.domain.gateway.dto.TenantCheckInDTO;
import com.neo.user.domain.gateway.dto.TenantThirdPartyDTO;
import com.neo.user.domain.gateway.dto.TenantThirdPartySessionKeyInfoDTO;
import com.neo.user.domain.gateway.dto.TenantUserThirdInfoDTO;
import com.neo.user.domain.gateway.thirdparty.TenantSaasService;
import com.neo.user.domain.utils.CommonUtil;
import com.neo.user.domain.utils.TenantThirdPartyConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class QiyeweixinSaasServiceImpl implements TenantSaasService {

//    @Value("${wx.qy.suiteId}")
//    private String suiteId;
//
//    @Value("${wx.qy.suiteSecret}")
//    private String suiteSecret;

    @Value("${wx.qy.web.suiteId}")
    private String webSuiteId;

    @Value("${wx.qy.web.suiteSecret}")
    private String webSuiteSecret;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private QiyeweixinSaasOpenServiceImpl qiyeweixinSaasOpenServiceImpl;

    @Autowired
    private ITenantThirdPartyRepository tenantThirdPartyRepository;

    @Autowired
    private ITenantRepository tenantRepository;

    @Autowired
    private Environment env;

    public String getSuiteId(String appId) {
        String key = "wx.qy." + appId.toLowerCase() + ".suiteId";
        return env.getProperty(key);
    }

    public String getSuiteSecret(String appId) {
        String key = "wx.qy." + appId.toLowerCase() + ".suiteSecret";
        return env.getProperty(key);
    }

    @Override
    public Boolean refreshSuiteTicket(String appId, String suiteTicket) {
        cacheService.setex("SuiteTicket_" + appId,  suiteTicket, 30 * 60);
        cacheService.del("SuiteAccessToken_" + appId);
        return true;
    }

    @Override
    public String getPreAuthCode(String appId) {
        return qiyeweixinSaasOpenServiceImpl.getPreAuthCode(getCachedSuiteAccessToken(appId));
    }

    @Override
    public TenantThirdPartyDTO getTenantThirdParty(String appId, String authCode) {
        String res = qiyeweixinSaasOpenServiceImpl.getTenantPermanentCode(getCachedSuiteAccessToken(appId), authCode);
        if (StringUtils.isEmpty(res)) {
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(res);
        log.info("【企业微信Saas】企业永久授权码：" + jsonObject.getString("permanent_code"));

        JSONObject authCorpInfo = jsonObject.getJSONObject("auth_corp_info");
        LambdaQueryWrapper<TenantThirdParty> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TenantThirdParty::getCorpId, authCorpInfo.getString("corpid"));
        wrapper.eq(TenantThirdParty::getAppId, appId);
        wrapper.eq(TenantThirdParty::getThirdType, ThirdPartyEnum.QIYEWEIXIN_SAAS.getCode());
        TenantThirdParty one = tenantThirdPartyRepository.getOne(wrapper);
        if (one == null) {
            // 未创建租户，返回三方信息
            TenantThirdPartyDTO tenantThirdPartyDTO = new TenantThirdPartyDTO();
            tenantThirdPartyDTO.setAppId(appId);
            tenantThirdPartyDTO.setName(authCorpInfo.getString("corp_name"));
            tenantThirdPartyDTO.setCorpId(authCorpInfo.getString("corpid"));
            tenantThirdPartyDTO.setThirdType(ThirdPartyEnum.QIYEWEIXIN_SAAS.getCode());
            tenantThirdPartyDTO.setCorpName(authCorpInfo.getString("corp_name"));
            tenantThirdPartyDTO.setPermanentCode(jsonObject.getString("permanent_code"));
            tenantThirdPartyDTO.setLogo(authCorpInfo.getString("corp_square_logo_url"));
            // 初始用户信息
            JSONObject authUserInfo = jsonObject.getJSONObject("auth_user_info");
            tenantThirdPartyDTO.setThirdUserId(authUserInfo.getString("userid"));
            tenantThirdPartyDTO.setThirdUserName(authUserInfo.getString("name"));
            tenantThirdPartyDTO.setThirdUserOpenId(authUserInfo.getString("open_userid"));
            log.info("【企业微信Saas】初始化租户信息 corpid:" + tenantThirdPartyDTO.getCorpId() + " corp_name:" + tenantThirdPartyDTO.getCorpName());
            return tenantThirdPartyDTO;
        }
        // 已创建过租户
        if (!one.getPermanentCode().equals(jsonObject.getString("permanent_code"))){//更新企业永久授权码
            log.info("【企业微信Saas】租户已存在，更新企业永久授权码 tenantId=" + one.getTenantId() + " " + jsonObject.getString("permanent_code"));
            one.setCorpName(authCorpInfo.getString("corp_name"));
            one.setPermanentCode(jsonObject.getString("permanent_code"));
            one.setUpdated(CommonUtil.getCurrentSeconds());
            tenantThirdPartyRepository.updateById(one);
        }
        LambdaQueryWrapper<Tenant> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(Tenant::getId, one.getTenantId());
        Tenant tenant = tenantRepository.getOne(wrapper2);
        return TenantThirdPartyConverter.doToVO(tenant, one);
    }

    @Override
    public Boolean saveTenantThirdParty(TenantThirdPartyDTO tenantThirdPartyDTO) {
        if (tenantThirdPartyDTO == null || tenantThirdPartyDTO.getTenantId() == null
                || StringUtils.isBlank(tenantThirdPartyDTO.getAppId())
                || StringUtils.isEmpty(tenantThirdPartyDTO.getCorpId())
                || StringUtils.isEmpty(tenantThirdPartyDTO.getCorpName())) {
            return false;
        }
        TenantThirdParty tenantThirdParty = new TenantThirdParty();
        tenantThirdParty.setTenantId(tenantThirdPartyDTO.getTenantId());
        tenantThirdParty.setAppId(tenantThirdPartyDTO.getAppId());
        tenantThirdParty.setCorpId(tenantThirdPartyDTO.getCorpId());
        tenantThirdParty.setCorpName(tenantThirdPartyDTO.getCorpName());
        tenantThirdParty.setThirdType(ThirdPartyEnum.QIYEWEIXIN_SAAS.getCode());
        tenantThirdParty.setPermanentCode(tenantThirdPartyDTO.getPermanentCode());
        tenantThirdParty.setCreated(CommonUtil.getCurrentSeconds());
        tenantThirdParty.setUpdated(CommonUtil.getCurrentSeconds());
        tenantThirdPartyRepository.save(tenantThirdParty);
        return true;
    }


    @Override
    public TenantUserThirdInfoDTO getTenantUserInfo(String appId, Long tenantId, String thirdUserId) {
        String res = qiyeweixinSaasOpenServiceImpl.getUserInfo(getCachedCorpAccessToken(appId, tenantId), thirdUserId);
        if (StringUtils.isEmpty(res)) {
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(res);
        log.info("【企业微信Saas】读取用户：" + jsonObject.getString("name"));
        TenantUserThirdInfoDTO tenantUserThirdInfoDTO = new TenantUserThirdInfoDTO();
        tenantUserThirdInfoDTO.setTenantId(tenantId);
        tenantUserThirdInfoDTO.setUserId(jsonObject.getString("userid"));
        tenantUserThirdInfoDTO.setName(jsonObject.getString("name"));
        // TODO 待补充其他字段

        return tenantUserThirdInfoDTO;
    }

    @Override
    public TenantThirdPartySessionKeyInfoDTO getUserAccessToken(String appId, String authCode, String type) {
        String s = null;
        switch (type) {
            case "JS":
                s = qiyeweixinSaasOpenServiceImpl.jscode2session(getCachedSuiteAccessToken(appId), authCode);
                break;
//            case "XCX_OAUTH2":
//                s = qiyeweixinSaasOpenServiceImpl.getuserdetail3rd(getCachedSuiteAccessToken(appId), authCode);
//                break;
            case "WEB_OAUTH2":
                s = qiyeweixinSaasOpenServiceImpl.getuserinfo3rd(getCachedWebSuiteAccessToken(), authCode);
                break;
            default:
                break;
        }

        if (StringUtils.isEmpty(s)) {
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(s);
        TenantThirdPartySessionKeyInfoDTO sessionKeyInfoDTO = new TenantThirdPartySessionKeyInfoDTO();
        sessionKeyInfoDTO.setAppId(appId);
        // TODO 待确认，是否需要corpId判空，若用户不属于任何企业，只会返回openid
        sessionKeyInfoDTO.setCorpId(jsonObject.getString("corpid"));
        sessionKeyInfoDTO.setOpenId(jsonObject.getString("userid"));//用户在企业内的UserID，如果该企业与第三方应用没有授权关系时，返回密文UserId，有授权关系时，按照升级后的ID策略返回明文或密文
        sessionKeyInfoDTO.setUnionId(jsonObject.getString("open_userid"));//全局唯一。对于同一个服务商，不同应用获取到企业内同一个成员的open_userid是相同的，最多64个字节。仅第三方应用可获取
        sessionKeyInfoDTO.setSessionKey(jsonObject.getString("user_ticket"));
        sessionKeyInfoDTO.setExpireIn(jsonObject.getInteger("expires_in"));
        return sessionKeyInfoDTO;
    }

    @Override
    public String getTenantUserPrivateInfo(String appId, String sessionKey) {
        String s = qiyeweixinSaasOpenServiceImpl.getuserdetail3rd(getCachedSuiteAccessToken(appId), sessionKey);
        if (StringUtils.isEmpty(s)) {
            return null;
        }
        return s;
    }

    @Override
    public Long getTenantIdByCorpId(String appId, String corpId) {
        LambdaQueryWrapper<TenantThirdParty> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TenantThirdParty::getCorpId, corpId);
        wrapper.eq(TenantThirdParty::getAppId, appId);
        wrapper.eq(TenantThirdParty::getThirdType, ThirdPartyEnum.QIYEWEIXIN_SAAS.getCode());
        TenantThirdParty one = tenantThirdPartyRepository.getOne(wrapper);
        if (one == null) {
            return null;
        }
        return one.getTenantId();
    }

    @Override
    public Boolean refreshWebSuiteTicket(String suiteTicket) {
        cacheService.setex("WebSuiteTicket_",  suiteTicket, 30 * 60);
        return true;
    }


    private String getCachedCorpAccessToken(String appId, Long tenantId) {
        String cacheKey = "TenantAccessToken_" + appId + "_" + tenantId;
        String s = cacheService.get(cacheKey);
        if (StringUtils.isNoneEmpty(s)){
            log.info("【企业微信Saas】从缓存中获取企业AccessToken " + appId + " " + tenantId);
            return s;
        }
        LambdaQueryWrapper<TenantThirdParty> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TenantThirdParty::getTenantId, tenantId);
        wrapper.eq(TenantThirdParty::getAppId, appId);
        TenantThirdParty one = tenantThirdPartyRepository.getOne(wrapper);
        if (one != null) {
            String corpToken = qiyeweixinSaasOpenServiceImpl.getCorpToken(getCachedSuiteAccessToken(appId), one.getCorpId(), one.getPermanentCode());
            if (StringUtils.isNotEmpty(corpToken)) {
                cacheService.setex(cacheKey, corpToken, 7000);
                log.info("【企业微信Saas】获取企业AccessToken并写入缓存 " + appId + " " + tenantId);
                return corpToken;
            }
        }
        return null;
    }

    private String getCachedSuiteAccessToken(String appId){
        String s = cacheService.get("SuiteAccessToken_" + appId);
        if (StringUtils.isNoneEmpty(s)){
            return s;
        }
        String suiteTicket = cacheService.get("SuiteTicket_" + appId);
        String suiteAccessToken = qiyeweixinSaasOpenServiceImpl.getSuiteAccessToken(getSuiteId(appId), getSuiteSecret(appId), suiteTicket);
        cacheService.setex("SuiteAccessToken_" + appId,  suiteAccessToken, 7000);
        return suiteAccessToken;
    }

    // 一个服务商的所有应用，共用一套web登录授权
    private String getCachedWebSuiteAccessToken(){
        String s = cacheService.get("WebSuiteAccessToken_");
        if (StringUtils.isNoneEmpty(s)){
            return s;
        }
        String webSuiteTicket = cacheService.get("WebSuiteTicket_");
        String webSuiteAccessToken = qiyeweixinSaasOpenServiceImpl.getSuiteAccessToken(webSuiteId, webSuiteSecret, webSuiteTicket);
        cacheService.setex("WebSuiteAccessToken_",  webSuiteAccessToken, 7000);
        return webSuiteAccessToken;
    }


    @Override
    public DepartmentDTO getTenantDepartment(String appId, Long tenantId) {
        DepartmentDTO  departmentDTO = new DepartmentDTO();
        List<Map<String, Object>> departmentSimpleList = qiyeweixinSaasOpenServiceImpl.getDepartmentSimpleList(getCachedCorpAccessToken(appId, tenantId), null);

        return departmentDTO;
    }

    @Override
    public List<TenantCheckInDTO> getCheckinData(String appId, Long tenantId, List<String> userIds, Long startTime, Long endTime) {
        List<Map<String, Object>> checkinData = qiyeweixinSaasOpenServiceImpl.getCheckinData(getCachedCorpAccessToken(appId, tenantId), userIds, startTime, endTime);
        if (checkinData == null || checkinData.isEmpty()) {
            return new ArrayList<>();
        }
        List<TenantCheckInDTO> list = new ArrayList<>();
        for (Map<String, Object> map : checkinData) {
            TenantCheckInDTO dto = new TenantCheckInDTO();
            dto.setThirdUserId(String.valueOf(map.get("userid")));
            dto.setCheckinType(String.valueOf(map.get("checkin_type")));
            dto.setExceptionType(String.valueOf(map.get("exception_type")));
            dto.setNotes(map.get("notes")!=null?String.valueOf(map.get("notes")):null);
            dto.setCheckinTime(map.get("checkin_time")!=null?Integer.valueOf((int)map.get("checkin_time")).longValue():null);
            dto.setLocationTitle(String.valueOf(map.get("location_title")));
            dto.setLocationDetail(String.valueOf(map.get("location_detail")));
            dto.setScheduleCheckinTime(map.get("sch_checkin_time")!=null?Integer.valueOf((int)map.get("sch_checkin_time")).longValue():null);
            dto.setLat(String.valueOf(map.get("lat")));
            dto.setLng(String.valueOf(map.get("lng")));
            dto.setTimelineId(map.get("timeline_id")!=null?(int)map.get("timeline_id"):null);
            dto.setGroupId(map.get("groupid")!=null?(int)map.get("groupid"):null);
            list.add(dto);
        }
        return list;
    }

}
