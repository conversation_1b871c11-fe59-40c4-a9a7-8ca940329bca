package com.neo.user.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.user.domain.entity.UserDepartment;
import com.neo.user.domain.gateway.IUserDepartmentRepository;
import com.neo.user.infrastructure.mapper.UserDepartmentMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @apiNote
*/
@Service
public class UserDepartmentRepositoryImpl extends ServiceImpl<UserDepartmentMapper, UserDepartment>
    implements IUserDepartmentRepository {

}
