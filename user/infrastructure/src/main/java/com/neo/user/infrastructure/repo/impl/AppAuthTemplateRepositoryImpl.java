package com.neo.user.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.user.domain.entity.AppAuthTemplate;
import com.neo.user.domain.gateway.IAppAuthTemplateRepository;
import com.neo.user.infrastructure.mapper.AppAuthTemplateMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @apiNote AppAuthTemplate表
*/
@Service
public class AppAuthTemplateRepositoryImpl extends ServiceImpl<AppAuthTemplateMapper, AppAuthTemplate>
    implements IAppAuthTemplateRepository {

}
