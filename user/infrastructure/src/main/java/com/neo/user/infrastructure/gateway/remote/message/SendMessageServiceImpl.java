package com.neo.user.infrastructure.gateway.remote.message;

import com.neo.api.SingleResponse;
import com.neo.user.domain.gateway.sendmsg.SendMessageService;
import com.sendgrid.*;
import com.sendgrid.helpers.mail.Mail;
import com.sendgrid.helpers.mail.objects.Content;
import com.sendgrid.helpers.mail.objects.Email;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

@Slf4j
@Service
public class SendMessageServiceImpl implements SendMessageService {

    @Override
    public SingleResponse<Boolean> sendMessage(String receivers, Map<String, String> variables, String template) {
        SingleResponse<Boolean> singleResponse = new SingleResponse<>();
        singleResponse.setSuccess(Boolean.TRUE);
        singleResponse.setData(Boolean.TRUE);
        return singleResponse;
    }

}
