package com.neo.user.infrastructure.gateway.remote.kvstore;

import com.alibaba.fastjson.JSON;
import com.neo.cache.CacheService;
import com.neo.user.client.tenant.dto.EnumSessionDomainDTO;
import com.neo.user.client.tenant.dto.EnumTenantSessionDomainDTO;
import com.neo.user.domain.constants.UserConstants;
import com.neo.user.domain.gateway.cache.EnumTenantDomainCacheService;
import com.neo.user.domain.gateway.cache.SessionCacheService;
import com.neo.user.domain.utils.CommonUtil;
import com.neo.user.domain.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Slf4j
@Service("SessionCacheServiceKVStoreImpl")
public class SessionCacheServiceKVStoreImpl implements SessionCacheService {

    @Autowired
    private CacheService cacheService;

    @Autowired
    private EnumTenantDomainCacheService enumTenantDomainLocalCacheImpl;

    /**
     * 生成新的SessionId
     *
     * @param enumSessionDomainDTO
     * @return string
     */
    public static String generateNewSessionId(EnumSessionDomainDTO enumSessionDomainDTO) {
        return UUID.randomUUID().toString().replace(UserConstants.STR_STRIKETHROUGH, StringUtils.EMPTY)
                + enumSessionDomainDTO.getSuffix();

    }

    /**
     * 从缓存中删除指定用户的所有登录态Session
     *
     * @param userId 用户ID
     * @return boolean 是否删除成功: true-删除删除; false-删除失败
     */

    public boolean deleteAllSessionByUserId(Long userId) {
        try {
            String userIdStr = String.valueOf(userId);
            List<String> sessionIds = cacheService.lrange(userIdStr, 0L, -1L);
            if (null == sessionIds) {
                // redis访问异常,返回false
                return false;
            }
            log.warn("start clearAllSession userId:{},sessionIds:{}", userId, JSON.toJSONString(sessionIds));

            // 删除SessionBlocks
            cacheService.mDel(sessionIds.toArray(new String[sessionIds.size()]));

            //排查session清理失败的问题
            for (String sessionId : sessionIds) {
                List<String> sessionBlock = cacheService.hmget(sessionId,
                        UserConstants.SESSION_BLOCK_KEY_USER_ID, UserConstants.SESSION_BLOCK_KEY_SESSION_CREATE_TIME,
                        UserConstants.SESSION_BLOCK_KEY_DEVICE_ID, UserConstants.SESSION_BLOCK_KEY_TENANT_ID);
                if (!"{}".equals(JSON.toJSONString(sessionBlock))) {
                    log.warn("clearAllSession failed , still has sessionBlocks.  sessionBlock={}", JSON.toJSONString(sessionBlock));
                }
            }

            // 删除用户对应的session列表
            cacheService.del(userIdStr);
            log.info("clearAllSession ok userId :{}", userId);
        } catch (Exception e) {
            log.error("clearAllSession failed, userId:{}", userId, e);
            return false;
        }

        return true;
    }

    /**
     * 从缓存中删除指定登录态Session
     *
     * @param userId    用户ID
     * @param sessionId 用户Session标识
     * @return boolean 是否删除成功: true-删除删除; false-删除失败
     */

    public boolean deleteSingleSessionBySessionId(Long userId, String sessionId) {
        if (userId == null || userId <= 0 || StringUtils.isEmpty(sessionId)) {
            log.warn("userId invalid or sessionId is empty, userId={}, sessionId={}", userId, sessionId);
            return false;
        }

        try {
            String userIdStr = String.valueOf(userId);

            // 删除SessionId以及内容
            cacheService.lrem(userIdStr, 1L, sessionId);
            cacheService.del(sessionId);

            // 判断是否需要删除掉 userId List
            List<String> sessionIds = cacheService.lrange(userIdStr, 0L, -1L);
            if (null == sessionIds) {
                // redis访问异常,返回false
                return false;
            }
            if (sessionIds.size() == 0) {
                cacheService.del(userIdStr);
            }

            log.info("clearCurrentSession ok userId={}, sessionId={}", userIdStr, sessionId);
        } catch (Exception e) {
            log.error("clearCurrentSession failed, sessionId:{}", sessionId, e);
            return false;
        }
        return true;
    }

    /**
     * 生成指定用户的新登录态Session，并返回对应的sessionId
     *
     * @param userId                     用户ID
     * @param enumTenantDomainDTO        租户缓存命名空间
     * @param deviceId                   设备标识
     * @param expireSeconds              Session内容过期时间，单位：秒
     * @return string 新生成Session的id标识
     */

    public String createSession(Long userId, EnumTenantSessionDomainDTO enumTenantDomainDTO,
                                String deviceId, int expireSeconds) {
        if (userId == null || userId <= 0) {
            log.warn("userId invalid, userId={}", userId);
            return null;
        }
        if (deviceId == null) {
            log.warn("deviceId is null! userId={}, deviceId={}", userId, deviceId);
            return null;
        }
        String nsSuffix = enumTenantDomainDTO.getSuffix();
        String newSessionId = generateNewSessionId(enumTenantDomainDTO);

        String userIdStr = String.valueOf(userId);
        try {
            // 查询用户对应的所有Session状态
            List<String> sessionIds = cacheService.lrange(userIdStr, 0L, -1L);

            if (!CollectionUtils.isEmpty(sessionIds)) {
                int existSessionCount = 0;
                for (String sessionId : sessionIds) {

                    // 同一个NameSpace，保持Session状态的数量不能超过max
                    if (StringUtils.equals(StringUtils.substring(sessionId, 32), nsSuffix)) {
                        existSessionCount++;

                        // 如果超过max，则删除掉最老的Session
                        if (existSessionCount >= enumTenantDomainDTO.getMaxShare()) {
                            cacheService.del(sessionId);
                            cacheService.lrem(userIdStr, 1L, sessionId);
                        }
                    }
                }
            }

            // 将sessionId存入userId对应的列表 userId =>
            // sessionId1->sessionId2->...->sessionIdN
            cacheService.lpushex(UserConstants.DEFAULT_SESSION_EXPIRE_TIME, userIdStr, new String[]{newSessionId});

            // 将SessionBlock的内容保存到Redis，设置失效时间
            Map<String, String> sessionBlock = new HashMap<>(3);
            sessionBlock.put(UserConstants.SESSION_BLOCK_KEY_USER_ID, userIdStr);
            sessionBlock.put(UserConstants.SESSION_BLOCK_KEY_DEVICE_ID, SecurityUtil.md5(deviceId));
            sessionBlock.put(UserConstants.SESSION_BLOCK_KEY_SESSION_CREATE_TIME,
                    String.valueOf(CommonUtil.getCurrentSeconds()));
            sessionBlock.put(UserConstants.SESSION_BLOCK_KEY_TENANT_ID, String.valueOf(enumTenantDomainDTO.getTenantId()));
            cacheService.hmsetex(UserConstants.DEFAULT_SESSION_EXPIRE_TIME, newSessionId, sessionBlock);

            return newSessionId;
        } catch (Exception e) {
            log.error("create session failed, userId={},enumTenantDomainDTO={}, deviceId={}, expireSeconds={}",
                    userId, enumTenantDomainDTO, deviceId, expireSeconds, e);
        }
        return null;
    }

    /**
     * 生成指定用户的新登录态Session，并返回对应的sessionId
     * <p>
     * 说明: 缓存超时采用默认值
     *
     * @param userId                        用户ID
     * @param enumTenantSessionDomainDTO    租户缓存命名空间
     * @param deviceId                      设备标识
     * @return string 新生成Session的标识
     */

    public String createSession(Long userId, EnumTenantSessionDomainDTO enumTenantSessionDomainDTO, String deviceId) {
        return createSession(userId, enumTenantSessionDomainDTO, deviceId, UserConstants.DEFAULT_SESSION_EXPIRE_TIME);
    }

    /**
     * 验证指定的Session是否有效
     *
     * @param userId                        用户ID
     * @param sessionId
     * @param deviceId                      设备标识
     * @param enumTenantSessionDomainDTO    租户缓存命名空间
     * @return boolean 是否验证通过，true-Session验证有效；false-Session验证无效
     */

    public boolean checkSession(Long userId, String sessionId, String deviceId, EnumTenantSessionDomainDTO enumTenantSessionDomainDTO) {
        if (userId <= 0 || StringUtils.isEmpty(sessionId)) {
            return false;
        }
        // 业务域判定
        String suffix = StringUtils.substring(sessionId, 32);
        EnumTenantSessionDomainDTO namespace = enumTenantDomainLocalCacheImpl.getTenentSessionDomainBySuffix(suffix);
        // 租户id判断
        if (namespace != null && !namespace.getTenantId().equals(enumTenantSessionDomainDTO.getTenantId())) {
                log.warn(
                    "[SessionService.isValidSession][namespace check failure][userId={},sessionId={},deviceId={}]", userId,
                    sessionId, deviceId);
            return false;
        }

        try {
            Map<String, String> sessionBlocks = cacheService.hmgetMap(sessionId,
                    UserConstants.SESSION_BLOCK_KEY_USER_ID, UserConstants.SESSION_BLOCK_KEY_SESSION_CREATE_TIME,
                    UserConstants.SESSION_BLOCK_KEY_DEVICE_ID);
            if (null == sessionBlocks) {
                // redis 异常，默认返回验证成功,redis操作异常自动降级
                log.warn(
                        "[SessionService.isValidSession][redis get failure][userId={},sessionId={},deviceId={}]",
                        userId, sessionId, deviceId);
                return true;
            }

            // 判断UserId是否相同
            String sessionUserId = sessionBlocks.get(UserConstants.SESSION_BLOCK_KEY_USER_ID);
            if (!StringUtils.equals(String.valueOf(userId), sessionUserId)) {
                log.error("SessionService.CheckSession expectUser={}, actualUser={}", userId,
                        sessionUserId);
                return false;
            }
            // 判断deviceId是否相同
            String sessionDeviceId = sessionBlocks.get(UserConstants.SESSION_BLOCK_KEY_DEVICE_ID);
            if (namespace.isCheckDid() && !StringUtils.equals(SecurityUtil.md5(deviceId), sessionDeviceId)) {
                log.error("SessionService.CheckSession expectDeviceId={}, acturalDeviceId={}", deviceId,
                        sessionDeviceId);
                return false;
            }
        } catch (Exception e) {
            log.error("check session failed, userId={}, sessionId={},deviceId={}, domain={}",
                    userId, sessionId, deviceId, enumTenantSessionDomainDTO.getDomain());
        }
        return true;
    }

    /**
     * 查询用户的所有Session记录
     *
     * @param userId
     * @return map map内容为session block
     */

    public List<Map<String, String>> querySessionsByUserId(Long userId) {

        // 查询用户对应的所有Session状态
        List<String> sessionIds = cacheService.lrange(String.valueOf(userId), 0L, -1L);

        if (CollectionUtils.isEmpty(sessionIds)) {
            return Collections.EMPTY_LIST;
        }
        int sessionSize = sessionIds.size();
        // 避免异常情况循环查询Redis，超过最大查询个数时则返回最大条数session记录
        if (sessionSize > UserConstants.QUERY_SESSION_MAX_SIZE) {
            log.info("User holds too more sessions, userId={}, size={}", userId, sessionSize);
            sessionSize = UserConstants.QUERY_SESSION_MAX_SIZE;
        }
        List<Map<String, String>> list = new ArrayList<Map<String, String>>(sessionSize);
        for (int i = 0; i < sessionSize; i++) {
            String sessionId = sessionIds.get(i);
            // 同一个NameSpace，保持Session状态的数量不能超过max
            String namespace = StringUtils.substring(sessionId, 32);
            Map<String, String> sessionBlocks = cacheService.hmgetMap(sessionId,
                    UserConstants.SESSION_BLOCK_KEY_USER_ID, UserConstants.SESSION_BLOCK_KEY_SESSION_CREATE_TIME,
                    UserConstants.SESSION_BLOCK_KEY_DEVICE_ID);
            if (CollectionUtils.isEmpty(sessionBlocks)) {
                continue;
            }
            long ttl = cacheService.ttl(sessionId);
            Map<String, String> resultMap = new HashMap<String, String>();
            resultMap.putAll(sessionBlocks);
            resultMap.put("namespace", namespace);
            resultMap.put("sessionId", sessionId);
            resultMap.put("ttl", String.valueOf(ttl));
            list.add(resultMap);
        }
        return list;
    }

}
