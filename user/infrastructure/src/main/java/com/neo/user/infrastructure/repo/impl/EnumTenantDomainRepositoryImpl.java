package com.neo.user.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.user.domain.entity.EnumTenantDomain;
import com.neo.user.domain.gateway.IEnumTenantDomainRepository;
import com.neo.user.infrastructure.mapper.EnumTenantDomainMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @apiNote EnumTenantDomain表
*/
@Service
public class EnumTenantDomainRepositoryImpl extends ServiceImpl<EnumTenantDomainMapper, EnumTenantDomain>
    implements IEnumTenantDomainRepository {

}
