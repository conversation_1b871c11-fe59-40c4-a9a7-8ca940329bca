package com.neo.user.infrastructure.gateway.local.cache;

import com.neo.user.client.userinfo.dto.EnumThirdPartyAppInfoDTO;
import com.neo.user.domain.entity.EnumThirdPartyAppInfo;
import com.neo.user.domain.gateway.IEnumThirdPartyAppInfoRepository;
import com.neo.user.domain.gateway.cache.EnumThirdPartyAppInfoCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("enumThirdPartyAppInfoLocalCacheImpl")
public class EnumThirdPartyAppInfoLocalCacheImpl implements EnumThirdPartyAppInfoCacheService {


    private Map<String, List<EnumThirdPartyAppInfoDTO>> appIdMap = new HashMap<>();

    @Autowired
    private IEnumThirdPartyAppInfoRepository iEnumThirdPartyAppInfoRepository;

    @Override
    public EnumThirdPartyAppInfoDTO getByAppIdAndThirdPlatform(String appId, String thridPlatform) {
        List<EnumThirdPartyAppInfoDTO> enumThirdPartyAppInfoDTOS = appIdMap.get(appId);
        if (!CollectionUtils.isEmpty(enumThirdPartyAppInfoDTOS)){
            for (EnumThirdPartyAppInfoDTO dto:enumThirdPartyAppInfoDTOS){
                if (dto.getThirdPlatform().equals(thridPlatform)){
                    return dto;
                }
            }
        }
        return null;
    }

    @Scheduled(fixedRate = 60000)
    public synchronized void loadSessionConfig() {
//        log.info("【三方appId】start config job at {}", System.currentTimeMillis());
        try {
            List<EnumThirdPartyAppInfo> list = iEnumThirdPartyAppInfoRepository.list();
            if (!CollectionUtils.isEmpty(list)) {
                list.stream().forEach(item -> {
                    List<EnumThirdPartyAppInfoDTO> tmpList = appIdMap.getOrDefault(item.getAppId(), new ArrayList<EnumThirdPartyAppInfoDTO>());
                    EnumThirdPartyAppInfoDTO enumThirdPartyAppInfoDTO = new EnumThirdPartyAppInfoDTO();
                    enumThirdPartyAppInfoDTO.setAppId(item.getAppId());
                    enumThirdPartyAppInfoDTO.setThirdPlatform(item.getThirdPlatform());
                    enumThirdPartyAppInfoDTO.setAppKey(item.getAppKey());
                    enumThirdPartyAppInfoDTO.setAppSecret(item.getAppSecret());
                    tmpList.add(enumThirdPartyAppInfoDTO);
                    appIdMap.put(item.getAppId(), tmpList);
                });
//            } else {
//                log.error("【三方appId】loadSessionConfig fail empty config");
            }
        } catch (Exception e) {
            log.error("【三方appId】loadConfig fail exception:", e);
        }
//        log.info("【三方appId】end config job at {}", System.currentTimeMillis());
    }
}
