package com.neo.user.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.user.domain.entity.User;
import com.neo.user.domain.gateway.IUserRepository;
import com.neo.user.infrastructure.mapper.UserMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @apiNote 用户表
* @since 2024-01-03 15:35:04
*/
@Service
public class UserRepositoryImpl extends ServiceImpl<UserMapper,User>
    implements IUserRepository{

}