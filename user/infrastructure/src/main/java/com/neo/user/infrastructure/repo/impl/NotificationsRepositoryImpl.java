package com.neo.user.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.user.domain.entity.Notifications;
import com.neo.user.domain.gateway.INotificationsRepository;
import com.neo.user.infrastructure.mapper.NotificationsMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @apiNote 消息通知表
 * @since 2024-07-16 12:25:02
 */
@Service
public class NotificationsRepositoryImpl extends ServiceImpl<NotificationsMapper, Notifications>
        implements INotificationsRepository {

}