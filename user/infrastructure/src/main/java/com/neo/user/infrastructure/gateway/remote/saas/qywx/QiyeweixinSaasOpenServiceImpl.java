package com.neo.user.infrastructure.gateway.remote.saas.qywx;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class QiyeweixinSaasOpenServiceImpl {

    /**
     * 获取第三方应用凭证（ suiteTicket 换 suite_access_token ）
     * https://developer.work.weixin.qq.com/document/path/90600
     */
    public String getSuiteAccessToken(String suiteId, String suiteSecret, String suiteTicket){
        RestTemplate restTemplate = new RestTemplate();
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_suite_token";

        Map<String, Object> params = new HashMap<>();
        params.put("suite_id", suiteId);
        params.put("suite_secret", suiteSecret);
        params.put("suite_ticket", suiteTicket);
        ResponseEntity<String> response = restTemplate.postForEntity(url, params, String.class);
        if (response.getStatusCode().value() ==200){
            String res = response.getBody();
            JSONObject jsonObject = JSONObject.parseObject(res);
            if (!jsonObject.containsKey("errcode")){//注意：因历史原因，该接口在调用失败时才返回errcode。没返回errcode视为调用成功
                log.info("【企业微信Saas】getSuiteAccessToken res：" + res);
                return jsonObject.getString("suite_access_token");
            }else {
                log.error("【企业微信Saas】getSuiteAccessToken error：" + res);
            }
        }
        return null;
    }

    /**
     * 获取预授权码
     * https://developer.work.weixin.qq.com/document/path/90601
     * @param suiteAccessToken
     * @return
     */
    public String getPreAuthCode(String suiteAccessToken){
        RestTemplate restTemplate = new RestTemplate();
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_pre_auth_code?suite_access_token=" + suiteAccessToken;
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        if (response.getStatusCode().value() ==200){
            String res = response.getBody();
            JSONObject jsonObject = JSONObject.parseObject(res);
            if (jsonObject.getInteger("errcode") == 0 ){
                log.info("【企业微信Saas】/cgi-bin/service/get_pre_auth_code res：" + res);
                return jsonObject.getString("pre_auth_code");
            } else {
                log.error("【企业微信Saas】/cgi-bin/service/get_pre_auth_code error：" + res);
            }
        }
        return null;

    }

    /**
     * 获取企业永久授权码
     * https://developer.work.weixin.qq.com/document/path/90603
     */
    public String getTenantPermanentCode(String suiteAccessToken, String authCode){

        RestTemplate restTemplate = new RestTemplate();
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_permanent_code?suite_access_token=" + suiteAccessToken;
        Map<String, Object> params = new HashMap<>();
        params.put("auth_code", authCode);
        ResponseEntity<String> response = restTemplate.postForEntity(url, params, String.class);

        if (response.getStatusCode().value() ==200){
            String res = response.getBody();
            JSONObject jsonObject = JSONObject.parseObject(res);
            if (!jsonObject.containsKey("errcode")){//注意：因历史原因，该接口在调用失败时才返回errcode。没返回errcode视为调用成功
                log.info("【企业微信Saas】get_permanent_code res：" + res);
                return res;
            }else {
                log.error("【企业微信Saas】get_permanent_code error：" + res);
            }
        }
        return null;
    }

    /**
     * 获取企业凭证
     * 第三方服务商在取得企业的永久授权码后，通过此接口可以获取到企业的access_token
     * https://developer.work.weixin.qq.com/document/path/90605
     * @param suiteAccessToken
     * @param corpId
     * @param permanentCode
     * @return
     */
    public String getCorpToken(String suiteAccessToken, String corpId, String permanentCode){
        RestTemplate restTemplate = new RestTemplate();
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_corp_token?suite_access_token=" + suiteAccessToken;
        Map<String, Object> params = new HashMap<>();
        params.put("auth_corpid", corpId);
        params.put("permanent_code", permanentCode);
        ResponseEntity<String> response = restTemplate.postForEntity(url, params, String.class);
        if (response.getStatusCode().value() ==200){
            String res = response.getBody();
            JSONObject jsonObject = JSONObject.parseObject(res);
            if (!jsonObject.containsKey("errcode")){//注意：因历史原因，该接口在调用失败时才返回errcode。没返回errcode视为调用成功
                log.info("【企业微信Saas】get_corp_token res：" + res);
                return jsonObject.getString("access_token");
            }else {
                log.error("【企业微信Saas】get_corp_token error：" + res);
            }
        }
        return null;
    }

    /**
     * https://developer.work.weixin.qq.com/document/path/90332
     * 通讯录管理-读取成员
     * @param corpToken
     * @param userId
     * @return
     *
     * {
     *     "errcode": 0,
     *     "errmsg": "ok",
     *     "userid": "wenxi",
     *     "department": [
     *         1
     *     ],
     *     "gender": "0",
     *     "avatar": "https://rescdn.qqmail.com/node/wwmng/wwmng/style/images/independent/DefaultAvatar$73ba92b5.png",
     *     "status": 1,
     *     "order": [],
     *     "main_department": 1,
     *     "is_leader_in_dept": [],
     *     "thumb_avatar": "https://rescdn.qqmail.com/node/wwmng/wwmng/style/images/independent/DefaultAvatar$73ba92b5.png",
     *     "open_userid": "wo-s8YEAAA-lkuxxK7yjpRggy3MrS5sQ",
     *     "direct_leader": [],
     *     "name": "wenxi"
     * }
     *
     */
    public String getUserInfo(String corpToken, String userId){
        RestTemplate restTemplate = new RestTemplate();
        String url = "https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=" + corpToken + "&userid=" + userId;
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        if (response.getStatusCode().value() ==200){
            String res = response.getBody();
            JSONObject jsonObject = JSONObject.parseObject(res);
            if (jsonObject.getInteger("errcode") == 0 ){
                log.info("【企业微信Saas】/cgi-bin/user/get res：" + res);
                return res;
            } else if (jsonObject.getInteger("errcode") == 60011 ){
                log.error("【企业微信Saas】" + url + " error：" + res);
            } else {
                log.error("【企业微信Saas】/cgi-bin/user/get error：" + res);
            }
        }
        return null;
    }

    /**
     * https://developer.work.weixin.qq.com/document/path/91121
     * 获取访问用户身份
     * @param suiteAccessToken
     * @param authCode
     * @return
     */
    public String getuserinfo3rd(String suiteAccessToken , String authCode){
        RestTemplate restTemplate = new RestTemplate();
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/auth/getuserinfo3rd?suite_access_token=" + suiteAccessToken + "&code=" + authCode;
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        if (response.getStatusCode().value() ==200){
            String res = response.getBody();
            JSONObject jsonObject = JSONObject.parseObject(res);
            if (jsonObject.getInteger("errcode") == 0 ){
                log.info("【企业微信Saas】/cgi-bin/service/auth/getuserinfo3rd res：" + res);
                return res;
            } else {
                log.error("【企业微信Saas】/cgi-bin/service/auth/getuserinfo3rd error：" + res);
            }
        }
        return null;
    }

    /**
     * https://developer.work.weixin.qq.com/document/path/92423
     * 第三方应用开发>客户端API>小程序>登录>code2Session
     * @param suiteAccessToken
     * @param authCode
     * @return
     */
    public String jscode2session(String suiteAccessToken, String authCode){
        RestTemplate restTemplate = new RestTemplate();
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/miniprogram/jscode2session?suite_access_token=" + suiteAccessToken + "&js_code=" + authCode + "&grant_type=authorization_code";
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        if (response.getStatusCode().value() ==200){
            String res = response.getBody();
            JSONObject jsonObject = JSONObject.parseObject(res);
            if (jsonObject.getInteger("errcode") == 0 ){
                log.info("【企业微信Saas】/cgi-bin/service/miniprogram/jscode2session res：" + res);
                return res;
            } else {
                log.error("【企业微信Saas】/cgi-bin/service/miniprogram/jscode2session error：" + res);
            }
        }
        return null;
    }

    /**
     * 获取访问用户敏感信息
     * https://developer.work.weixin.qq.com/document/path/91122
     * @param suiteAccessToken
     * @param userTicket
     * @return
     */
    public String getuserdetail3rd(String suiteAccessToken , String userTicket){
        RestTemplate restTemplate = new RestTemplate();
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/auth/getuserdetail3rd?suite_access_token=" + suiteAccessToken;
        Map<String, Object> params = new HashMap<>();
        params.put("user_ticket", userTicket);
        ResponseEntity<String> response = restTemplate.postForEntity(url, params, String.class);
        if (response.getStatusCode().value() ==200){
            String res = response.getBody();
            JSONObject jsonObject = JSONObject.parseObject(res);
            if (jsonObject.getInteger("errcode") == 0 ){
                log.info("【企业微信Saas】getuserdetail3rd res：" + res);
                return res;
            }else {
                log.error("【企业微信Saas】getuserdetail3rd error：" + res);
            }
        }
        return null;

    }

    /**
     * 获取子部门ID列表
     * https://developer.work.weixin.qq.com/document/path/95406
     * @param corpAccessToken
     * @param deptId
     * @return
     */
    public List<Map<String,Object>> getDepartmentSimpleList(String corpAccessToken, Long deptId){
        RestTemplate restTemplate = new RestTemplate();
        String url = "https://qyapi.weixin.qq.com/cgi-bin/department/simplelist?access_token=" + corpAccessToken + "&id=" + deptId!=null?deptId.toString():"";
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        if (response.getStatusCode().value() ==200){
            String res = response.getBody();
            JSONObject jsonObject = JSONObject.parseObject(res);
            if (jsonObject.getInteger("errcode") == 0 ){
                log.info("【企业微信Saas】/cgi-bin/department/simplelist res：" + res);
                return jsonObject.getObject("department_id", new TypeReference<List<Map<String,Object>>>(){});
            } else {
                log.error("【企业微信Saas】/cgi-bin/department/simplelist error：" + res);
            }
        }
        return null;
    }

    /**
     * 获取打卡记录数据
     * https://developer.work.weixin.qq.com/document/path/94205
     * @param corpAccessToken
     * @param useridlist
     * @param startTime
     * @param endTime
     * @return
     */
    public List<Map<String,Object>> getCheckinData(String corpAccessToken, List<String> useridlist, Long startTime, Long endTime) {
        RestTemplate restTemplate = new RestTemplate();
        String url = "https://qyapi.weixin.qq.com/cgi-bin/checkin/getcheckindata?access_token=" + corpAccessToken;
        Map<String, Object> params = new HashMap<>();
        params.put("opencheckindatatype", 3);
        params.put("useridlist", useridlist);
        params.put("starttime", startTime);
        params.put("endtime", endTime);
        ResponseEntity<String> response = restTemplate.postForEntity(url, params, String.class);
        if (response.getStatusCode().value() ==200){
            String res = response.getBody();
            JSONObject jsonObject = JSONObject.parseObject(res);
            if (jsonObject.getInteger("errcode") == 0 ){
                log.info("【企业微信Saas】getcheckindata res：" + res);
                return jsonObject.getObject("checkindata", new TypeReference<List<Map<String,Object>>>(){});
            }else {
                log.error("【企业微信Saas】getcheckindata error：" + res);
            }
        }
        return null;
    }


}
