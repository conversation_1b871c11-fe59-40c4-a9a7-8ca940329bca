package com.neo.user.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.user.domain.entity.EnumTenantDomain;
import com.neo.user.domain.entity.Tenant;
import com.neo.user.domain.gateway.IEnumTenantDomainRepository;
import com.neo.user.domain.gateway.ITenantRepository;
import com.neo.user.infrastructure.mapper.EnumTenantDomainMapper;
import com.neo.user.infrastructure.mapper.TenantMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @apiNote Tenant表
*/
@Service
public class TenantRepositoryImpl extends ServiceImpl<TenantMapper, Tenant>
    implements ITenantRepository {

}
