package com.neo.user.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.user.domain.entity.UserNotifications;
import com.neo.user.domain.gateway.IUserNotificationsRepository;
import com.neo.user.infrastructure.mapper.UserNotificationsMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @apiNote 用户通知关联表
* @since 2024-07-16 12:26:38
*/
@Service
public class UserNotificationsRepositoryImpl extends ServiceImpl
<UserNotificationsMapper,UserNotifications>
implements IUserNotificationsRepository{

}
