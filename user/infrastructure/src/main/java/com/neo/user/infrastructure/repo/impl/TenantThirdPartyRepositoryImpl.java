package com.neo.user.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.user.domain.entity.TenantThirdParty;
import com.neo.user.domain.gateway.ITenantThirdPartyRepository;
import com.neo.user.infrastructure.mapper.TenantThirdPartyMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
*/
@Service
public class TenantThirdPartyRepositoryImpl extends ServiceImpl<TenantThirdPartyMapper, TenantThirdParty>
    implements ITenantThirdPartyRepository {

}
