package com.neo.user.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.user.domain.entity.EnumThirdPartyAppInfo;
import com.neo.user.domain.gateway.IEnumThirdPartyAppInfoRepository;
import com.neo.user.infrastructure.mapper.EnumThirdPartyAppInfoMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @apiNote EnumThirdPartyAppInfo表
*/
@Service
public class EnumThirdPartyAppInfoRepositoryImpl extends ServiceImpl<EnumThirdPartyAppInfoMapper, EnumThirdPartyAppInfo>
    implements IEnumThirdPartyAppInfoRepository {

}
