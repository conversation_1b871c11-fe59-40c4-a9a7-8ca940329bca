package com.neo.user.infrastructure.gateway.remote.auth;

import com.neo.api.SingleResponse;
import com.neo.auth.client.param.CopyAllDto;
import com.neo.auth.client.rpc.PlatformService;
import com.neo.user.domain.gateway.auth.IUserAuthRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class UserAuthRepositoryImpl implements IUserAuthRepository {

    @Autowired
    private PlatformService platformService;

    @Override
    public SingleResponse<Boolean> copyAuthTemplate(Long newTenantId, Long newAdminUserId, Long templateTenantId, Long templatePlatformId ) {
        CopyAllDto dto = new CopyAllDto();
        dto.setSourcePlatformId(templatePlatformId);
        dto.setSourceTenantId(templateTenantId);
        dto.setTargetTenantId(newTenantId);
        dto.setTargetUserId(newAdminUserId);
        return platformService.copyAll(dto);
    }
}
