package com.neo.user.infrastructure.gateway.local.cache;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.neo.user.client.tenant.dto.EnumSessionDomainDTO;
import com.neo.user.client.tenant.dto.EnumTenantDomainDTO;
import com.neo.user.client.tenant.dto.EnumTenantSessionDomainDTO;
import com.neo.user.domain.entity.EnumAppDomainAuth;
import com.neo.user.domain.entity.EnumSessionDomain;
import com.neo.user.domain.entity.EnumTenantDomain;
import com.neo.user.domain.gateway.IEnumAppDomainAuthRepository;
import com.neo.user.domain.gateway.IEnumSessionDomainRepository;
import com.neo.user.domain.gateway.IEnumTenantDomainRepository;
import com.neo.user.domain.gateway.cache.EnumTenantDomainCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("enumTenantDomainLocalCacheImpl")
public class EnumTenantDomainLocalCacheImpl implements EnumTenantDomainCacheService {

    private Map<String, EnumTenantDomain> domainTenantMap = new HashMap<>();
    private Map<Long, EnumTenantDomain> idTenantMap = new HashMap<>();
    private Map<String, EnumSessionDomain> suffixSessionMap = new HashMap<>();
    private Map<String, EnumSessionDomain> domainSessionMap = new HashMap<>();

    @Autowired
    IEnumTenantDomainRepository iEnumTenantDomainRepository;

    @Autowired
    IEnumSessionDomainRepository iEnumSessionDomainRepository;

    @Autowired
    IEnumAppDomainAuthRepository iEnumAppDomainAuthRepository;

    @Scheduled(fixedRate = 60000)
    public synchronized void loadSessionConfig() {
//        log.info("【租户】start config job at {}", System.currentTimeMillis());
        try {
            List<EnumTenantDomain> list = iEnumTenantDomainRepository.list();
            if (!CollectionUtils.isEmpty(list)) {
                Map<String, EnumTenantDomain> newDomainTenantMap = new HashMap<>();
                Map<Long, EnumTenantDomain> newIdTenantMap =  new HashMap<>();
                for (EnumTenantDomain domain : list) {
                    newDomainTenantMap.put(domain.getDomain(), domain);
                    newIdTenantMap.put(domain.getTenantId(), domain);
                }
                domainTenantMap.keySet().removeIf(key -> {
                    if (!newDomainTenantMap.containsKey(key)) {
                        log.info("【租户】删除domainTenantMap缓存 domain: {}", key);
                        return true; // 删除元素
                    }
                    return false; // 保留元素
                });
                idTenantMap.keySet().removeIf(key -> {
                    if (!newIdTenantMap.containsKey(key)) {
                        log.info("【租户】删除idTenantMap缓存 tenantId: {}", key);
                        return true; // 删除元素
                    }
                    return false; // 保留元素
                });

                for (Map.Entry<String, EnumTenantDomain> entry : newDomainTenantMap.entrySet()) {
                    EnumTenantDomain existingDomain = domainTenantMap.get(entry.getKey());
                    if (existingDomain == null) {
                        log.info("【租户】domainTenantMap 新增租户 domain: {}", entry.getKey());
                    } else if (!existingDomain.equals(entry.getValue())) {
                        log.info("【租户】domainTenantMap 更新租户 domain: {}", entry.getKey());
                    }
                    domainTenantMap.put(entry.getKey(), entry.getValue());
                }

                for (Map.Entry<Long, EnumTenantDomain> entry : newIdTenantMap.entrySet()) {
                    EnumTenantDomain existingId = idTenantMap.get(entry.getKey());
                    if (existingId == null) {
                        log.info("【租户】idTenantMap 新增租户 tenantId: {}", entry.getKey());
                    } else if (!existingId.equals(entry.getValue())) {
                        log.info("【租户】idTenantMap 更新租户 tenantId: {}", entry.getKey());
                    }
                    idTenantMap.put(entry.getKey(), entry.getValue());
                }
            } else if (!domainTenantMap.isEmpty() || !idTenantMap.isEmpty()) {
                domainTenantMap = new HashMap<>();
                log.info("【租户】清空 domainTenantMap");
                idTenantMap = new HashMap<>();
                log.info("【租户】清空 idTenantMap");
            }


            List<EnumSessionDomain> list1 = iEnumSessionDomainRepository.list();
            if (!CollectionUtils.isEmpty(list1)){
                Map<String, EnumSessionDomain> newSuffixSessionMap = new HashMap<>();
                Map<String, EnumSessionDomain> newDomainSessionMap =  new HashMap<>();
                for (EnumSessionDomain domain : list1) {
                    newSuffixSessionMap.put(domain.getSuffix(), domain);
                    newDomainSessionMap.put(domain.getDomain(), domain);
                }
                suffixSessionMap.keySet().removeIf(key -> {
                    if (!newSuffixSessionMap.containsKey(key)) {
                        log.info("【租户】删除suffixSessionMap缓存 suffix: {}", key);
                        return true; // 删除元素
                    }
                    return false; // 保留元素
                });
                domainSessionMap.keySet().removeIf(key -> {
                    if (!newDomainSessionMap.containsKey(key)) {
                        log.info("【租户】删除domainSessionMap缓存 domain: {}", key);
                        return true; // 删除元素
                    }
                    return false; // 保留元素
                });

                for (Map.Entry<String, EnumSessionDomain> entry : newSuffixSessionMap.entrySet()) {
                    EnumSessionDomain existingDomain = suffixSessionMap.get(entry.getKey());
                    if (existingDomain == null) {
                        log.info("【租户】suffixSessionMap 新增租户 suffix: {}", entry.getKey());
                    } else if (!existingDomain.equals(entry.getValue())) {
                        log.info("【租户】suffixSessionMap 更新租户 suffix: {}", entry.getKey());
                    }
                    suffixSessionMap.put(entry.getKey(), entry.getValue());
                }

                for (Map.Entry<String, EnumSessionDomain> entry : newDomainSessionMap.entrySet()) {
                    EnumSessionDomain existingDomain = domainSessionMap.get(entry.getKey());
                    if (existingDomain == null) {
                        log.info("【租户】domainSessionMap 新增租户 domain: {}", entry.getKey());
                    } else if (!existingDomain.equals(entry.getValue())) {
                        log.info("【租户】domainSessionMap 更新租户 domain: {}", entry.getKey());
                    }
                    domainSessionMap.put(entry.getKey(), entry.getValue());
                }


                for (EnumSessionDomain enumSessionDomain : list1){
                    suffixSessionMap.put(enumSessionDomain.getSuffix(), enumSessionDomain);
                    domainSessionMap.put(enumSessionDomain.getDomain(), enumSessionDomain);
                }
            } else if (!suffixSessionMap.isEmpty() || !domainSessionMap.isEmpty()){
                suffixSessionMap = new HashMap<>();
                log.info("【租户】清空 suffixSessionMap");
                domainSessionMap = new HashMap<>();
                log.info("【租户】清空 domainSessionMap");
            }

        } catch (Exception e) {
            log.error("【租户】loadConfig fail exception:", e);
        }
//        log.info("【租户】end config job at {}", System.currentTimeMillis());
    }

    @Override
    public Boolean refreshAll() {
        loadSessionConfig();
        return true;
    }

    @Override
    public EnumTenantSessionDomainDTO getSessionDomainByDomain(String domain) {
        EnumTenantDomain enumTenantDomain = domainTenantMap.get(domain);
        EnumSessionDomain enumSessionDomain = domainSessionMap.get(domain);
        if (enumTenantDomain == null || enumSessionDomain == null) {
            return null;
        }
        return doToToEnumTenantSessionDomainDTO(enumSessionDomain, enumTenantDomain);
    }

    @Override
    public EnumTenantSessionDomainDTO getTenantDomainById(Long id) {
        EnumTenantDomain enumTenantDomain = idTenantMap.get(id);
        if (enumTenantDomain == null) {
            return null;
        }
        EnumSessionDomain enumSessionDomain = domainSessionMap.get(enumTenantDomain.getDomain());
        return doToToEnumTenantSessionDomainDTO(enumSessionDomain, enumTenantDomain);
    }

    @Override
    public EnumSessionDomainDTO getSessionDomainBySuffix(String suffix) {
        EnumSessionDomain enumSessionDomain = suffixSessionMap.get(suffix);
        return doToToEnumSessionDomainDTO(enumSessionDomain);
    }

    @Override
    public EnumTenantSessionDomainDTO getTenentSessionDomainBySuffix(String suffix) {
        EnumSessionDomain enumSessionDomain = suffixSessionMap.get(suffix);
        EnumTenantDomain enumTenantDomain = domainTenantMap.get(enumSessionDomain.getDomain());
        return doToToEnumTenantSessionDomainDTO(enumSessionDomain, enumTenantDomain);
    }

    @Override
    public EnumTenantDomainDTO getTenantDomainByTenantIdAndAppId(Long tenantId, String appId) {
        EnumTenantSessionDomainDTO enumTenantSessionDomainDTO = getTenantDomainById(tenantId);
        if (enumTenantSessionDomainDTO == null){
            return null;
        }
        LambdaQueryWrapper<EnumAppDomainAuth> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnumAppDomainAuth::getAppId, appId);
        wrapper.eq(EnumAppDomainAuth::getTenantId, tenantId);
        EnumAppDomainAuth enumAppDomainAuth = iEnumAppDomainAuthRepository.getOne(wrapper);
        if (enumAppDomainAuth != null){
            EnumTenantDomainDTO enumTenantDomainDTO = new EnumTenantDomainDTO();
            enumTenantDomainDTO.setTenantId(enumTenantSessionDomainDTO.getTenantId());
            enumTenantDomainDTO.setDomain(enumTenantSessionDomainDTO.getDomain());
            enumTenantDomainDTO.setSuffix(enumTenantSessionDomainDTO.getSuffix());
            enumTenantDomainDTO.setSessionDomain(enumTenantSessionDomainDTO.getSessionDomain());
            enumTenantDomainDTO.setMaxShare(enumTenantSessionDomainDTO.getMaxShare());
            enumTenantDomainDTO.setCheckDid(enumTenantSessionDomainDTO.getCheckDid());

            enumTenantDomainDTO.setAppId(appId);
            enumTenantDomainDTO.setAuthApp(enumAppDomainAuth.getAuthApp());
            enumTenantDomainDTO.setAuthSecret(enumAppDomainAuth.getAuthSecret());
            return enumTenantDomainDTO;
        }
        return null;
    }

    @Override
    public EnumTenantDomainDTO getTenantDomainByDomainAndAppId(String domain, String appId) {
        EnumTenantSessionDomainDTO enumTenantSessionDomainDTO = getSessionDomainByDomain(domain);
        if (enumTenantSessionDomainDTO == null){
            return null;
        }
        LambdaQueryWrapper<EnumAppDomainAuth> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnumAppDomainAuth::getAppId, appId);
        wrapper.eq(EnumAppDomainAuth::getTenantId, enumTenantSessionDomainDTO.getTenantId());
        EnumAppDomainAuth enumAppDomainAuth = iEnumAppDomainAuthRepository.getOne(wrapper);
        if (enumAppDomainAuth != null){
            EnumTenantDomainDTO enumTenantDomainDTO = new EnumTenantDomainDTO();
            enumTenantDomainDTO.setTenantId(enumTenantSessionDomainDTO.getTenantId());
            enumTenantDomainDTO.setDomain(enumTenantSessionDomainDTO.getDomain());
            enumTenantDomainDTO.setSuffix(enumTenantSessionDomainDTO.getSuffix());
            enumTenantDomainDTO.setSessionDomain(enumTenantSessionDomainDTO.getSessionDomain());
            enumTenantDomainDTO.setMaxShare(enumTenantSessionDomainDTO.getMaxShare());
            enumTenantDomainDTO.setCheckDid(enumTenantSessionDomainDTO.getCheckDid());

            enumTenantDomainDTO.setAppId(appId);
            enumTenantDomainDTO.setAuthApp(enumAppDomainAuth.getAuthApp());
            enumTenantDomainDTO.setAuthSecret(enumAppDomainAuth.getAuthSecret());
            return enumTenantDomainDTO;
        }
        return null;
    }

    private EnumSessionDomainDTO doToToEnumSessionDomainDTO(EnumSessionDomain enumSessionDomain) {
        if (null == enumSessionDomain) {
            return null;
        }
        EnumSessionDomainDTO enumSessionDomainDTO = new EnumSessionDomainDTO();
        enumSessionDomainDTO.setDomain(enumSessionDomain.getDomain());
        enumSessionDomainDTO.setSuffix(enumSessionDomain.getSuffix());
        enumSessionDomainDTO.setSessionDomain(enumSessionDomain.getSessionDomain());
        enumSessionDomainDTO.setMaxShare(enumSessionDomain.getMaxShare());
        enumSessionDomainDTO.setCheckDid(enumSessionDomain.getCheckDid());
        return enumSessionDomainDTO;
    }

    private EnumTenantSessionDomainDTO doToToEnumTenantSessionDomainDTO(EnumSessionDomain enumSessionDomain, EnumTenantDomain enumTenantDomain) {
        EnumSessionDomainDTO enumSessionDomainDTO = doToToEnumSessionDomainDTO(enumSessionDomain);
        if (enumSessionDomainDTO == null || enumTenantDomain == null){
            return null;
        }
        EnumTenantSessionDomainDTO enumTenantSessionDomainDTO = new EnumTenantSessionDomainDTO();
        enumTenantSessionDomainDTO.setDomain(enumSessionDomain.getDomain());
        enumTenantSessionDomainDTO.setSuffix(enumSessionDomain.getSuffix());
        enumTenantSessionDomainDTO.setSessionDomain(enumSessionDomain.getSessionDomain());
        enumTenantSessionDomainDTO.setMaxShare(enumSessionDomain.getMaxShare());
        enumTenantSessionDomainDTO.setCheckDid(enumSessionDomain.getCheckDid());

        enumTenantSessionDomainDTO.setTenantId(enumTenantDomain.getTenantId());
        return enumTenantSessionDomainDTO;
    }

}
