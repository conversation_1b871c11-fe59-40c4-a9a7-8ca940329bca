package com.neo.user.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.user.domain.entity.EnumSessionDomain;
import com.neo.user.domain.gateway.IEnumSessionDomainRepository;
import com.neo.user.infrastructure.mapper.EnumSessionDomainMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @apiNote EnumSessionDomain表
*/
@Service
public class EnumSessionDomainRepositoryImpl extends ServiceImpl<EnumSessionDomainMapper, EnumSessionDomain>
    implements IEnumSessionDomainRepository {

}
