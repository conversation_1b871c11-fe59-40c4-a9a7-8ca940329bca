package com.neo.user.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.user.domain.entity.EnumAppDomainAuth;
import com.neo.user.domain.gateway.IEnumAppDomainAuthRepository;
import com.neo.user.infrastructure.mapper.EnumAppDomainAuthMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @apiNote EnumAppDomainAuth表
*/
@Service
public class EnumAppDomainAuthRepositoryImpl extends ServiceImpl<EnumAppDomainAuthMapper, EnumAppDomainAuth>
    implements IEnumAppDomainAuthRepository {

}
