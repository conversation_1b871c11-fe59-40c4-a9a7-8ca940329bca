<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.neo</groupId>
        <artifactId>user</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>user-infrastructure</artifactId>
    <name>user-infrastructure</name>

    <dependencies>

        <!--  环境引入   -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sendgrid</groupId>
            <artifactId>sendgrid-java</artifactId>
        </dependency>


        <!--  通用      -->
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-common</artifactId>
        </dependency>

        <!--  notifyParam 仅允许引用domain      -->
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>user-domain</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-cache</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>tagcenter-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>auth-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.neo</groupId>
                    <artifactId>neo-session-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


    </dependencies>
</project>
