package com.neo.user.domain.gateway.department;

import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.user.domain.gateway.department.dto.DepartmentDTO;

import java.util.List;
import java.util.Map;


public interface IDepartmentRepository {

    public MultiResponse<DepartmentDTO> getAllDepartment(Long tenantId);

    public MultiResponse<Long> getAllSubDeptIds(Long tenantId, Long deptId);

    public SingleResponse<Map<Long, String>> queryDepartmentNameMap(Long tenantId, List<Long> deptIds);

    public SingleResponse<DepartmentDTO> get(Long tenantId, Long deptId, Boolean includeDeleted);

    public SingleResponse<Boolean> init(Long tenantId, String domain);

    public SingleResponse<Boolean> add(DepartmentDTO departmentDTO);

    public SingleResponse<Boolean> update(DepartmentDTO departmentDTO);

    public SingleResponse<Boolean> delete(Long tenantId, Long deptId);

    public MultiResponse<DepartmentDTO> multiGet(Long tenantId, List<Long> deptIds, Boolean includeDeleted);

    public Integer getDeptPos(Long deptId);

    public MultiResponse<Long> getDeptIdsByExtra(Long tenantId, String extra);

    public SingleResponse<Map<Long, List<Long>>> batchGetSubDeptIds(Long tenantId, List<Long> deptIds);
}
