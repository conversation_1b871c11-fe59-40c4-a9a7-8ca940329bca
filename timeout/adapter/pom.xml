<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.neo</groupId>
        <artifactId>timeout</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>timeout-adapter</artifactId>
    <name>timeout-adapter</name>

    <!-- notify 不允许单独指定版本-->
    <dependencies>
        <!-- notify 此处为通用依赖引入-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- notify 此处为环境引入-->

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>xxljob-client</artifactId>
        </dependency>

        <!-- notify 仅允许引app和client-->
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>timeout-app</artifactId>
        </dependency>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>timeout-client</artifactId>
        </dependency>

    </dependencies>
</project>
