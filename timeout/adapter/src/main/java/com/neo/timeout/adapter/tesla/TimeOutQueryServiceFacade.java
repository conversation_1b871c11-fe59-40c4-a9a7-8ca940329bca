package com.neo.timeout.adapter.tesla;

import com.neo.api.MultiResponse;
import com.neo.timeout.app.query.QueryTimeOutService;
import com.neo.timeout.client.api.TimeOutQueryService;
import com.neo.timeout.client.domain.dto.GetTimeOutDetailByBizIdReqDTO;
import com.neo.timeout.client.domain.dto.TimeOutDetailResDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR> on 2017-08-02 18:13.
 */
@Service("timeOutQueryService")
public class TimeOutQueryServiceFacade implements TimeOutQueryService {

    @Autowired
    private QueryTimeOutService queryTimeOutService;

    /**
     * 根据bizId,bizType查询超时任务详细信息
     *
     * @param reqDTO GetTimeOutDetailByBizIdReqDTO
     * @return Response Response<List<TimeOutDetailResDTO>>
     */

    @Override
    public MultiResponse<TimeOutDetailResDTO> getTimeOutDetailByBizId(GetTimeOutDetailByBizIdReqDTO reqDTO) {
        return MultiResponse.of(queryTimeOutService.getTimeOutDetailByBizId(reqDTO));
    }
}
