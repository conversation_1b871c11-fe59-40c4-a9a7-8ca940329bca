package com.neo.timeout.adapter.tesla;

import com.neo.api.SingleResponse;
import com.neo.timeout.app.operate.OperateTimeOutMsgService;
import com.neo.timeout.client.api.TimeOutMsgOperateService;
import com.neo.timeout.client.domain.dto.CancelTimeOutBizMessageReqDTO;
import com.neo.timeout.client.domain.dto.ModifyTimeOutBizMessageReqDTO;
import com.neo.timeout.client.domain.dto.SubscribeTimeOutBizMessageReqDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR> on 2016-07-11 12:17.
 */
@Service("timeOutMsgOperateService")
public class TimeOutMsgOperateServiceFacade implements TimeOutMsgOperateService {

    @Resource
    private OperateTimeOutMsgService operateTimeOutMsgService;

    /**
     * 超时消息订阅API
     *
     * @param subscribeTimeOutBizMessageReqDTO SubscribeTimeOutBizMessageReqDTO
     * @return Response<Boolean> {true: 订阅成功; false: 订阅失败; null: 异常}
     */
    @Override
    public SingleResponse<Boolean> subscribeTimeOutBizMessage(SubscribeTimeOutBizMessageReqDTO subscribeTimeOutBizMessageReqDTO) {
        return SingleResponse.of(operateTimeOutMsgService.subscribeTimeOutBizMessage(subscribeTimeOutBizMessageReqDTO));
    }

    /**
     * 修改超时消息API
     *
     * @param modifyTimeOutBizMessageReqDTO ModifyTimeOutBizMessageReqDTO
     * @return Response<Boolean> {true: 修改成功; false: 修改失败; null: 异常}
     */
    @Override
    public SingleResponse<Boolean> modifyTimeOutBizMessage(ModifyTimeOutBizMessageReqDTO modifyTimeOutBizMessageReqDTO) {
        return SingleResponse.of(operateTimeOutMsgService.modifyTimeOutBizMessage(modifyTimeOutBizMessageReqDTO));
    }

    /**
     * 取消超时任务消息API
     *
     * @param cancelTimeOutBizMessageReqDTO CancelTimeOutBizMessageReqDTO
     * @return Response<Boolean> {true: 取消成功; false: 取消失败; null: 异常}
     */
    @Override
    public SingleResponse<Boolean> cancelTimeOutBizMessage(CancelTimeOutBizMessageReqDTO cancelTimeOutBizMessageReqDTO) {
        return SingleResponse.of(operateTimeOutMsgService.cancelTimeOutBizMessage(cancelTimeOutBizMessageReqDTO));
    }
}
