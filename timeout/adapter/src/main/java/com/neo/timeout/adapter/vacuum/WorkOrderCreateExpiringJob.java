package com.neo.timeout.adapter.vacuum;

import com.google.common.collect.Lists;
import com.neo.common.middlewave.task.TaskContext;
import com.neo.timeout.app.msghandle.TimeOutOutputMsgHandleService;
import com.neo.timeout.domain.enums.TimeOutBizTypeEnum;
import com.neo.xxljob.client.context.XxlJobHelper;
import com.neo.xxljob.client.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/8/12
 **/
@Component("WorkOrderCreateExpiringJob")
public class WorkOrderCreateExpiringJob {

    @Autowired
    private TimeOutOutputMsgHandleService timeOutOutputMsgHandleService;

    @XxlJob("WorkOrderCreateExpiringJob")
    public void WorkOrderCreateExpiringJob() throws Exception {
        TaskContext taskContext = new TaskContext();
        taskContext.setFetchDataAmount(100);
        taskContext.setJobName("WorkOrderCreateExpiringJob");
        taskContext.setShardingItems(Lists.newArrayList(XxlJobHelper.getShardIndex()));
        taskContext.setShardingTotalCount(XxlJobHelper.getShardTotal());
        taskContext.setJobParameter(XxlJobHelper.getJobParam());
        //处理超时消息
        timeOutOutputMsgHandleService.handleTimeOutOutputBizMsg(taskContext, TimeOutBizTypeEnum.WORK_ORDER_CREATE_EXPIRING);
    }
}
