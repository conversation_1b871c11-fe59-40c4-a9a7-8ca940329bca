package com.neo.timeout.adapter.tesla;

import com.neo.api.SingleResponse;
import com.neo.timeout.app.operate.OperateTimeOutTaskService;
import com.neo.timeout.client.api.TimeOutTaskOperateService;
import com.neo.timeout.client.domain.dto.CancelTimeOutBizTaskReqDTO;
import com.neo.timeout.client.domain.dto.SetTimeOutBizTaskReqDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR> on 2016-07-11 12:17.
 */
@Service("timeOutTaskOperateService")
public class TimeOutTaskOperateServiceFacade implements TimeOutTaskOperateService {

    @Resource
    private OperateTimeOutTaskService operateTimeOutTaskService;

    /**
     * 设置超时任务API
     *
     * @param setTimeOutBizTaskReqDTO SetTimeOutBizTaskReqDTO
     * @return Response<Boolean> {true: 取消成功; false: 取消失败,失败信息见code和message; null: 异常,异常信息见code和message}
     */
    @Override
    public SingleResponse<Boolean> setTimeOutBizTask(SetTimeOutBizTaskReqDTO setTimeOutBizTaskReqDTO) {
        return SingleResponse.of(operateTimeOutTaskService.setTimeOutBizTask(setTimeOutBizTaskReqDTO));
    }

    /**
     * 取消超时任务API
     *
     * @param cancelTimeOutBizTaskReqDTO CancelTimeOutBizTaskReqDTO
     * @return Response<Boolean> {true: 取消成功; false: 取消失败,失败信息见code和message; null: 异常,异常信息见code和message}
     */
    @Override
    public SingleResponse<Boolean> cancelTimeOutBizTask(CancelTimeOutBizTaskReqDTO cancelTimeOutBizTaskReqDTO) {
        return SingleResponse.of(operateTimeOutTaskService.cancelTimeOutBizTask(cancelTimeOutBizTaskReqDTO));
    }
}
