package com.neo.auth.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.auth.domain.entity.PlatformUser;
import com.neo.auth.domain.gateway.IPlatformUserRepository;
import com.neo.auth.infrastructure.mapper.PlatformUserMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @apiNote 应用管理员表
 * @since 2024-01-12 18:42:21
 */
@Service
public class PlatformUserRepositoryImpl extends ServiceImpl<PlatformUserMapper, PlatformUser>
        implements IPlatformUserRepository {

}