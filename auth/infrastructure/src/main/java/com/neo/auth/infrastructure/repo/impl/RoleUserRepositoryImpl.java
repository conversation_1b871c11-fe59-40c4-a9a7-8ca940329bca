package com.neo.auth.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.auth.domain.entity.RoleUser;
import com.neo.auth.domain.gateway.IRoleUserRepository;
import com.neo.auth.infrastructure.mapper.RoleUserMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @apiNote 用户角色关联表
 * @since 2024-01-12 18:42:21
 */
@Service
public class RoleUserRepositoryImpl extends ServiceImpl<RoleUserMapper, RoleUser>
        implements IRoleUserRepository {

}