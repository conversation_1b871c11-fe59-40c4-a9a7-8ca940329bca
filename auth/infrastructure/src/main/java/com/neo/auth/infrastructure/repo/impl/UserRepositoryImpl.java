package com.neo.auth.infrastructure.repo.impl;

import cn.hutool.core.collection.CollUtil;
import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.auth.client.dto.AuthUserInfoDto;
import com.neo.auth.domain.gateway.IUserRepository;
import com.neo.user.client.userinfo.api.UserService;
import com.neo.user.client.userinfo.dto.UserInfoDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @apiNote 用户查询
 * @since 2024-03-27 16:38:18
 */

@Service("authUserRepositoryImpl")
public class UserRepositoryImpl implements IUserRepository {

    private static final Logger logger = LoggerFactory.getLogger(UserRepositoryImpl.class);

    @Autowired
    private UserService userService;

    @Override
    public Map<Long, AuthUserInfoDto> queryMapByUserIds(List<Long> userIds) {
        SingleResponse<Map<Long, UserInfoDTO>> response = userService.queryMapByUserIds(userIds);
        if (!response.isSuccess()) {
            logger.error("queryMapByUserIds error:{}", response.getErrMessage());
            return null;
        }

        Map<Long, UserInfoDTO> userInfoDTOMap = response.getData();
        Map<Long, AuthUserInfoDto> authUserInfoDtoMap = new HashMap<>();
        userInfoDTOMap.forEach((k, v) -> {
            authUserInfoDtoMap.put(k, convertAuthUserInfoDto(v));
        });
        return authUserInfoDtoMap;
    }

    @Override
    public List<AuthUserInfoDto> queryByUserIds(List<Long> workIds) {
        MultiResponse<UserInfoDTO> response = userService.queryByUserIds(workIds);
        if (!response.isSuccess()) {
            logger.error("queryByUserIds error:{}", response.getErrMessage());
            return null;
        }
        return response.getData().stream().map(this::convertAuthUserInfoDto).collect(Collectors.toList());
    }

    @Override
    public AuthUserInfoDto queryUserByUserId(Long workId) {
        SingleResponse<UserInfoDTO> response = userService.queryUserByUserId(workId);
        if (!response.isSuccess()) {
            logger.error("queryUserByUserId error:{}", response.getErrMessage());
            return null;
        }

        return convertAuthUserInfoDto(response.getData());
    }

    @Override
    public Map<Long, String> getNicknameByUserIds(List<Long> workIds) {
        MultiResponse<UserInfoDTO> response = userService.queryByUserIds(workIds);
        if (!response.isSuccess()) {
            logger.error("getNicknameByUserIds error:{}", response.getErrMessage());
            return new HashMap<>();
        }

        List<UserInfoDTO> userInfoDTOList = response.getData();
        if (CollUtil.isEmpty(userInfoDTOList)) {
            return new HashMap<>();
        }


        return userInfoDTOList.stream().collect(Collectors.toMap(UserInfoDTO::getUserId, UserInfoDTO::getUnick));
    }

    private AuthUserInfoDto convertAuthUserInfoDto(UserInfoDTO userInfoDTO) {
        AuthUserInfoDto authUserInfoDto = new AuthUserInfoDto();
        BeanUtils.copyProperties(userInfoDTO, authUserInfoDto);
        return authUserInfoDto;
    }
}