package com.neo.auth.infrastructure.repo.impl;

import com.neo.auth.domain.entity.Platform;
import com.neo.auth.domain.gateway.IPlatformRepository;
import com.neo.auth.infrastructure.mapper.PlatformMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @apiNote 平台
 * @since 2024-01-12 18:37:39
 */
@Service
public class PlatformRepositoryImpl extends BaseRepositoryImpl<PlatformMapper, Platform>
        implements IPlatformRepository {
}
