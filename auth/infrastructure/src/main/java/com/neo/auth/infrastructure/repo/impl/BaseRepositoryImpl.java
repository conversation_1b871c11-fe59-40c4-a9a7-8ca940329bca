package com.neo.auth.infrastructure.repo.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.auth.domain.gateway.BaseRepository;
import org.apache.commons.lang3.reflect.FieldUtils;


public class BaseRepositoryImpl<M extends BaseMapper<T>, T> extends ServiceImpl<M, T> implements BaseRepository<T> {

    @Override
    public boolean updateByIdSelective(T entity) {
        UpdateWrapper<T> updateWrapper = new UpdateWrapper<>();
        boolean hasId = false;
        try {
            for (java.lang.reflect.Field field : FieldUtils.getAllFieldsList(entity.getClass())) {
                Object value = FieldUtils.readField(field, entity, true);
                if (value != null) {
                    updateWrapper.set(field.getName(), value);
                    if (field.getName().equals("id")) {
                        hasId = true;
                    }
                }
            }
        } catch (IllegalAccessException e) {
            // 处理异常
        }
        if (!hasId) {
            return false;
        }
        return this.update(null, updateWrapper);
    }

}
