package com.neo.auth.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.auth.domain.entity.AuthWorkflowProxyUser;
import com.neo.auth.domain.gateway.IAuthWorkflowProxyUserRepository;
import com.neo.auth.infrastructure.mapper.AuthWorkflowProxyUserMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @apiNote 代理人
 * @since 2024-03-27 16:38:18
 */
@Service
public class AuthWorkflowProxyUserRepositoryImpl extends BaseRepositoryImpl<AuthWorkflowProxyUserMapper, AuthWorkflowProxyUser>
        implements IAuthWorkflowProxyUserRepository {


    private static final Logger logger = LoggerFactory.getLogger(AuthWorkflowProxyUserRepositoryImpl.class);

}