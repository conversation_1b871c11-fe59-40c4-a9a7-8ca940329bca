package com.neo.auth.infrastructure.repo.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.auth.client.param.AuditInfoDto;
import com.neo.auth.client.param.TaskDto;
import com.neo.auth.domain.emums.TaskResult;
import com.neo.auth.domain.entity.Workflow;
import com.neo.auth.domain.entity.flow.DefaultAuditorInfoDto;
import com.neo.auth.domain.entity.flow.EndAtDto;
import com.neo.auth.domain.entity.flow.FlowStateDto;
import com.neo.auth.domain.entity.flow.ProcessDto;
import com.neo.auth.domain.gateway.IWorkflowRepository;
import com.neo.auth.domain.params.ProcessTaskQuery;
import com.neo.auth.infrastructure.mapper.WorkflowMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @apiNote 工作流
 * @since 2024-01-12 18:42:21
 */
@Service
public class WorkflowRepositoryImpl extends ServiceImpl<WorkflowMapper, Workflow>
        implements IWorkflowRepository {

    @Override
    public List<TaskDto> getTasks(String processId) {
        // 默认工作流就只有责任人审批
        Workflow workflow = getOne(
                new LambdaQueryWrapper<Workflow>()
                        .eq(Workflow::getProcessId, processId)
                        .eq(Workflow::getIsDeleted, 0)
        );

        List<TaskDto> taskDtoList = new ArrayList<>();
        taskDtoList.add(buildTaskDto(workflow));

        return taskDtoList;
    }

    @Override
    public TaskDto getTaskByTaskId(String taskId) {
        Workflow workflow = getOne(
                new LambdaQueryWrapper<Workflow>()
                        .eq(Workflow::getTaskId, taskId)
                        .eq(Workflow::getIsDeleted, 0)
        );
        TaskDto taskDto = new TaskDto();
        taskDto.setEndTime(workflow.getUpdated());
        taskDto.setTaskName("责任人审批");
        taskDto.setNickName(workflow.getAuditUserNick());
        taskDto.setComment(TaskResult.valueOf(workflow.getResult().toString()).getComment());
        return taskDto;
    }

    @Override
    public Page<TaskDto> queryTaskByStatus(ProcessTaskQuery processTaskQuery) {
        Page<Workflow> workflow = page(
                new PageDTO<>(processTaskQuery.getPagNum(), processTaskQuery.getPageSize(), true),
                buildTaskByStatusQueryWrapper(processTaskQuery)
        );
        if (workflow == null || workflow.getTotal() == 0) {
            return null;
        }

        List<TaskDto> taskDtoList = new ArrayList<>();
        for (Workflow w : workflow.getRecords()) {
            TaskDto taskDto = new TaskDto();
            taskDto.setId(w.getTaskId());
            taskDto.setBizId(String.valueOf(w.getId()));
            taskDto.setEndTime(w.getUpdated());
            taskDto.setTaskName("责任人审批");
            taskDto.setBizData(convertBizData(w));
            taskDto.setNickName(w.getApplyUserNick());
            taskDto.setComment(Objects.requireNonNull(TaskResult.getByDbRes(w.getResult().toString())).getComment() + " " + w.getAuditReason());
            taskDtoList.add(taskDto);
        }

        return new Page<TaskDto>(workflow.getCurrent(), workflow.getSize(), workflow.getTotal()).setRecords(taskDtoList);
    }

    /**
     * 构建queryTaskByStatus方法查询条件
     */
    private LambdaQueryWrapper<Workflow> buildTaskByStatusQueryWrapper(ProcessTaskQuery processTaskQuery) {
        LambdaQueryWrapper<Workflow> qw = new LambdaQueryWrapper<>();
        qw.orderByDesc(Workflow::getCreated);

        // 工单撤销、流程异常 不展示
        if (processTaskQuery.getDone() == 0) {
            qw.in(Workflow::getResult, List.of(0));
            qw.like(Workflow::getAuditorsSearch, "#" + processTaskQuery.getAssignee().toString() + "#");
        } else if (processTaskQuery.getDone() == 1) {
            qw.in(Workflow::getResult, List.of(1, 2));
            qw.eq(Workflow::getAuditUserId, processTaskQuery.getAssignee());
        }

        qw.eq(Workflow::getTenantId, processTaskQuery.getTenantId());

        return qw;
    }

    private Map<String, Object> convertBizData(Workflow workflow) {
        return BeanUtil.beanToMap(workflow);
    }

    @Override
    public boolean audit(AuditInfoDto auditInfoForm) {
        UpdateWrapper<Workflow> uw = new UpdateWrapper<>();
        uw.set("result", Objects.requireNonNull(TaskResult.getByResult(auditInfoForm.getAction())).getDbRes())
                .set("auditReason", auditInfoForm.getComment())
                .set("auditUserId", auditInfoForm.getActionerUserId())
                .set("auditUserNick", auditInfoForm.getActionerNickName())
                .eq("tenantId", auditInfoForm.getTenantId())
                .eq("id", auditInfoForm.getBiz().getId());
        return update(null, uw);
    }

    @Override
    public boolean cancel(Long id) {
        UpdateWrapper<Workflow> uw = new UpdateWrapper<>();
        uw.set("result", TaskResult.CANCEL.getDbRes())
                .eq("id", id);
        return update(null, uw);
    }

    @Override
    public FlowStateDto startProcess(Integer level, Map<String, Object> bizData) {
        long id = (long) bizData.getOrDefault("id", 0L);
        if (id == 0) {
            return null;
        }

        boolean applyUserIdIsAuditor = false;

        // 判断发起人是否是审核人。如果是，则直接通过
        DefaultAuditorInfoDto defaultAuditorInfoDto = JSONObject.parseObject(bizData.get("auditors").toString(), DefaultAuditorInfoDto.class);
        if (defaultAuditorInfoDto != null) {
            DefaultAuditorInfoDto.Assignee assignee = defaultAuditorInfoDto.getAssignee();
            if (assignee.getAssigneeId().equals(bizData.get("applyUserId"))) {
                applyUserIdIsAuditor = true;
            }
        }

        String processId = RandomUtil.randomString(10);

        UpdateWrapper<Workflow> uw = new UpdateWrapper<>();
        // processId后面会再设置一遍，因为是统一流程，没办法
        uw.set("processId", processId)
                .set("taskId", processId)
                .set(applyUserIdIsAuditor, "auditReason", "发起人是审核人，直接通过")
                .set(applyUserIdIsAuditor, "auditUserNick", "系统")
                .eq("id", id);
        update(null, uw);


        FlowStateDto flowStateDto = new FlowStateDto();
        ProcessDto process = new ProcessDto();
        EndAtDto endAt = new EndAtDto();
        flowStateDto.setProcess(process);
        flowStateDto.setEndAt(endAt);

        process.setProcessInstanceId(processId);
        if (applyUserIdIsAuditor) {
            endAt.setId(TaskResult.AGREE);
        } else {
            endAt.setId(TaskResult.NONE);
        }

        return flowStateDto;
    }

    @Override
    public TaskDto getTask(String taskId) {
        // 默认工作流就只有责任人审批
        Workflow workflow = getOne(
                new LambdaQueryWrapper<Workflow>()
                        .eq(Workflow::getTaskId, taskId)
                        .eq(Workflow::getIsDeleted, 0)
        );


        return buildTaskDto(workflow);
    }

    private TaskDto buildTaskDto(Workflow workflow) {
        TaskDto taskDto = new TaskDto();
        taskDto.setEndTime(workflow.getUpdated());
        taskDto.setTaskName("责任人审批");

        DefaultAuditorInfoDto defaultAuditorInfoDto = JSONObject.parseObject(workflow.getAuditors(), DefaultAuditorInfoDto.class);
        DefaultAuditorInfoDto.Assignee assignee = defaultAuditorInfoDto.getAssignee();
        List<DefaultAuditorInfoDto.Candidate> candidateList = defaultAuditorInfoDto.getCandidates();

        TaskResult taskResult = TaskResult.getByDbRes(workflow.getResult().toString());
        taskDto.setComment(taskResult != null ? taskResult.getComment() : "");
        taskDto.setProcessInstanceId(workflow.getProcessId());
        taskDto.setDone(workflow.getResult());

        // 补充执行人和候选人
        taskDto.setAssignee(assignee.getAssigneeId());
        taskDto.setNickName(assignee.getAssigneeNickName());
        taskDto.setCandidates(candidateList.stream().map(DefaultAuditorInfoDto.Candidate::getCandidateId).collect(Collectors.toList()));
        taskDto.setCandidatesNicks(candidateList.stream().map(DefaultAuditorInfoDto.Candidate::getCandidateNickName).reduce((a, b) -> a + "," + b).orElse(""));

        // 实际审核人
        taskDto.setAuditUserId(workflow.getAuditUserId());
        taskDto.setAuditUserNick(workflow.getAuditUserNick());

        return taskDto;
    }
}