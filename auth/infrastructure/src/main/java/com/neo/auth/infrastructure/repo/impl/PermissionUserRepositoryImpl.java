package com.neo.auth.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.auth.domain.entity.PermissionUser;
import com.neo.auth.domain.gateway.IPermissionUserRepository;
import com.neo.auth.infrastructure.mapper.PermissionUserMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @apiNote 权限用户关联表
 * @since 2024-01-12 18:42:21
 */
@Service
public class PermissionUserRepositoryImpl extends ServiceImpl<PermissionUserMapper, PermissionUser>
        implements IPermissionUserRepository {

}