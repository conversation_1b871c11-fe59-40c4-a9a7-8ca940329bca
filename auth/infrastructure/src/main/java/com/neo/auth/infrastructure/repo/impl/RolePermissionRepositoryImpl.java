package com.neo.auth.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.auth.domain.entity.RolePermission;
import com.neo.auth.domain.gateway.IRolePermissionRepository;
import com.neo.auth.infrastructure.mapper.RolePermissionMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @apiNote 角色权限关联表
 * @since 2024-01-12 18:42:21
 */
@Service
public class RolePermissionRepositoryImpl extends ServiceImpl<RolePermissionMapper, RolePermission>
        implements IRolePermissionRepository {

}