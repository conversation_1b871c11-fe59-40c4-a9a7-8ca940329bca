<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.neo</groupId>
        <artifactId>auth</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>auth-infrastructure</artifactId>
    <name>auth-infrastructure</name>

    <dependencies>

        <!--  环境引入   -->
        <!--        <dependency>
                    <groupId>org.mybatis.spring.boot</groupId>
                    <artifactId>mybatis-spring-boot-starter</artifactId>
                </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!--  通用      -->

        <!--  notify 仅允许引用domain      -->
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>auth-domain</artifactId>
        </dependency>

    </dependencies>
</project>
