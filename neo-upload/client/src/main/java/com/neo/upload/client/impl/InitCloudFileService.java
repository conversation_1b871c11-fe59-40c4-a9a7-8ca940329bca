package com.neo.upload.client.impl;

import com.neo.upload.client.api.CloudType;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/*
 *             ,%%%%%%%%,
 *           ,%%/\%%%%/\%%
 *          ,%%%\c "" J/%%%
 * %.       %%%%/ o  o \%%%
 * `%%.     %%%%    _  |%%%
 *  `%%     `%%%%(__Y__)%%'
 *  //       ;%%%%`\-/%%%'
 * ((       /  `%%%%%%%'
 *  \\    .'          |
 *   \\  /       \  | |
 *    \\/         ) | |
 *     \         /_ | |__
 *     (___________))))))) 攻城狮
 *
 *
 * 用途说明:
 * 作者姓名: 竹笋
 * 创建时间: 2024/5/7 下午3:39
 */
@Component
public class InitCloudFileService implements InitializingBean {


    protected static final Map<CloudType, CloudFileService> cloudFileServiceMap = new HashMap<>();
    @Resource
    private CloudFileService tencentCloudFileServiceImpl;


    @Resource
    private CloudFileService aliCloudFileServiceImpl;


    @Override
    public void afterPropertiesSet() throws Exception {
        cloudFileServiceMap.put(CloudType.ALI_OSS, aliCloudFileServiceImpl);
        cloudFileServiceMap.put(CloudType.TENCENT_COS, tencentCloudFileServiceImpl);
    }
}
