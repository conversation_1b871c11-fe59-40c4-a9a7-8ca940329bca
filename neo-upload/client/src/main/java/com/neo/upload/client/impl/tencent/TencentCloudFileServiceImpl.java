package com.neo.upload.client.impl.tencent;

import com.neo.api.SingleResponse;
import com.neo.upload.client.api.CloudFilePathEnum;
import com.neo.upload.client.api.CloudFileSuffixEnum;
import com.neo.upload.client.impl.CloudFileService;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.model.COSObject;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.region.Region;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.InputStream;
import java.util.UUID;

/*
 *             ,%%%%%%%%,
 *           ,%%/\%%%%/\%%
 *          ,%%%\c "" J/%%%
 * %.       %%%%/ o  o \%%%
 * `%%.     %%%%    _  |%%%
 *  `%%     `%%%%(__Y__)%%'
 *  //       ;%%%%`\-/%%%'
 * ((       /  `%%%%%%%'
 *  \\    .'          |
 *   \\  /       \  | |
 *    \\/         ) | |
 *     \         /_ | |__
 *     (___________))))))) 攻城狮
 *
 *
 * 用途说明:
 * 作者姓名: 竹笋
 * 创建时间: 2024/4/19 上午10:12
 */
@Service
@DependsOn("cosProperties")
public class TencentCloudFileServiceImpl implements CloudFileService {

    /**
     * 上传文件
     *
     * @param inputStream
     * @return url
     */
    @Override
    public SingleResponse<String> uploadFile(CloudFilePathEnum type, CloudFileSuffixEnum cloudFileSuffixEnum, InputStream inputStream) {
        String path = type.getPath() + UUID.randomUUID() + "." + cloudFileSuffixEnum.getSuffix();
        return this.upload(path, inputStream);
    }

    @Override
    public SingleResponse<String> uploadCustomFileName(CloudFilePathEnum type, String fileName, InputStream inputStream) {
        String path = type.getPath() + System.currentTimeMillis() / 1000 + "_" + fileName;
        return this.upload(path, inputStream);
    }


    @Override
    public SingleResponse<InputStream> getFile(String filePath) {
        try {
            if (filePath == null) {
                return SingleResponse.buildFailure("5006", "文件路径不能为空");
            }
            String key = filePath.replace(CosProperties.URL + "/", "");
            COSCredentials cred = new BasicCOSCredentials(CosProperties.ACCESS_KEY_ID, CosProperties.ACCESS_KEY_SECRET);
            // 2 设置 bucket 的地域, COS 地域的简称请参见 https://cloud.tencent.com/document/product/436/6224
            // clientConfig 中包含了设置 region, https(默认 http), 超时, 代理等 set 方法, 使用可参见源码或者常见问题 Java SDK 部分。
            Region region = new Region(CosProperties.REGION);
            ClientConfig clientConfig = new ClientConfig(region);
            // 这里建议设置使用 https 协议
            // 从 5.6.54 版本开始，默认使用了 https
            clientConfig.setHttpProtocol(HttpProtocol.https);
            // 3 生成 cos 客户端。
            COSClient cosClient = new COSClient(cred, clientConfig);

            //去掉CosProperties.url
            COSObject object = cosClient.getObject(CosProperties.BUCKET_NAME, key);

            if (object == null) {
                return SingleResponse.buildFailure("400", "未获取到COS对象");
            }
            return SingleResponse.buildSuccess(object.getObjectContent());
        } catch (Exception e) {
            return SingleResponse.buildFailure("400", "获取失败！");
        }

    }

    private SingleResponse<String> upload(String filePath, InputStream inputStream) {
        try {
            COSCredentials cred = new BasicCOSCredentials(CosProperties.ACCESS_KEY_ID, CosProperties.ACCESS_KEY_SECRET);
            // 2 设置 bucket 的地域, COS 地域的简称请参见 https://cloud.tencent.com/document/product/436/6224
            // clientConfig 中包含了设置 region, https(默认 http), 超时, 代理等 set 方法, 使用可参见源码或者常见问题 Java SDK 部分。
            Region region = new Region(CosProperties.REGION);
            ClientConfig clientConfig = new ClientConfig(region);
            // 这里建议设置使用 https 协议
            // 从 5.6.54 版本开始，默认使用了 https
            clientConfig.setHttpProtocol(HttpProtocol.https);
            // 3 生成 cos 客户端。
            COSClient cosClient = new COSClient(cred, clientConfig);
            ObjectMetadata objectMetadata = new ObjectMetadata();
            PutObjectRequest putObjectRequest = new PutObjectRequest(CosProperties.BUCKET_NAME, filePath, inputStream, objectMetadata);
            PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);

            if (putObjectResult == null || StringUtils.isEmpty(putObjectResult.getETag())) {
                return SingleResponse.buildFailure("上传失败");
            }
            return SingleResponse.buildSuccess(CosProperties.URL + "/" + filePath);
        } catch (Exception e) {
            return SingleResponse.buildFailure("上传失败");
        }
    }
}
