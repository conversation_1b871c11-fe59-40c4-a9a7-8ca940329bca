package com.neo.tagcenter.adapter.rpc;

import cn.hutool.core.collection.CollUtil;
import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.tagcenter.app.biz.option.TreeTagQuery;
import com.neo.tagcenter.app.constants.TagDomainEnums;
import com.neo.tagcenter.app.logic.TagLeafInfoLogic;
import com.neo.tagcenter.app.logic.TreeTagLogic;
import com.neo.tagcenter.client.common.TagCenterResultTypeEnum;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;
import com.neo.tagcenter.client.dto.TreeTagDto;
import com.neo.tagcenter.client.param.BaseTagQueryOption;
import com.neo.tagcenter.client.param.BusinessTreeTagQueryParam;
import com.neo.tagcenter.client.rpc.BusinessTreeTagReadReadService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class BusinessTreeTagReadReadServiceImpl implements BusinessTreeTagReadReadService {

    private static final Logger log = LoggerFactory.getLogger(BusinessTreeTagReadReadServiceImpl.class);

    @Resource
    private TreeTagLogic treeTagLogic;

    @Resource
    private TagLeafInfoLogic tagLeafInfoLogic;


    @Override
    public MultiResponse<TagLeafInfoDto> querySalesRegionSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option) {
        return queryTagLeafInfo(param, TagDomainEnums.SALES_REGION.getCode(), option);
    }

    @Override
    public MultiResponse<TagLeafInfoDto> queryAdministrativeRegionSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option) {
        return queryTagLeafInfo(param, TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), option);
    }

    @Override
    public MultiResponse<TagLeafInfoDto> queryCustomerTypeSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option) {
        return queryTagLeafInfo(param, TagDomainEnums.CUSTOMER_TYPE.getCode(), option);
    }

    @Override
    public MultiResponse<TagLeafInfoDto> queryPricePlanSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option) {
        return queryTagLeafInfo(param, TagDomainEnums.PRICE_PLAN.getCode(), option);
    }

    @Override
    public MultiResponse<TagLeafInfoDto> queryDeliveryRouteSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option) {
        return queryTagLeafInfo(param, TagDomainEnums.DELIVERY_ROUTE.getCode(), option);
    }

    @Override
    public MultiResponse<TagLeafInfoDto> queryGoodsCategorySetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option) {
        return queryTagLeafInfo(param, TagDomainEnums.GOODS_CATEGORY.getCode(), option);
    }

    @Override
    public MultiResponse<TagLeafInfoDto> queryGoodsCategorySettingFinished(BusinessTreeTagQueryParam param, BaseTagQueryOption option) {
        return queryTagLeafInfoByTagCode(param, "10", option);
    }

    @Override
    public MultiResponse<TagLeafInfoDto> queryGoodsCategorySettingSemiFinished(BusinessTreeTagQueryParam param, BaseTagQueryOption option) {
        return queryTagLeafInfoByTagCode(param, "20", option);
    }

    @Override
    public MultiResponse<TagLeafInfoDto> queryGoodsCategorySettingRawMaterial(BusinessTreeTagQueryParam param, BaseTagQueryOption option) {
        return queryTagLeafInfoByTagCode(param, "30", option);
    }

    private MultiResponse<TagLeafInfoDto> queryTagLeafInfo(BusinessTreeTagQueryParam param, String tagDomain, BaseTagQueryOption option) {
        List<TagLeafInfoDto> tagLeafInfoDtoList = tagLeafInfoLogic.queryTagLeafInfoByBusinessDomainAndTagDomain(param, tagDomain, option);
        if (CollUtil.isEmpty(tagLeafInfoDtoList)) {
            return MultiResponse.buildFailure(TagCenterResultTypeEnum.TAG_LEAF_NOT_EXISTS);
        }
        return MultiResponse.of(tagLeafInfoDtoList);
    }

    private MultiResponse<TagLeafInfoDto> queryTagLeafInfoByTagCode(BusinessTreeTagQueryParam param, String tagCode, BaseTagQueryOption option) {
        TreeTagQuery treeTagQuery = new TreeTagQuery();
        treeTagQuery.setBusinessDomain(param.getBusinessDomain());
        treeTagQuery.setTagDomain(TagDomainEnums.GOODS_CATEGORY.getCode());
        treeTagQuery.setTagCode(tagCode);
        treeTagQuery.setQueryChild(true);
        treeTagQuery.setIncludeDisable(option.getIncludeDisable());
        treeTagQuery.setIncludeDeleted(option.getIncludeDeleted());
        MultiResponse<TreeTagDto> response = treeTagLogic.queryTreeTagDto(treeTagQuery);

        if (!response.isSuccess()) {
            return MultiResponse.buildFailure(TagCenterResultTypeEnum.TAG_LEAF_NOT_EXISTS);
        }

        List<TagLeafInfoDto> tagLeafInfoDtoList = response.getData().stream().map(TreeTagDto::getTagLeafInfoDto).toList();
        if (CollUtil.isEmpty(tagLeafInfoDtoList)) {
            return MultiResponse.buildFailure(TagCenterResultTypeEnum.TAG_LEAF_NOT_EXISTS);
        }

        return MultiResponse.of(tagLeafInfoDtoList);
    }

    // ==================== 批量查询方法实现 ====================

    @Override
    public SingleResponse<Map<String, TagLeafInfoDto>> batchQuerySalesRegionSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option) {
        return batchQueryTagLeafInfo(param, TagDomainEnums.SALES_REGION.getCode(), option);
    }

    @Override
    public SingleResponse<Map<String, TagLeafInfoDto>> batchQueryAdministrativeRegionSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option) {
        return batchQueryTagLeafInfo(param, TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), option);
    }

    @Override
    public SingleResponse<Map<String, TagLeafInfoDto>> batchQueryCustomerTypeSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option) {
        return batchQueryTagLeafInfo(param, TagDomainEnums.CUSTOMER_TYPE.getCode(), option);
    }

    @Override
    public SingleResponse<Map<String, TagLeafInfoDto>> batchQueryPricePlanSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option) {
        return batchQueryTagLeafInfo(param, TagDomainEnums.PRICE_PLAN.getCode(), option);
    }

    @Override
    public SingleResponse<Map<String, TagLeafInfoDto>> batchQueryDeliveryRouteSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option) {
        return batchQueryTagLeafInfo(param, TagDomainEnums.DELIVERY_ROUTE.getCode(), option);
    }

    @Override
    public SingleResponse<Map<String, TagLeafInfoDto>> batchQueryGoodsCategorySetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option) {
        return batchQueryTagLeafInfo(param, TagDomainEnums.GOODS_CATEGORY.getCode(), option);
    }

    @Override
    public SingleResponse<Map<String, TagLeafInfoDto>> batchQueryGoodsCategorySettingFinished(BusinessTreeTagQueryParam param, BaseTagQueryOption option) {
        return batchQueryTagLeafInfoByTagCode(param, "10", option);
    }

    @Override
    public SingleResponse<Map<String, TagLeafInfoDto>> batchQueryGoodsCategorySettingSemiFinished(BusinessTreeTagQueryParam param, BaseTagQueryOption option) {
        return batchQueryTagLeafInfoByTagCode(param, "20", option);
    }

    @Override
    public SingleResponse<Map<String, TagLeafInfoDto>> batchQueryGoodsCategorySettingRawMaterial(BusinessTreeTagQueryParam param, BaseTagQueryOption option) {
        return batchQueryTagLeafInfoByTagCode(param, "30", option);
    }

    /**
     * 批量查询标签信息的通用方法
     */
    private SingleResponse<Map<String, TagLeafInfoDto>> batchQueryTagLeafInfo(BusinessTreeTagQueryParam param, String tagDomain, BaseTagQueryOption option) {
        Map<String, TagLeafInfoDto> resultMap = tagLeafInfoLogic.batchQueryTagLeafInfoByBusinessDomainAndTagDomain(param, tagDomain, option);
        if (CollUtil.isEmpty(resultMap)) {
            return SingleResponse.buildFailure(TagCenterResultTypeEnum.TAG_LEAF_NOT_EXISTS);
        }
        return SingleResponse.of(resultMap);
    }

    /**
     * 根据标签代码批量查询标签信息的方法
     */
    private SingleResponse<Map<String, TagLeafInfoDto>> batchQueryTagLeafInfoByTagCode(BusinessTreeTagQueryParam param, String tagCode, BaseTagQueryOption option) {
        // 对于按tagCode查询的情况，需要特殊处理
        // 这里暂时返回失败，因为TreeTagLogic的批量查询需要额外实现
        return SingleResponse.buildFailure(TagCenterResultTypeEnum.TAG_LEAF_NOT_EXISTS);
    }
}
