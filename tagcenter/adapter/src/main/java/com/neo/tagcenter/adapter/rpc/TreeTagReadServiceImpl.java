package com.neo.tagcenter.adapter.rpc;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.api.code.GlobalResponseCodeEnum;
import com.neo.tagcenter.app.biz.option.TreeTagQuery;
import com.neo.tagcenter.app.logic.TagLeafInfoLogic;
import com.neo.tagcenter.app.logic.TreeTagLogic;
import com.neo.tagcenter.client.common.TagCenterResultTypeEnum;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;
import com.neo.tagcenter.client.param.BaseTagQueryOption;
import com.neo.tagcenter.client.param.BaseTreeTagQueryOption;
import com.neo.tagcenter.client.param.BusinessTreeTagQueryParam;
import com.neo.tagcenter.client.param.TreeTagQueryOption;
import com.neo.tagcenter.client.rpc.TreeTagReadService;
import com.neo.tagcenter.domain.entity.TagLeafInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Service
public class TreeTagReadServiceImpl implements TreeTagReadService {

    private static final Logger log = LoggerFactory.getLogger(TreeTagReadServiceImpl.class);

    @Autowired
    private TreeTagLogic treeTagLogic;

    @Autowired
    private TagLeafInfoLogic tagLeafInfoLogic;


    @Override
    public SingleResponse<Map<Integer, List<TagLeafInfoDto>>> queryTagLeafInfoByBusinessDomain(List<Integer> businessDomainEnumList, BaseTagQueryOption option) {
        if (CollUtil.isEmpty(businessDomainEnumList)) {
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }

        Map<Integer, List<TagLeafInfoDto>> tagLeafInfoMap = tagLeafInfoLogic.queryTagLeafInfoByBusinessDomain(businessDomainEnumList, option);
        return SingleResponse.of(tagLeafInfoMap);
    }

    @Override
    public SingleResponse<Map<String, List<TagLeafInfoDto>>> queryTagLeafInfoByTagDomain(List<String> tagDomainEnumList, BaseTagQueryOption option) {
        if (CollUtil.isEmpty(tagDomainEnumList)) {
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }

        Map<String, List<TagLeafInfoDto>> tagLeafInfoMap = tagLeafInfoLogic.queryTagLeafInfoByTagDomain(tagDomainEnumList, option);
        return SingleResponse.of(tagLeafInfoMap);
    }

    @Override
    public MultiResponse<TagLeafInfoDto> queryTagLeafInfoByBusinessDomainAndTagDomain(Integer businessDomain, String tagDomain, BaseTagQueryOption option) {
        if (businessDomain == null || StrUtil.isBlank(tagDomain)) {
            return MultiResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }

        BusinessTreeTagQueryParam param = new BusinessTreeTagQueryParam();
        param.setBusinessDomain(businessDomain);

        List<TagLeafInfoDto> tagLeafInfoDtoList = tagLeafInfoLogic.queryTagLeafInfoByBusinessDomainAndTagDomain(param, tagDomain, option);
        return MultiResponse.of(tagLeafInfoDtoList);
    }

    @Override
    public MultiResponse<TagLeafInfoDto> queryTagLeafInfoById(Long tagLeafId, BaseTreeTagQueryOption option) {
        if (tagLeafId == null || tagLeafId <= 0) {
            return MultiResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }

        TreeTagQuery query = new TreeTagQuery();
        query.setTagLeafId(tagLeafId);
        BeanUtil.copyProperties(option, query);
        query.setQueryChild(option.getQueryChild());

        return executeQuery(treeTagLogic::getTreeTagWithOptions, query);
    }

    @Override
    public SingleResponse<Map<Long, List<TagLeafInfoDto>>> batchQueryTagLeafInfoByIds(List<Long> tagLeafIds, BaseTreeTagQueryOption option) {
        if (CollUtil.isEmpty(tagLeafIds)) {
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }

        TreeTagQuery query = new TreeTagQuery();
        query.setTagLeafIds(tagLeafIds);
        BeanUtil.copyProperties(option, query);
        query.setQueryChild(option.getQueryChild());

        Map<Long, List<TagLeafInfo>> treeMap = treeTagLogic.getTreeTagWithOptionsReturnMap(query);
        if (MapUtil.isEmpty(treeMap)) {
            return SingleResponse.buildFailure(TagCenterResultTypeEnum.TAG_LEAF_NOT_EXISTS);
        }

        Map<Long, List<TagLeafInfoDto>> resultMap = new HashMap<>();
        treeMap.forEach((key, value) -> {
            resultMap.put(key, BeanUtil.copyToList(value, TagLeafInfoDto.class));
        });

        return SingleResponse.of(resultMap);
    }

    @Override
    public MultiResponse<TagLeafInfoDto> queryTagLeafInfoByTagCode(Integer businessDomain, String tagDomain, String tagCode, BaseTreeTagQueryOption option) {
        if (StrUtil.isBlank(tagCode)) {
            return MultiResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }

        TreeTagQuery query = new TreeTagQuery();
        query.setBusinessDomain(businessDomain);
        query.setTagDomain(tagDomain);
        query.setTagCode(tagCode);
        BeanUtil.copyProperties(option, query);

        return executeQuery(treeTagLogic::getTreeTagWithOptions, query);
    }

    @Override
    public MultiResponse<TagLeafInfoDto> queryTagLeafInfoByTagName(Integer businessDomain, String tagDomain, String tagName, BaseTreeTagQueryOption option) {
        if (StrUtil.isBlank(tagName)) {
            return MultiResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }

        TreeTagQuery query = new TreeTagQuery();
        query.setTagName(tagName);
        query.setBusinessDomain(businessDomain);
        query.setTagDomain(tagDomain);
        BeanUtil.copyProperties(option, query);
        query.setQueryChild(option.getQueryChild());

        return executeQuery(treeTagLogic::getTreeTagWithOptions, query);
    }

    @Override
    public MultiResponse<TagLeafInfoDto> queryTagLeafInfo(TreeTagQueryOption option) {
        String errMsg = option.validate();
        if (StrUtil.isNotBlank(errMsg)) {
            return MultiResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR.getCode(), errMsg);
        }

        List<TagLeafInfoDto> tagLeafInfoList = treeTagLogic.queryTagLeafInfo(option);
        return MultiResponse.of(tagLeafInfoList);
    }

    private MultiResponse<TagLeafInfoDto> executeQuery(Function<TreeTagQuery, List<TagLeafInfo>> queryFunction,
                                                       TreeTagQuery query) {
        List<TagLeafInfo> tagLeafInfoList = queryFunction.apply(query);

        if (CollUtil.isEmpty(tagLeafInfoList)) {
            return MultiResponse.buildFailure(TagCenterResultTypeEnum.TAG_LEAF_NOT_EXISTS);
        }

        return MultiResponse.of(BeanUtil.copyToList(tagLeafInfoList, TagLeafInfoDto.class));
    }
}
