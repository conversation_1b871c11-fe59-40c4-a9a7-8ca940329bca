package com.neo.tagcenter.adapter.rpc;

import com.neo.api.SingleResponse;
import com.neo.session.SessionContextHolder;
import com.neo.tagcenter.app.logic.TagLeafInfoLogic;
import com.neo.tagcenter.app.logic.TreeTagLogic;
import com.neo.tagcenter.client.param.EnableDisableFeatureParam;
import com.neo.tagcenter.client.param.TagBusinessDomainParam;
import com.neo.tagcenter.client.param.TreeTagSaveParam;
import com.neo.tagcenter.client.param.TreeTagUpdateParam;
import com.neo.tagcenter.client.rpc.TreeTagWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TreeTagWriteServiceImpl implements TreeTagWriteService {

    @Autowired
    private TreeTagLogic treeTagLogic;

    @Autowired
    private TagLeafInfoLogic tagLeafInfoLogic;

    @Override
    public SingleResponse<Long> addTagLeafInfo(TreeTagSaveParam treeTagSaveParam) {
        return treeTagLogic.addTreeTagReturnId(treeTagSaveParam);
    }

    @Override
    public SingleResponse<Boolean> updateTagLeaf(TreeTagUpdateParam treeTagUpdateParam) {
        return treeTagLogic.updateTagLeaf(treeTagUpdateParam);
    }

    @Override
    public SingleResponse<Boolean> enableDisableTagLeaf(EnableDisableFeatureParam enableDisableFeatureParam) {
        return null;
    }

    @Override
    public SingleResponse<Boolean> delTagLeaf(Long tagLeafId) {
        return treeTagLogic.deleteTreeTagWithinChildren(tagLeafId, -1);
    }

    @Override
    public SingleResponse<Boolean> addBusinessDomain(TagBusinessDomainParam tagBusinessDomainParam) {
        return treeTagLogic.addBusinessDomain(tagBusinessDomainParam);
    }
}
