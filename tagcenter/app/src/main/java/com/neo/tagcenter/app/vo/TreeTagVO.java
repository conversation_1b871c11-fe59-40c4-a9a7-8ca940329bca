package com.neo.tagcenter.app.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class TreeTagVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -3034054285112328563L;

    /**
     * 标签标识
     */
    private String key;
    /**
     * 标签名称
     */
    private String title;
    /**
     * 业务域，
     */
    private Integer businessDomain;

    /**
     * 标签域，
     */
    private String tagDomain;
    /**
     * 该叶子节点父叶子节点
     */
    private Long parentId;
    /**
     * 该叶子节点在标签等级依次是1，2，3，4，5
     */
    private Integer level;
    /**
     * 该叶子结点的根结点tagId
     */
    private Long rootId;
    /**
     * 该叶子结点在该层级的位置
     */
    private Integer pos;
    /**
     * 该叶子结点的扩展信息
     */
    private String extra;

    /**
     * 0：可用
     * 1：不可用
     */
    private Integer isEnabled;

    /**
     * 0：未删除
     * 1：已删除
     */
    private Integer isDeleted;

    /**
     * 是否是子节点
     */
    private Boolean isLeaf;

    /**
     * 编号
     */
    private String code;
    /**
     * 名称简写
     */
    private String shortName;
    /**
     * 描述
     */
    private String description;

    /**
     * 三方创建时间（时间戳）
     */
    private Long thirdPartyCreateTime;

    /**
     * 三方创建人
     */
    private String thirdPartyCreator;

    /**
     * 创建时间（时间戳）
     */
    private Long createTime;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 修改时间（时间戳）
     */
    private Long updateTime;
}
