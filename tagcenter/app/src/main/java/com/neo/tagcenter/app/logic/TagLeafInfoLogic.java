package com.neo.tagcenter.app.logic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.neo.api.SingleResponse;
import com.neo.api.code.GlobalResponseCodeEnum;
import com.neo.tagcenter.app.biz.option.TagLeafInfoQueryOption;
import com.neo.tagcenter.app.constants.LevelEnum;
import com.neo.tagcenter.client.common.TagCenterResultTypeEnum;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;
import com.neo.tagcenter.client.param.BaseTagQueryOption;
import com.neo.tagcenter.client.param.BusinessTreeTagQueryParam;
import com.neo.tagcenter.domain.entity.TagLeafInfo;
import com.neo.tagcenter.domain.gateway.ITagLeafInfoRepository;
import com.neo.tagcenter.domain.util.TagConvert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class TagLeafInfoLogic {

    @Autowired
    private ITagLeafInfoRepository iTagLeafInfoRepository;

    /**
     * 单条插入
     */
    public Long insert(TagLeafInfoDto tagLeafInfoDTO) {
        if (Objects.isNull(tagLeafInfoDTO)) {
            return -1L;
        }
        // 验证父节点是否存在
        TagLeafInfo tagLeafInfoDO = TagConvert.fromDTO(tagLeafInfoDTO);
        TagLeafInfo parent = queryTagLeafInfoById(tagLeafInfoDO.getParentId(), new BaseTagQueryOption());
        setLevelInfo(parent, tagLeafInfoDO);

        long time = DateUtil.currentSeconds();
        tagLeafInfoDO.setCreated(time);
        tagLeafInfoDO.setUpdated(time);

        tagLeafInfoDO.setCreateTime(time);
        tagLeafInfoDO.setUpdateTime(time);
        tagLeafInfoDO.setCreator(tagLeafInfoDTO.getCreator());
        tagLeafInfoDO.setUpdater(tagLeafInfoDTO.getCreator());

        boolean res = iTagLeafInfoRepository.save(tagLeafInfoDO);

        tagLeafInfoDTO.setId(tagLeafInfoDO.getId());
        tagLeafInfoDTO.setLevel(tagLeafInfoDO.getLevel());
        return res ? tagLeafInfoDO.getId() : -1L;
    }

    public SingleResponse<Boolean> updateTreeTag(TagLeafInfoDto tagLeafInfoDto) {
        if (tagLeafInfoDto == null || tagLeafInfoDto.getId() == null
                || tagLeafInfoDto.getId() <= 0L || StrUtil.isBlank(tagLeafInfoDto.getName())) {
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }

        TagLeafInfo tagLeafInfo = new TagLeafInfo();
        tagLeafInfo.setId(tagLeafInfoDto.getId());
        tagLeafInfo.setName(tagLeafInfoDto.getName());
        tagLeafInfo.setExtra(tagLeafInfoDto.getExtra());
        tagLeafInfo.setUpdated(new Date().getTime());

        tagLeafInfo.setCode(tagLeafInfoDto.getCode());
        tagLeafInfo.setShortName(tagLeafInfoDto.getShortName());
        tagLeafInfo.setDescription(tagLeafInfoDto.getDescription());
        tagLeafInfo.setUpdateTime(new Date().getTime());
        tagLeafInfo.setUpdater(tagLeafInfoDto.getUpdater());

        return SingleResponse.of(iTagLeafInfoRepository.updateById(tagLeafInfo));
    }

    public SingleResponse<Boolean> updateTreeTagName(TagLeafInfoDto tagLeafInfoDto) {
        if (tagLeafInfoDto == null || tagLeafInfoDto.getId() == null
                || tagLeafInfoDto.getId() <= 0L || StrUtil.isBlank(tagLeafInfoDto.getName())) {
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }

        TagLeafInfo tagLeafInfo = queryTagLeafInfoById(tagLeafInfoDto.getId(), new BaseTagQueryOption());
        if (tagLeafInfo == null) {
            return SingleResponse.buildFailure(TagCenterResultTypeEnum.TAG_LEAF_NOT_EXISTS);
        }

        // check 名称是否重复
        TagLeafInfo checkName = iTagLeafInfoRepository.getOne(
                new LambdaQueryWrapper<TagLeafInfo>()
                        .eq(TagLeafInfo::getBusinessDomain, tagLeafInfo.getBusinessDomain())
                        .eq(TagLeafInfo::getTagDomain, tagLeafInfo.getTagDomain())
                        .eq(TagLeafInfo::getName, tagLeafInfoDto.getName())
                        .eq(TagLeafInfo::getIsDeleted, 0)
        );
        if (checkName != null) {
            return SingleResponse.buildFailure(TagCenterResultTypeEnum.TAG_NAME_REPEAT);
        }

        TagLeafInfo addTagLeafInfo = new TagLeafInfo();
        addTagLeafInfo.setId(tagLeafInfoDto.getId());
        addTagLeafInfo.setName(tagLeafInfoDto.getName());
        addTagLeafInfo.setUpdated(new Date().getTime());
        return SingleResponse.of(iTagLeafInfoRepository.updateById(addTagLeafInfo));
    }

    /*
     * 子标签设置level
     * */
    private void setLevelInfo(TagLeafInfo parent, TagLeafInfo tagLeafInfoDO) {
        if (parent == null) {
            tagLeafInfoDO.setLevel(LevelEnum.Level1.getLevel());
            tagLeafInfoDO.setRootId(tagLeafInfoDO.getId());
            return;
        }
        LevelEnum levelEnum = LevelEnum.getLevelEnum((parent.getLevel() + 1));
        tagLeafInfoDO.setLevel(levelEnum == null ? null : levelEnum.getLevel());
        LevelEnum parentLevelEnum = LevelEnum.getLevelEnum((parent.getLevel()));
        if (parentLevelEnum == null) {
            throw new IllegalStateException("Set leaf level error because valid parent level Unexpected value: " + parent.getLevel());
        }
        switch (parentLevelEnum) {
            case Level1 -> tagLeafInfoDO.setRootId(parent.getId());
            case Level2 -> {
                tagLeafInfoDO.setRootId(parent.getRootId());
                tagLeafInfoDO.setLevel2Id(parent.getId());
            }
            case Level3 -> {
                tagLeafInfoDO.setRootId(parent.getRootId());
                tagLeafInfoDO.setLevel2Id(parent.getLevel2Id());
                tagLeafInfoDO.setLevel3Id(parent.getId());
            }
            case Level4 -> {
                tagLeafInfoDO.setRootId(parent.getRootId());
                tagLeafInfoDO.setLevel2Id(parent.getLevel2Id());
                tagLeafInfoDO.setLevel3Id(parent.getLevel3Id());
                tagLeafInfoDO.setLevel4Id(parent.getId());
            }
            case Level5 -> {
                tagLeafInfoDO.setRootId(parent.getRootId());
                tagLeafInfoDO.setLevel2Id(parent.getLevel2Id());
                tagLeafInfoDO.setLevel3Id(parent.getLevel3Id());
                tagLeafInfoDO.setLevel4Id(parent.getLevel4Id());
                tagLeafInfoDO.setLevel5Id(parent.getId());
            }
            case Level6 -> {
                tagLeafInfoDO.setRootId(parent.getRootId());
                tagLeafInfoDO.setLevel2Id(parent.getLevel2Id());
                tagLeafInfoDO.setLevel3Id(parent.getLevel3Id());
                tagLeafInfoDO.setLevel4Id(parent.getLevel4Id());
                tagLeafInfoDO.setLevel5Id(parent.getLevel5Id());
                tagLeafInfoDO.setLevel6Id(parent.getId());
            }
            case Level7 -> {
                tagLeafInfoDO.setRootId(parent.getRootId());
                tagLeafInfoDO.setLevel2Id(parent.getLevel2Id());
                tagLeafInfoDO.setLevel3Id(parent.getLevel3Id());
                tagLeafInfoDO.setLevel4Id(parent.getLevel4Id());
                tagLeafInfoDO.setLevel5Id(parent.getLevel5Id());
                tagLeafInfoDO.setLevel6Id(parent.getLevel6Id());
                tagLeafInfoDO.setLevel7Id(parent.getId());
            }
            case Level8 -> {
                tagLeafInfoDO.setRootId(parent.getRootId());
                tagLeafInfoDO.setLevel2Id(parent.getLevel2Id());
                tagLeafInfoDO.setLevel3Id(parent.getLevel3Id());
                tagLeafInfoDO.setLevel4Id(parent.getLevel4Id());
                tagLeafInfoDO.setLevel5Id(parent.getLevel5Id());
                tagLeafInfoDO.setLevel6Id(parent.getLevel6Id());
                tagLeafInfoDO.setLevel7Id(parent.getLevel7Id());
                tagLeafInfoDO.setLevel8Id(parent.getId());
            }
            case Level9 -> {
                tagLeafInfoDO.setRootId(parent.getRootId());
                tagLeafInfoDO.setLevel2Id(parent.getLevel2Id());
                tagLeafInfoDO.setLevel3Id(parent.getLevel3Id());
                tagLeafInfoDO.setLevel4Id(parent.getLevel4Id());
                tagLeafInfoDO.setLevel5Id(parent.getLevel5Id());
                tagLeafInfoDO.setLevel6Id(parent.getLevel6Id());
                tagLeafInfoDO.setLevel7Id(parent.getLevel7Id());
                tagLeafInfoDO.setLevel8Id(parent.getLevel8Id());
                tagLeafInfoDO.setLevel9Id(parent.getId());
            }
            case Level10 -> {
                tagLeafInfoDO.setRootId(parent.getRootId());
                tagLeafInfoDO.setLevel2Id(parent.getLevel2Id());
                tagLeafInfoDO.setLevel3Id(parent.getLevel3Id());
                tagLeafInfoDO.setLevel4Id(parent.getLevel4Id());
                tagLeafInfoDO.setLevel5Id(parent.getLevel5Id());
                tagLeafInfoDO.setLevel6Id(parent.getLevel6Id());
                tagLeafInfoDO.setLevel7Id(parent.getLevel7Id());
                tagLeafInfoDO.setLevel8Id(parent.getLevel8Id());
                tagLeafInfoDO.setLevel9Id(parent.getLevel9Id());
                tagLeafInfoDO.setLevel10Id(parent.getId());
            }
            default -> throw new IllegalStateException("Set leaf level error Unexpected value: " + parent.getLevel());
        }
    }

    public List<TagLeafInfo> getTagLeafInfoWithOptions(TagLeafInfoQueryOption option) {
        if (option == null) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<TagLeafInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        if (CollUtil.isNotEmpty(option.getIds())) {
            lambdaQueryWrapper.in(TagLeafInfo::getId, option.getIds());
        }

        if (CollUtil.isNotEmpty(option.getNames())) {
            lambdaQueryWrapper.in(TagLeafInfo::getName, option.getNames());
        }

        if (CollUtil.isNotEmpty(option.getBusinessDomainEnumList())) {
            lambdaQueryWrapper.in(TagLeafInfo::getBusinessDomain, option.getBusinessDomainEnumList());
        }

        if (CollUtil.isNotEmpty(option.getTagDomainEnumList())) {
            lambdaQueryWrapper.in(TagLeafInfo::getTagDomain, option.getTagDomainEnumList());
        }

        if (CollUtil.isNotEmpty(option.getParentIds())) {
            lambdaQueryWrapper.in(TagLeafInfo::getParentId, option.getParentIds());
        }

        if (CollUtil.isNotEmpty(option.getRootIds())) {
            lambdaQueryWrapper.in(TagLeafInfo::getRootId, option.getRootIds());
        }

        if (CollUtil.isNotEmpty(option.getLevels())) {
            lambdaQueryWrapper.in(TagLeafInfo::getLevel, option.getLevels());
        }

        if (CollUtil.isNotEmpty(option.getTagCodes())) {
            lambdaQueryWrapper.in(TagLeafInfo::getCode, option.getTagCodes());
        }

        if (StrUtil.isNotBlank(option.getTagName())) {
            lambdaQueryWrapper.like(TagLeafInfo::getName, option.getTagName())
                    .or()
                    .likeRight(TagLeafInfo::getShortName, option.getTagName());
        }

        if (option.getIncludeDisable()) {
            lambdaQueryWrapper.in(TagLeafInfo::getIsEnabled, CollUtil.newArrayList(0, 1));
        } else {
            lambdaQueryWrapper.in(TagLeafInfo::getIsEnabled, 0);
        }

        lambdaQueryWrapper.in(TagLeafInfo::getIsDeleted, option.getIsIncludeDeleted() ? CollUtil.newArrayList(0, 1) : CollUtil.newArrayList(0));
        lambdaQueryWrapper.orderBy(true, option.getIsPosSortAsc(), TagLeafInfo::getPos);
        lambdaQueryWrapper.last("limit " + option.getPageSize());

        return iTagLeafInfoRepository.list(
                lambdaQueryWrapper
        );
    }

    /**
     * 根据标签叶子id查找标签叶子接口
     */
    public TagLeafInfo queryTagLeafInfoById(Long tagId, BaseTagQueryOption option) {
        if (tagId == null || tagId <= 0) {
            return null;
        }


        return iTagLeafInfoRepository.getOne(
                new LambdaQueryWrapper<TagLeafInfo>()
                        .eq(TagLeafInfo::getId, tagId)
                        .in(TagLeafInfo::getIsDeleted,
                                option.getIncludeDeleted() ? CollUtil.newArrayList(0, 1) : CollUtil.newArrayList(0))
                        .in(TagLeafInfo::getIsEnabled,
                                option.getIncludeDisable() ? CollUtil.newArrayList(0, 1) : CollUtil.newArrayList(0))
        );
    }

    /**
     * 根据标签BusinessDomain查找标签叶子接口
     */
    public Map<Integer, List<TagLeafInfoDto>> queryTagLeafInfoByBusinessDomain(List<Integer> businessDomainList, BaseTagQueryOption option) {
        if (CollUtil.isEmpty(businessDomainList)) {
            return MapUtil.newHashMap();
        }

        List<TagLeafInfo> tagLeafInfoList = iTagLeafInfoRepository.list(
                new LambdaQueryWrapper<TagLeafInfo>()
                        .in(TagLeafInfo::getBusinessDomain, businessDomainList)
                        .in(TagLeafInfo::getIsDeleted,
                                option.getIncludeDeleted() ? CollUtil.newArrayList(0, 1) : CollUtil.newArrayList(0))
                        .in(TagLeafInfo::getIsEnabled,
                                option.getIncludeDisable() ? CollUtil.newArrayList(0, 1) : CollUtil.newArrayList(0))
        );

        if (CollUtil.isEmpty(tagLeafInfoList)) {
            return MapUtil.newHashMap();
        }

        return tagLeafInfoList.stream().collect(
                HashMap::new,
                (map, tagLeafInfo) -> {
                    List<TagLeafInfoDto> list = map.computeIfAbsent(tagLeafInfo.getBusinessDomain(), k -> new ArrayList<>());
                    list.add(BeanUtil.copyProperties(tagLeafInfo, TagLeafInfoDto.class));
                },
                HashMap::putAll
        );

    }

    /**
     * 根据标签TagDomain查找标签叶子接口
     */
    public Map<String, List<TagLeafInfoDto>> queryTagLeafInfoByTagDomain(List<String> tagDomainList, BaseTagQueryOption option) {
        if (CollUtil.isEmpty(tagDomainList)) {
            return MapUtil.newHashMap();
        }

        List<TagLeafInfo> tagLeafInfoList = iTagLeafInfoRepository.list(
                new LambdaQueryWrapper<TagLeafInfo>()
                        .in(TagLeafInfo::getTagDomain, tagDomainList)
                        .in(TagLeafInfo::getIsDeleted,
                                option.getIncludeDeleted() ? CollUtil.newArrayList(0, 1) : CollUtil.newArrayList(0))
                        .in(TagLeafInfo::getIsEnabled,
                                option.getIncludeDisable() ? CollUtil.newArrayList(0, 1) : CollUtil.newArrayList(0))
        );

        if (CollUtil.isEmpty(tagLeafInfoList)) {
            return MapUtil.newHashMap();
        }

        return tagLeafInfoList.stream().collect(
                HashMap::new,
                (map, tagLeafInfo) -> {
                    List<TagLeafInfoDto> list = map.computeIfAbsent(tagLeafInfo.getTagDomain(), k -> new ArrayList<>());
                    list.add(BeanUtil.copyProperties(tagLeafInfo, TagLeafInfoDto.class));
                },
                HashMap::putAll
        );
    }

    public boolean updateEnableStatus(List<Long> tagLeafIdList, Integer status) {
        if (CollUtil.isEmpty(tagLeafIdList)
                || status == null
                || (status != 0 && status != 1)) {
            return false;
        }

        return iTagLeafInfoRepository.update(
                new LambdaUpdateWrapper<TagLeafInfo>()
                        .in(TagLeafInfo::getId, tagLeafIdList)
                        .set(TagLeafInfo::getIsEnabled, status)
        );
    }

    public List<TagLeafInfoDto> queryTagLeafInfoByBusinessDomainAndTagDomain(BusinessTreeTagQueryParam param, String tagDomain, BaseTagQueryOption option) {
        if (param == null || StrUtil.isBlank(tagDomain)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<TagLeafInfo> lambdaQueryWrapper = new LambdaQueryWrapper<TagLeafInfo>()
                .eq(TagLeafInfo::getBusinessDomain, param.getBusinessDomain())
                .eq(TagLeafInfo::getTagDomain, tagDomain)
                .in(TagLeafInfo::getIsDeleted,
                        option.getIncludeDeleted() ? CollUtil.newArrayList(0, 1) : CollUtil.newArrayList(0))
                .in(TagLeafInfo::getIsEnabled,
                        option.getIncludeDisable() ? CollUtil.newArrayList(0, 1) : CollUtil.newArrayList(0))
                .eq(StrUtil.isNotBlank(param.getOutId1()), TagLeafInfo::getOutId1, param.getOutId1())
                .eq(StrUtil.isNotBlank(param.getOutId2()), TagLeafInfo::getOutId2, param.getOutId2());

        List<TagLeafInfo> tagLeafInfoList = iTagLeafInfoRepository.list(lambdaQueryWrapper);

        if (CollUtil.isEmpty(tagLeafInfoList)) {
            return new ArrayList<>();
        }

        return BeanUtil.copyToList(tagLeafInfoList, TagLeafInfoDto.class);
    }

    /**
     * 批量查询标签信息，支持outId1、outId2、outId3的批量查询
     *
     * @param param 查询参数，包含outId1List、outId2List、outId3List
     * @param tagDomain 标签域
     * @param option 查询选项
     * @return Map<OutId, List<TagLeafInfoDto>> 按outId分组的标签信息
     */
    public Map<String, TagLeafInfoDto> batchQueryTagLeafInfoByBusinessDomainAndTagDomain(
            BusinessTreeTagQueryParam param, String tagDomain, BaseTagQueryOption option) {
        if (param == null || StrUtil.isBlank(tagDomain)) {
            return new HashMap<>();
        }

        // 构建基础查询条件
        LambdaQueryWrapper<TagLeafInfo> lambdaQueryWrapper = new LambdaQueryWrapper<TagLeafInfo>()
                .eq(TagLeafInfo::getBusinessDomain, param.getBusinessDomain())
                .eq(TagLeafInfo::getTagDomain, tagDomain)
                .in(TagLeafInfo::getIsDeleted,
                        option.getIncludeDeleted() ? CollUtil.newArrayList(0, 1) : CollUtil.newArrayList(0))
                .in(TagLeafInfo::getIsEnabled,
                        option.getIncludeDisable() ? CollUtil.newArrayList(0, 1) : CollUtil.newArrayList(0));

        // 添加批量查询条件
        boolean hasCondition = false;
        if (CollUtil.isNotEmpty(param.getOutId1List())) {
            lambdaQueryWrapper.in(TagLeafInfo::getOutId1, param.getOutId1List());
            hasCondition = true;
        }
        if (CollUtil.isNotEmpty(param.getOutId2List())) {
            lambdaQueryWrapper.in(TagLeafInfo::getOutId2, param.getOutId2List());
            hasCondition = true;
        }
        if (CollUtil.isNotEmpty(param.getOutId3List())) {
            lambdaQueryWrapper.in(TagLeafInfo::getOutId3, param.getOutId3List());
            hasCondition = true;
        }

        // 如果没有批量查询条件，返回空结果
        if (!hasCondition) {
            return new HashMap<>();
        }

        List<TagLeafInfo> tagLeafInfoList = iTagLeafInfoRepository.list(lambdaQueryWrapper);

        if (CollUtil.isEmpty(tagLeafInfoList)) {
            return new HashMap<>();
        }

        // 转换为DTO并按outId分组
        Map<String, TagLeafInfoDto> resultMap = new HashMap<>();

        for (TagLeafInfo tagLeafInfo : tagLeafInfoList) {
            TagLeafInfoDto dto = BeanUtil.copyProperties(tagLeafInfo, TagLeafInfoDto.class);

            // 根据不同的outId进行分组
            String groupKey = null;
            if (CollUtil.isNotEmpty(param.getOutId1List()) && StrUtil.isNotBlank(tagLeafInfo.getOutId1())) {
                groupKey = tagLeafInfo.getOutId1();
            } else if (CollUtil.isNotEmpty(param.getOutId2List()) && StrUtil.isNotBlank(tagLeafInfo.getOutId2())) {
                groupKey = tagLeafInfo.getOutId2();
            } else if (CollUtil.isNotEmpty(param.getOutId3List()) && StrUtil.isNotBlank(tagLeafInfo.getOutId3())) {
                groupKey = tagLeafInfo.getOutId3();
            }

            resultMap.put(groupKey, dto);
        }

        return resultMap;
    }
}
