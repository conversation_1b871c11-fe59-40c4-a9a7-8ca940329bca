package com.neo.tagcenter.app.logic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.api.code.GlobalResponseCodeEnum;
import com.neo.tagcenter.app.biz.option.TagLeafInfoQueryOption;
import com.neo.tagcenter.app.biz.option.TreeTagComplexQueryOption;
import com.neo.tagcenter.app.biz.option.TreeTagQuery;
import com.neo.tagcenter.app.constants.EnableStatus;
import com.neo.tagcenter.app.constants.LevelEnum;
import com.neo.tagcenter.app.vo.TreeTagBusinessDomainVO;
import com.neo.tagcenter.app.vo.TreeTagVO;
import com.neo.tagcenter.client.common.TagCenterResultTypeEnum;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;
import com.neo.tagcenter.client.dto.TreeTagDto;
import com.neo.tagcenter.client.param.*;
import com.neo.tagcenter.domain.entity.TagLeafBusinessDomain;
import com.neo.tagcenter.domain.entity.TagLeafInfo;
import com.neo.tagcenter.domain.gateway.ITagLeafBusinessDomainRepository;
import com.neo.tagcenter.domain.gateway.ITagLeafInfoRepository;
import com.neo.tagcenter.domain.util.TagConvert;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class TreeTagLogic {

    private static final Logger log = LoggerFactory.getLogger(TreeTagLogic.class);

    @Autowired
    private ITagLeafInfoRepository iTagLeafInfoRepository;

    @Autowired
    private ITagLeafBusinessDomainRepository iTagLeafBusinessDomainRepository;

    @Autowired
    private TagLeafInfoLogic tagLeafInfoLogic;

    public MultiResponse<TreeTagDto> queryTreeTagDto(TreeTagQuery query) {
        MultiResponse<TreeTagDto> response = MultiResponse.buildSuccess();


        List<TagLeafInfo> tagLeafInfoList = getTreeTagWithOptions(query);
        if (CollUtil.isEmpty(tagLeafInfoList)) {
            return response;
        }

        List<TreeTagDto> treeTagDtoList = convertTafLeafInfoDtoListToTree(BeanUtil.copyToList(tagLeafInfoList, TagLeafInfoDto.class));
        response.setData(treeTagDtoList);
        return response;
    }

    public MultiResponse<TreeTagVO> queryTreeTagVO(TreeTagVOQuery query) {
        MultiResponse<TreeTagVO> response = MultiResponse.buildSuccess();

        List<TagLeafInfo> tagLeafInfoList = getTreeTagWithOptions(convertTreeTagQuery(query));
        response.setData(convertTreeTagVO(tagLeafInfoList));
        return response;
    }

    private TreeTagQuery convertTreeTagQuery(TreeTagVOQuery query) {
        TreeTagQuery treeTagQuery = new TreeTagQuery();
        treeTagQuery.setBusinessDomain(query.getBusinessDomain());
        treeTagQuery.setTagDomain(query.getTagDomain());
        treeTagQuery.setTagLeafId(query.getTagLeafId());
        treeTagQuery.setIncludeDisable(query.getIncludeDisable());

        treeTagQuery.setTagName(query.getTagName());
        treeTagQuery.setTagCode(query.getTagCode());
        return treeTagQuery;
    }

    private List<TreeTagVO> convertTreeTagVO(List<TagLeafInfo> treeTagDtoList) {
        List<TreeTagVO> res = new ArrayList<>();
        if (CollUtil.isEmpty(treeTagDtoList)) {
            return res;
        }
        for (TagLeafInfo tagLeafInfo : treeTagDtoList) {
            TreeTagVO treeTagVO = new TreeTagVO();
            BeanUtil.copyProperties(tagLeafInfo, treeTagVO);
            treeTagVO.setKey(String.valueOf(tagLeafInfo.getId()));
            treeTagVO.setTitle(tagLeafInfo.getName());

            res.add(treeTagVO);
        }
        return res;
    }


    public List<TreeTagDto> convertTafLeafInfoDtoListToTree(List<TagLeafInfoDto> tagLeafInfoDtoList) {
        // 创建根节点
        List<TreeTagDto> rootNodeList = new ArrayList<>();

        List<TagLeafInfoDto> topLevelTagList = new ArrayList<>();

        // 找到根节点
        for (TagLeafInfoDto item : tagLeafInfoDtoList) {
            if (item.getRootId() == 0) {
                topLevelTagList.add(item);
            }
        }

        // 如果没有根节点，获取最大level的节点作为根节点
        if (CollUtil.isEmpty(topLevelTagList)) {
            int maxLevel = tagLeafInfoDtoList.stream().mapToInt(TagLeafInfoDto::getLevel).min().orElse(0);
            for (TagLeafInfoDto item : tagLeafInfoDtoList) {
                if (item.getLevel() == maxLevel) {
                    topLevelTagList.add(item);
                }
            }
        }

        for (TagLeafInfoDto item : topLevelTagList) {
            TreeTagDto rootNode = createNode(item, tagLeafInfoDtoList);
            rootNodeList.add(rootNode);
        }

        // 根据pos排序
        rootNodeList.sort(
                Comparator.comparing((TreeTagDto v) -> v.getTagLeafInfoDto().getPos())
        );

        return rootNodeList;
    }

    private TreeTagDto createNode(TagLeafInfoDto tagLeafInfoDto, List<TagLeafInfoDto> tagLeafInfoDtoList) {
        TreeTagDto node = new TreeTagDto();
        node.setTagLeafInfoDto(tagLeafInfoDto);

        for (TagLeafInfoDto child : findChildren(tagLeafInfoDto, tagLeafInfoDtoList)) {
            node.getChildren().add(createNode(child, tagLeafInfoDtoList));
        }

        return node;
    }

    private List<TagLeafInfoDto> findChildren(TagLeafInfoDto parent, List<TagLeafInfoDto> tagLeafInfoDtoList) {
        List<TagLeafInfoDto> children = new ArrayList<>();
        for (TagLeafInfoDto tagLeafInfoDto : tagLeafInfoDtoList) {
            if (tagLeafInfoDto.getParentId() != null && tagLeafInfoDto.getParentId().equals(parent.getId())) {
                children.add(tagLeafInfoDto);
            }
        }
        children.sort(
                Comparator.comparing(TagLeafInfoDto::getPos)
        );
        return children;
    }

    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<Boolean> addTreeTag(TreeTagSaveParam treeTagSaveParam) {
        SingleResponse<Long> result = doAddTreeTag(treeTagSaveParam);
        if (!result.isSuccess()) {
            return SingleResponse.buildFailure(result.getErrCode(), result.getErrMessage());
        }
        return SingleResponse.buildSuccess(result.getData() != null && result.getData() > 0);
    }

    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<Long> addTreeTagReturnId(TreeTagSaveParam treeTagSaveParam) {
        return doAddTreeTag(treeTagSaveParam);
    }

    // 公共逻辑，不加事务
    private SingleResponse<Long> doAddTreeTag(TreeTagSaveParam treeTagSaveParam) {
        // 参数校验
        String errorMsg = treeTagSaveParam.validate();
        if (StrUtil.isNotBlank(errorMsg)) {
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR.getCode(), errorMsg);
        }

        TagLeafBusinessDomain tagLeafBusinessDomain = getTagLeafBussinessDomain(
                treeTagSaveParam.getBusinessDomain());
        if (tagLeafBusinessDomain == null) {
            return SingleResponse.buildFailure(TagCenterResultTypeEnum.TAG_BUSSINESS_DOMAIN_NOT_EXISTS);
        }

/*        TagLeafInfoDto tagLeafInfoDTOOld = getTagLeafByName(
                treeTagSaveParam.getBusinessDomain(),
                treeTagSaveParam.getTagDomain(),
                treeTagSaveParam.getName());
        if (tagLeafInfoDTOOld != null) {
            return SingleResponse.buildFailure(TagCenterResultTypeEnum.TAG_LEAF_ALREADY_EXISTS);
        }*/

        return SingleResponse.buildSuccess(addChildTag(treeTagSaveParam));
    }


    private TagLeafBusinessDomain getTagLeafBussinessDomain(Integer businessDomain) {
        return iTagLeafBusinessDomainRepository.getOne(
                new LambdaQueryWrapper<TagLeafBusinessDomain>()
                        .eq(TagLeafBusinessDomain::getCode, businessDomain)
                        .eq(TagLeafBusinessDomain::getIsDeleted, 0)
        );
    }

    /**
     * 添加子节点或者兄弟节点
     */
    private Long addChildTag(TreeTagSaveParam treeTagSaveParam) {
        if (treeTagSaveParam == null) {
            return -1L;
        }

        TagLeafInfoDto tagLeafInfoDto = BeanUtil.copyProperties(treeTagSaveParam, TagLeafInfoDto.class);
        // 设置pos，根节点设置为1，子节点设置最大子节点pos + 1
        tagLeafInfoDto.setPos(getMaxChildPos(treeTagSaveParam.getParentId()) + 1);
        return tagLeafInfoLogic.insert(tagLeafInfoDto);
    }

    /**
     * 获取该节点下的最大子节点pos
     */
    private Integer getMaxChildPos(Long parentId) {
        if (parentId == null || parentId < 0) {
            throw new IllegalArgumentException("参数错误");
        }

        TagLeafInfo tagLeafInfo = iTagLeafInfoRepository.getOne(
                new LambdaQueryWrapper<TagLeafInfo>()
                        .eq(TagLeafInfo::getParentId, parentId)
                        .orderByDesc(TagLeafInfo::getPos)
                        .last("limit 1")
        );

        return tagLeafInfo == null ? 0 : tagLeafInfo.getPos();
    }


    /**
     * 根据标签名查标签叶子
     *
     * @param businessDomain
     * @param tagDomain
     * @param name
     * @return
     */
    public TagLeafInfoDto getTagLeafByName(Integer businessDomain, String tagDomain, String name) {
        TagLeafInfo tagLeafInfo = iTagLeafInfoRepository.getOne(
                new LambdaQueryWrapper<TagLeafInfo>()
                        .eq(TagLeafInfo::getBusinessDomain, businessDomain)
                        .eq(TagLeafInfo::getTagDomain, tagDomain)
                        .eq(TagLeafInfo::getName, name)
                        .eq(TagLeafInfo::getIsDeleted, 0)
        );

        return tagLeafInfo != null ? TagConvert.fromDO(tagLeafInfo) : null;
    }


    public SingleResponse<Boolean> deleteTreeTagWithinChildren(Long id, long updater) {
        if (id == null || id <= 0L) {
            log.warn("Parameter error id:{}", id);
            return SingleResponse.buildFailure(Boolean.FALSE);
        }
        List<TagLeafInfoDto> tagLeafInfoDTOList = getLeavesByQueryAllLeaveId(id);
        if (CollectionUtils.isEmpty(tagLeafInfoDTOList)) {
            log.info("delete tag leaf is not exist id:{}", id);
            return SingleResponse.buildSuccess(deleteTagLeaf(id));
        }
        List<Long> ids = tagLeafInfoDTOList
                .stream().map(TagLeafInfoDto::getId)
                .collect(Collectors.toList());
        log.info("delete tag leaf ids:{}", JSON.toJSONString(ids));
        boolean res = iTagLeafInfoRepository.update(
                new LambdaUpdateWrapper<TagLeafInfo>()
                        .set(TagLeafInfo::getUpdater, updater)
                        .set(TagLeafInfo::getIsDeleted, 1)
                        .set(TagLeafInfo::getUpdated, new Date().getTime())
                        .in(TagLeafInfo::getId, ids)
        );
        return SingleResponse.buildSuccess(res);
    }

    /**
     * 删除标签叶子接口
     */
    public Boolean deleteTagLeaf(Long id) {
        if (Objects.isNull(id) || id <= 0L) {
            return false;
        }
        TagLeafInfo tagLeafInfoDO = new TagLeafInfo();
        tagLeafInfoDO.setId(id);
        tagLeafInfoDO.setIsDeleted(1);
        return iTagLeafInfoRepository.updateById(tagLeafInfoDO);
    }

    /**
     * 根据根节点获取这棵树的所有叶子节点列表
     *
     * @param queryAllLeaveId
     * @return
     */
    public List<TagLeafInfoDto> getLeavesByQueryAllLeaveId(Long queryAllLeaveId) {
        //全部父节点
        SingleResponse<TagLeafInfoDto> response = queryTagLeafInfoById(queryAllLeaveId, new BaseTagQueryOption());
        if (!response.isSuccess() || response.getData() == null) {
            return Collections.emptyList();
        }

        List<TagLeafInfo> allLeaves = getAllLeavesByQueryAllLeaveId(queryAllLeaveId);
        return allLeaves.stream().map(TagConvert::fromDO).collect(Collectors.toList());
    }


    /**
     * 根据当前节点获取其全部子节点，包含自身
     */
    private List<TagLeafInfo> getAllLeavesByQueryAllLeaveId(Long queryAllLeaveId) {
        if (queryAllLeaveId == null || queryAllLeaveId <= 0L) {
            return Collections.emptyList();
        }
        List<Long> queryAllLeaveIds = new ArrayList<>();
        queryAllLeaveIds.add(queryAllLeaveId);
        Map<Long, List<TagLeafInfo>> rootIdTagLeafInfoMap = getBatchAllLeavesByQueryAllLeaveIds(queryAllLeaveIds);
        if (!CollectionUtils.isEmpty(rootIdTagLeafInfoMap) && rootIdTagLeafInfoMap.containsKey(queryAllLeaveId)) {
            return rootIdTagLeafInfoMap.get(queryAllLeaveId);
        }
        return Collections.emptyList();
    }

    /**
     * 批量根据当前id获取全部子节点
     *
     * @param queryAllLeaveIds 根节点列表
     */
    private Map<Long, List<TagLeafInfo>> getBatchAllLeavesByQueryAllLeaveIds(List<Long> queryAllLeaveIds) {
        // 批量查询全部节点，接口限制为每次查500个所以写个算法 多查询几次
        LambdaQueryWrapper<TagLeafInfo> wrapper = new LambdaQueryWrapper<>();
        if (CollUtil.isNotEmpty(queryAllLeaveIds)) {
            wrapper.and(
                    qw -> qw.eq(TagLeafInfo::getIsDeleted, 0)
                            .in(TagLeafInfo::getId, queryAllLeaveIds)
                            .or()
                            .in(TagLeafInfo::getRootId, queryAllLeaveIds)
                            .or()
                            .in(TagLeafInfo::getLevel2Id, queryAllLeaveIds)
                            .or()
                            .in(TagLeafInfo::getLevel3Id, queryAllLeaveIds)
                            .or()
                            .in(TagLeafInfo::getLevel4Id, queryAllLeaveIds)
                            .or()
                            .in(TagLeafInfo::getLevel5Id, queryAllLeaveIds)
                            .or()
                            .in(TagLeafInfo::getLevel6Id, queryAllLeaveIds)
                            .or()
                            .in(TagLeafInfo::getLevel7Id, queryAllLeaveIds)
                            .or()
                            .in(TagLeafInfo::getLevel8Id, queryAllLeaveIds)
                            .or()
                            .in(TagLeafInfo::getLevel9Id, queryAllLeaveIds)
            );
        }
        wrapper.orderByAsc(TagLeafInfo::getPos);
        List<TagLeafInfo> tagLeafInfoList = iTagLeafInfoRepository.list(
                wrapper
        );

        Map<Long, List<TagLeafInfo>> sonTreeRootIdTagLeafMap = new HashMap<>();
        for (TagLeafInfo tagLeafInfoDO : tagLeafInfoList) {
            if (tagLeafInfoDO == null) {
                continue;
            }
            Long id = tagLeafInfoDO.getId();
            addToSonTreeMap(id, tagLeafInfoDO, queryAllLeaveIds, sonTreeRootIdTagLeafMap);
            Long rootId = tagLeafInfoDO.getRootId();
            addToSonTreeMap(rootId, tagLeafInfoDO, queryAllLeaveIds, sonTreeRootIdTagLeafMap);

            Long level2Id = tagLeafInfoDO.getLevel2Id();
            addToSonTreeMap(level2Id, tagLeafInfoDO, queryAllLeaveIds, sonTreeRootIdTagLeafMap);

            Long level3Id = tagLeafInfoDO.getLevel3Id();
            addToSonTreeMap(level3Id, tagLeafInfoDO, queryAllLeaveIds, sonTreeRootIdTagLeafMap);

            Long level4Id = tagLeafInfoDO.getLevel4Id();
            addToSonTreeMap(level4Id, tagLeafInfoDO, queryAllLeaveIds, sonTreeRootIdTagLeafMap);

            Long level5Id = tagLeafInfoDO.getLevel5Id();
            addToSonTreeMap(level5Id, tagLeafInfoDO, queryAllLeaveIds, sonTreeRootIdTagLeafMap);

            Long level6Id = tagLeafInfoDO.getLevel6Id();
            addToSonTreeMap(level6Id, tagLeafInfoDO, queryAllLeaveIds, sonTreeRootIdTagLeafMap);

            Long level7Id = tagLeafInfoDO.getLevel7Id();
            addToSonTreeMap(level7Id, tagLeafInfoDO, queryAllLeaveIds, sonTreeRootIdTagLeafMap);

            Long level8Id = tagLeafInfoDO.getLevel8Id();
            addToSonTreeMap(level8Id, tagLeafInfoDO, queryAllLeaveIds, sonTreeRootIdTagLeafMap);

            Long level9Id = tagLeafInfoDO.getLevel9Id();
            addToSonTreeMap(level9Id, tagLeafInfoDO, queryAllLeaveIds, sonTreeRootIdTagLeafMap);

            Long level10Id = tagLeafInfoDO.getLevel10Id();
            addToSonTreeMap(level10Id, tagLeafInfoDO, queryAllLeaveIds, sonTreeRootIdTagLeafMap);
        }
        return sonTreeRootIdTagLeafMap;
    }

    /**
     * 将查到的叶子结点，根据子树根节点添加到map里
     *
     * @param id                      当前叶子节点的判断id,可能是叶子节点的id,queryAllLeaveId,level2Id,level3Id,level4Id,level5Id
     * @param tagLeafInfoDO           当前节点
     * @param queryAllLeaveIds        要查询的子树根节点
     * @param sonTreeRootIdTagLeafMap 返回的子数根节点，节点的map
     */
    private void addToSonTreeMap(Long id, TagLeafInfo tagLeafInfoDO, List<Long> queryAllLeaveIds, Map<Long, List<TagLeafInfo>> sonTreeRootIdTagLeafMap) {
        if (id == null || id <= 0L || CollUtil.isEmpty(queryAllLeaveIds) || sonTreeRootIdTagLeafMap == null) {
            return;
        }
        /**
         * 非关心的子树rootId,不用关心该节点
         */
        if (!queryAllLeaveIds.contains(id)) {
            return;
        }
        List<TagLeafInfo> sonTreeLeaves = sonTreeRootIdTagLeafMap.get(id);
        if (sonTreeLeaves == null) {
            sonTreeLeaves = new ArrayList<>();
        }
        sonTreeLeaves.add(tagLeafInfoDO);
        sonTreeRootIdTagLeafMap.put(id, sonTreeLeaves);
    }

    /**
     * 用于获取树形标签 或者 叶子标签
     */
    public List<TagLeafInfo> getTreeTagWithOptions(TreeTagQuery query) {
        // 查询标签叶子
        TreeTagComplexQueryOption treeTagComplexQueryOption = convertTreeTagQueryOption(query);
        List<TagLeafInfo> tagLeafInfoList = getTagLeafListByTreeTagQueryOption(treeTagComplexQueryOption);
        if (CollUtil.isEmpty(tagLeafInfoList)) {
            return Collections.emptyList();
        }

        // 查询标签节点是否为叶子节点
        Map<Long, Boolean> isLeafMap = queryisLeafMap(tagLeafInfoList, query.getIncludeDisable());
        for (TagLeafInfo tagLeafInfo : tagLeafInfoList) {
            tagLeafInfo.setLeaf(isLeafMap.getOrDefault(tagLeafInfo.getId(), Boolean.FALSE));
        }

        // 查询标签子树（标签id）
        if (query.getQueryChild()) {
            List<TagLeafInfo> childList = Lists.newArrayList();
            for (TagLeafInfo tagLeafInfo : tagLeafInfoList) {
                List<TagLeafInfo> childTagLeafInfoList = getAllChildTagLeafInfoList(tagLeafInfo.getId(), query.getIncludeDisable() ? EnableStatus.ALL : EnableStatus.ENABLE);
                // 合并
                childList.addAll(childTagLeafInfoList);
            }
            tagLeafInfoList.addAll(childList);
            // 去重，因为结果会拼接重复的数据
            if (CollUtil.isNotEmpty(tagLeafInfoList)) {
                tagLeafInfoList = tagLeafInfoList.stream()
                        .collect(Collectors.toMap(TagLeafInfo::getId, Function.identity(), (oldValue, newValue) -> oldValue))
                        .values().stream()
                        .toList();
            }
        }

        return tagLeafInfoList;
    }

    public Map<Long, List<TagLeafInfo>> getTreeTagWithOptionsReturnMap(TreeTagQuery query) {
        Map<Long, List<TagLeafInfo>> tagLeafInfoMap = new HashMap<>();
        // 查询标签叶子
        TreeTagComplexQueryOption treeTagComplexQueryOption = convertTreeTagQueryOption(query);
        List<TagLeafInfo> tagLeafInfoList = getTagLeafListByTreeTagQueryOption(treeTagComplexQueryOption);
        if (CollUtil.isEmpty(tagLeafInfoList)) {
            return new HashMap<>();
        }

        // 查询标签节点是否为叶子节点
        Map<Long, Boolean> isLeafMap = queryisLeafMap(tagLeafInfoList, query.getIncludeDisable());
        for (TagLeafInfo tagLeafInfo : tagLeafInfoList) {
            tagLeafInfo.setLeaf(isLeafMap.getOrDefault(tagLeafInfo.getId(), Boolean.FALSE));
        }

        // 查询标签子树（标签id）
        if (query.getQueryChild()) {
            for (TagLeafInfo tagLeafInfo : tagLeafInfoList) {
                List<TagLeafInfo> childTagLeafInfoList = getAllChildTagLeafInfoList(tagLeafInfo.getId(), query.getIncludeDisable() ? EnableStatus.ALL : EnableStatus.ENABLE);
                // 合并
                // 去重，因为结果会拼接重复的数据
                childTagLeafInfoList = childTagLeafInfoList.stream()
                        .collect(Collectors.toMap(TagLeafInfo::getId, Function.identity(), (oldValue, newValue) -> oldValue))
                        .values().stream()
                        .toList();

                tagLeafInfoMap.put(tagLeafInfo.getId(), childTagLeafInfoList);
            }
        } else {
            tagLeafInfoMap.put(tagLeafInfoList.get(0).getId(), tagLeafInfoList);
        }

        return tagLeafInfoMap;
    }

    /**
     * 查询标签节点是否为叶子节点
     */
    private Map<Long, Boolean> queryisLeafMap(List<TagLeafInfo> tagLeafInfoList, Boolean includeDisable) {
        if (CollUtil.isEmpty(tagLeafInfoList)) {
            return MapUtil.newHashMap();
        }
        List<Long> tagLeafIds = tagLeafInfoList.stream().map(TagLeafInfo::getId).collect(Collectors.toList());
        List<TagLeafInfo> isLeafList = iTagLeafInfoRepository.list(
                new QueryWrapper<TagLeafInfo>()
                        .in("parentId", tagLeafIds)
                        .in("isEnabled", includeDisable ? List.of(0, 1) : List.of(0))
                        .eq("isDeleted", 0));
        if (CollUtil.isEmpty(isLeafList)) {
            return MapUtil.newHashMap();
        }

        List<Long> pidList = isLeafList.stream().map(TagLeafInfo::getParentId).toList();

        Map<Long, Boolean> isLeafMap = new HashMap<>();
        for (TagLeafInfo tagLeafInfo : tagLeafInfoList) {
            if (pidList.contains(tagLeafInfo.getId())) {
                isLeafMap.put(tagLeafInfo.getId(), Boolean.TRUE);
            }
        }

        return isLeafMap;
    }

    /**
     * 查询标签子树（标签id）
     * status 0为启用，1为禁用，2为全部
     */
    private List<TagLeafInfo> getAllChildTagLeafInfoList(Long tagLeafId, EnableStatus status) {
        List<TagLeafInfo> childTagLeafInfoList = new ArrayList<>();

        // 根据tagLeafId查询子树
        if (tagLeafId == null) {
            return childTagLeafInfoList;
        }

        BaseTagQueryOption option = new BaseTagQueryOption();
        option.setIncludeDisable(status == EnableStatus.ALL || status == EnableStatus.DISABLE);
        TagLeafInfo tagLeafInfo = tagLeafInfoLogic.queryTagLeafInfoById(tagLeafId, option);
        LevelEnum levelEnum = LevelEnum.getLevelEnum(tagLeafInfo.getLevel());
        if (levelEnum == null) {
            return childTagLeafInfoList;
        }

        // 查询当前id下的子树
        childTagLeafInfoList = iTagLeafInfoRepository.list(
                new QueryWrapper<TagLeafInfo>()
                        .eq(levelEnum.getDbField(), tagLeafInfo.getId())
                        .in("isEnabled", status == EnableStatus.ALL ?
                                Arrays.asList(0, 1) : Collections.singletonList(status.getValue()))
                        .eq("isDeleted", 0)
        );

        return childTagLeafInfoList;
    }

    /**
     * 查询标签父节点
     * status 0为启用，1为禁用，2为全部
     */
    private List<TagLeafInfo> getAllParentTagLeafInfoList(Long tagLeafId, EnableStatus status) {
        List<TagLeafInfo> parentTagLeafInfoList = new ArrayList<>();
        if (tagLeafId == null || tagLeafId <= 0L) {
            return parentTagLeafInfoList;
        }

        TagLeafInfo tagLeafInfo = tagLeafInfoLogic.queryTagLeafInfoById(tagLeafId, new BaseTagQueryOption());
        if (tagLeafInfo == null) {
            return parentTagLeafInfoList;
        }

        // 查询标签父节点
        List<Long> pIdList = new ArrayList<>();
        pIdList.add(tagLeafInfo.getRootId());
        pIdList.add(tagLeafInfo.getLevel2Id());
        pIdList.add(tagLeafInfo.getLevel3Id());
        pIdList.add(tagLeafInfo.getLevel4Id());
        pIdList.add(tagLeafInfo.getLevel5Id());
        pIdList.add(tagLeafInfo.getLevel6Id());
        pIdList.add(tagLeafInfo.getLevel7Id());
        pIdList.add(tagLeafInfo.getLevel8Id());
        pIdList.add(tagLeafInfo.getLevel9Id());
        pIdList.add(tagLeafInfo.getLevel10Id());

        // 去掉0
        pIdList.removeIf(i -> i == 0);
        if (CollUtil.isEmpty(pIdList)) {
            return parentTagLeafInfoList;
        }


        parentTagLeafInfoList = iTagLeafInfoRepository.list(
                new QueryWrapper<TagLeafInfo>()
                        .in("id", pIdList)
                        .in("isEnabled", status == EnableStatus.ALL ?
                                Arrays.asList(0, 1) : Collections.singletonList(status.getValue()))
                        .eq("isDeleted", 0));

        return parentTagLeafInfoList;
    }

    /**
     * 转换树标签查询参数
     */
    private TreeTagComplexQueryOption convertTreeTagQueryOption(TreeTagQuery query) {
        TreeTagComplexQueryOption treeTagComplexQueryOption = new TreeTagComplexQueryOption();
        if (query.getBusinessDomain() != null) {
            treeTagComplexQueryOption.setBusinessDomainEnumList(Collections.singletonList(query.getBusinessDomain()));
        }

        if (query.getTagDomain() != null) {
            treeTagComplexQueryOption.setTagDomainEnumList(Collections.singletonList(query.getTagDomain()));
        }

        if (query.getTagLeafId() != null) {
            treeTagComplexQueryOption.setTagLeafIds(Collections.singletonList(query.getTagLeafId()));
        }

        if (CollUtil.isNotEmpty(query.getTagLeafIds())) {
            treeTagComplexQueryOption.setTagLeafIds(query.getTagLeafIds());
        }

        if (query.getTagName() != null) {
            treeTagComplexQueryOption.setTagName(query.getTagName());
        }

        if (query.getTagCode() != null) {
            treeTagComplexQueryOption.setTagCodes(Collections.singletonList(query.getTagCode()));
        }

        treeTagComplexQueryOption.setIncludeDeleted(query.getIncludeDeleted());
        treeTagComplexQueryOption.setIncludeDisable(query.getIncludeDisable());

        return treeTagComplexQueryOption;
    }


    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<Boolean> saveRank(SaveRankModifyParam param) {
        if (param == null || CollUtil.isEmpty(param.getTagLeafIdList())) {
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }

        int rank = 1;
        for (Long id : param.getTagLeafIdList()) {
            TagLeafInfo tagLeafInfo = new TagLeafInfo();
            tagLeafInfo.setId(id);
            tagLeafInfo.setPos(rank++);
            iTagLeafInfoRepository.updateById(tagLeafInfo);
        }
        return SingleResponse.buildSuccess(true);

    }

    /**
     * 根据树标签查询参数查找标签叶子
     */
    public List<TagLeafInfo> getTagLeafListByTreeTagQueryOption(TreeTagComplexQueryOption option) {
        if (option == null) {
            return CollUtil.newArrayList();
        }

        TagLeafInfoQueryOption tagLeafInfoQueryOption = new TagLeafInfoQueryOption();

        if (CollUtil.isNotEmpty(option.getBusinessDomainEnumList())) {
            tagLeafInfoQueryOption.setBusinessDomainEnumList(option.getBusinessDomainEnumList());
        }

        if (CollUtil.isNotEmpty(option.getTagDomainEnumList())) {
            tagLeafInfoQueryOption.setTagDomainEnumList(option.getTagDomainEnumList());
        }

        if (CollUtil.isNotEmpty(option.getTagLeafIds())) {
            tagLeafInfoQueryOption.setIds(option.getTagLeafIds());
        }

        if (CollUtil.isNotEmpty(option.getParentTagLeafIds())) {
            tagLeafInfoQueryOption.setParentIds(option.getParentTagLeafIds());
        }

        if (CollUtil.isNotEmpty(option.getRootTagLeafIds())) {
            tagLeafInfoQueryOption.setRootIds(option.getRootTagLeafIds());
        }

        if (CollUtil.isNotEmpty(option.getTagCodes())) {
            tagLeafInfoQueryOption.setTagCodes(option.getTagCodes());
        }

        if (StrUtil.isNotBlank(option.getTagName())) {
            tagLeafInfoQueryOption.setTagName(option.getTagName());
        }

        tagLeafInfoQueryOption.setIncludeDisable(option.getIncludeDisable());
        tagLeafInfoQueryOption.setIsIncludeDeleted(option.getIncludeDeleted());
        tagLeafInfoQueryOption.setPageSize(2000);
        return tagLeafInfoLogic.getTagLeafInfoWithOptions(tagLeafInfoQueryOption);
    }

    public SingleResponse<TagLeafInfoDto> queryTagLeafInfoById(Long tagId, BaseTagQueryOption option) {
        if (tagId == null || tagId <= 0L) {
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }

        TagLeafInfo tagLeafInfo = tagLeafInfoLogic.queryTagLeafInfoById(tagId, option);
        if (tagLeafInfo == null) {
            return SingleResponse.buildSuccess();
        }

        return SingleResponse.buildSuccess(TagConvert.fromDO(tagLeafInfo));
    }

    /**
     * 树标签业务域展示
     */
    public MultiResponse<TreeTagBusinessDomainVO> queryBusinessDomain() {
        List<TreeTagBusinessDomainVO> treeTagBusinessDomainVOList = new ArrayList<>();
        List<TagLeafBusinessDomain> tagLeafBusinessDomainList = iTagLeafBusinessDomainRepository.list(
                new LambdaQueryWrapper<TagLeafBusinessDomain>()
                        .eq(TagLeafBusinessDomain::getIsDeleted, 0)
        );

        for (TagLeafBusinessDomain businessDomain : tagLeafBusinessDomainList) {
            TreeTagBusinessDomainVO treeTagBusinessDomainVO = new TreeTagBusinessDomainVO();
            treeTagBusinessDomainVO.setCode(businessDomain.getCode());
            treeTagBusinessDomainVO.setBusinessDomainName(businessDomain.getName());
            treeTagBusinessDomainVOList.add(treeTagBusinessDomainVO);
        }
        return MultiResponse.of(treeTagBusinessDomainVOList);
    }

    /**
     * 启用 会将该节点下的所有子节点都启用 并且 会把他的所有父节点启用
     * 禁用标签叶子以及其子节点
     */
    public SingleResponse<Boolean> enableDisableFeature(EnableDisableFeatureParam param) {
        if (param == null || param.getTagLeafId() == null || param.getTagLeafId() <= 0L) {
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }

        // 查询标签叶子
        BaseTagQueryOption option = new BaseTagQueryOption();
        option.setIncludeDisable(true);
        TagLeafInfo tagLeafInfo = tagLeafInfoLogic.queryTagLeafInfoById(param.getTagLeafId(), option);
        if (tagLeafInfo == null) {
            return SingleResponse.buildFailure(TagCenterResultTypeEnum.TAG_LEAF_NOT_EXISTS);
        }

        // 标签叶子状态有无变更
        if (tagLeafInfo.getIsEnabled() == param.getStatus()) {
            return SingleResponse.buildFailure(TagCenterResultTypeEnum.TAG_LEAF_STATUS_NOT_CHANGE);
        }

        List<Long> tagLeafIdList = CollUtil.newArrayList(param.getTagLeafId());

        // 启用
        if (param.getStatus() == EnableStatus.ENABLE.getValue()) {
            // 查询所有 禁用 父节点
            List<TagLeafInfo> parentTagLeafInfoList = getAllParentTagLeafInfoList(param.getTagLeafId(), EnableStatus.DISABLE);
            if (CollUtil.isNotEmpty(parentTagLeafInfoList)) {
                tagLeafIdList.addAll(parentTagLeafInfoList.stream().map(TagLeafInfo::getId).toList());
            }

            // 查询所有 禁用 子节点
            List<TagLeafInfo> childTagLeafInfoList = getAllChildTagLeafInfoList(param.getTagLeafId(), EnableStatus.DISABLE);
            if (CollUtil.isNotEmpty(childTagLeafInfoList)) {
                tagLeafIdList.addAll(childTagLeafInfoList.stream().map(TagLeafInfo::getId).toList());
            }

            // 禁用
        } else if (param.getStatus() == EnableStatus.DISABLE.getValue()) {
            // 查询所有 启用 子节点
            List<TagLeafInfo> childTagLeafInfoList = getAllChildTagLeafInfoList(param.getTagLeafId(), EnableStatus.ENABLE);
            if (CollUtil.isNotEmpty(childTagLeafInfoList)) {
                tagLeafIdList.addAll(childTagLeafInfoList.stream().map(TagLeafInfo::getId).toList());
            }
        }

        boolean res = tagLeafInfoLogic.updateEnableStatus(tagLeafIdList, param.getStatus());
        return SingleResponse.buildSuccess(res);
    }

    public List<TagLeafInfoDto> queryTagLeafInfo(TreeTagQueryOption treeTagQueryOption) {
        TreeTagComplexQueryOption treeTagComplexQueryOption = new TreeTagComplexQueryOption();
        treeTagComplexQueryOption.setIncludeDisable(treeTagQueryOption.getIncludeDisable());
        treeTagComplexQueryOption.setIncludeDeleted(treeTagQueryOption.getIncludeDeleted());
        treeTagComplexQueryOption.setTagLeafIds(treeTagQueryOption.getTagLeafIds());
        treeTagComplexQueryOption.setParentTagLeafIds(treeTagQueryOption.getParentTagLeafIds());
        treeTagComplexQueryOption.setRootTagLeafIds(treeTagQueryOption.getRootTagLeafIds());
        treeTagComplexQueryOption.setBusinessDomainEnumList(treeTagQueryOption.getBusinessDomains());
        treeTagComplexQueryOption.setTagDomainEnumList(treeTagQueryOption.getTagDomains());

        List<TagLeafInfo> tagLeafInfoList = getTagLeafListByTreeTagQueryOption(treeTagComplexQueryOption);

        return BeanUtil.copyToList(tagLeafInfoList, TagLeafInfoDto.class);
    }

    /**
     * 更新标签叶子
     */
    public SingleResponse<Boolean> updateTagLeaf(TreeTagUpdateParam treeTagUpdateParam) {
        if (treeTagUpdateParam == null || treeTagUpdateParam.getTagLeafId() == null) {
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }

        TagLeafInfo tagLeafInfo = tagLeafInfoLogic.queryTagLeafInfoById(treeTagUpdateParam.getTagLeafId(), new BaseTagQueryOption());
        if (tagLeafInfo == null) {
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }

        // 更新标签叶子信息
        boolean res = iTagLeafInfoRepository.update(
                new LambdaUpdateWrapper<TagLeafInfo>()
                        .set(treeTagUpdateParam.getCode() != null,
                                TagLeafInfo::getCode, treeTagUpdateParam.getCode())
                        .set(StrUtil.isNotBlank(treeTagUpdateParam.getName()),
                                TagLeafInfo::getName, treeTagUpdateParam.getName())
                        .set(treeTagUpdateParam.getShortName() != null,
                                TagLeafInfo::getShortName, treeTagUpdateParam.getShortName())
                        .set(treeTagUpdateParam.getExtra() != null,
                                TagLeafInfo::getExtra, treeTagUpdateParam.getExtra())
                        .set(treeTagUpdateParam.getDescription() != null,
                                TagLeafInfo::getDescription, treeTagUpdateParam.getDescription())

                        .set(treeTagUpdateParam.getParentId() != null,
                                TagLeafInfo::getParentId, treeTagUpdateParam.getParentId())
                        .eq(TagLeafInfo::getId, treeTagUpdateParam.getTagLeafId())
                        .eq(TagLeafInfo::getIsDeleted, 0)
        );

        if (!res) {
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.FAIL);
        }

        List<TagLeafInfoDto> tagLeafInfoDTOList = getLeavesByQueryAllLeaveId(treeTagUpdateParam.getTagLeafId());
        if (CollectionUtils.isEmpty(tagLeafInfoDTOList)) {
            return SingleResponse.buildSuccess();
        }

        // 获取所有叶子节点id
        Set<Long> ids = tagLeafInfoDTOList.stream()
                .map(TagLeafInfoDto::getId)
                .filter(id -> id.longValue() != treeTagUpdateParam.getTagLeafId())
                .collect(Collectors.toSet());

        LevelEnum levelEnum = LevelEnum.getLevelEnum(tagLeafInfo.getLevel());
        if (levelEnum == null) {
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }

        res = iTagLeafInfoRepository.update(
                new UpdateWrapper<TagLeafInfo>()
                        .set(levelEnum.getDbField(), treeTagUpdateParam.getTagLeafId())
                        .set("updated", new Date().getTime())
                        .in("id", ids)
                        .eq("isDeleted", 0)
        );


        return res ? SingleResponse.buildSuccess() : SingleResponse.buildFailure(GlobalResponseCodeEnum.FAIL);
    }

    public SingleResponse<Boolean> addBusinessDomain(TagBusinessDomainParam tagBusinessDomainParam) {
        if (tagBusinessDomainParam == null || StrUtil.isBlank(tagBusinessDomainParam.getName())
                || tagBusinessDomainParam.getCode() == null) {
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }

        TagLeafBusinessDomain tagLeafBusinessDomain = iTagLeafBusinessDomainRepository.getOne(
                new LambdaQueryWrapper<TagLeafBusinessDomain>()
                        .eq(TagLeafBusinessDomain::getCode, tagBusinessDomainParam.getCode())
                        .eq(TagLeafBusinessDomain::getIsDeleted, 0)
        );

        if (tagLeafBusinessDomain != null) {
            return SingleResponse.buildFailure(TagCenterResultTypeEnum.TAG_BUSSINESS_DOMAIN_ALREADY_EXISTS);
        }

        tagLeafBusinessDomain = new TagLeafBusinessDomain();
        tagLeafBusinessDomain.setCode(tagBusinessDomainParam.getCode());
        tagLeafBusinessDomain.setName(tagBusinessDomainParam.getName());
        tagLeafBusinessDomain.setIsDeleted(0);
        tagLeafBusinessDomain.setCreated(DateUtil.currentSeconds());
        tagLeafBusinessDomain.setUpdated(DateUtil.currentSeconds());

        boolean res = iTagLeafBusinessDomainRepository.save(tagLeafBusinessDomain);
        return SingleResponse.buildSuccess(res);
    }
}
