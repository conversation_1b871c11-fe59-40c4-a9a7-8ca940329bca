package com.neo.tagcenter.app.constants;

public enum TagDomainEnums {

    SALES_REGION("sales_region", "销售区域"),

    ADMINISTRATIVE_REGION("administrative_region", "行政区域"),

    CUSTOMER_TYPE("customer_type", "客户类型"),

    PRICE_PLAN("price_plan", "价格计划"),

    GOODS_CATEGORY("goods_category", "货品分类"),

    // 送货路线
    DELIVERY_ROUTE("delivery_route", "送货路线"),

    ;

    private final String code;

    private final String description;

    TagDomainEnums(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }


}
