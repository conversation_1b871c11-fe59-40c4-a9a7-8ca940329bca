package com.neo.tagcenter.app.log;

public class TagCenterTraceConstant {
    public static final String TAG_CENTER_APP_NAME = "标签中心";

    public enum Identifier {
        ADD_TAG("add_tag", "用户[%d]在标签域：[%s]，新增标签：[%s]"),
        UPDATE_TAG("update_tag", "用户[%d]在标签域：[%s]，编辑标签ID：[%d]"),
        DEL_TAG("del_tag", "用户[%d]删除标签ID：[%d]"),
        SAVE_RANK_TAG("save_rank_tag", "用户[%d]保持标签排序，涉及标签ids：[%s]"),
        UPDATE_TAG_STATUS("update_tag_status", "用户[%d]更新标签状态，标签ID：[%d]，状态：[%d]"),

        ADD_BUSINESS_DOMAIN("add_business_domain", "用户[%d]创建业务域，[%s]"),

        ;

        private final String locationId;

        private final String desc;

        Identifier(String locationId, String desc) {
            this.locationId = locationId;
            this.desc = desc;
        }

        public String getLocationId() {
            return locationId;
        }

        public String getDesc() {
            return desc;
        }
    }

}
