package com.neo.tagcenter.app.biz.option;

import com.neo.tagcenter.client.param.BaseTreeTagQueryOption;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class TreeTagQuery extends BaseTreeTagQueryOption implements Serializable {
    @Serial
    private static final long serialVersionUID = -5816886535889795676L;

    /**
     * 查询某个业务领域下面的树形标
     */
    private Integer businessDomain;

    /**
     * 查询某个标签领域下面的树形标
     */
    private String tagDomain;

    /**
     * 查询某个标签id下面的树形标
     */
    private Long tagLeafId;

    /**
     * 批量查询某个标签id下面的树形标
     */
    private List<Long> tagLeafIds;

    /**
     * 查询编号
     */
    private String tagCode;

    /**
     * 查询名称
     */
    private String tagName;
}
