package com.neo.tagcenter.app.log;

import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class TagCenterBizTracer {

    private static final Logger logger = LoggerFactory.getLogger(TagCenterBizTracer.class);

    private boolean traceLogOpen() {
        return true;
    }

    public void trace(TagCenterTraceConstant.Identifier identifier, String appName, Object params, Object result, Object... args) {
        if (!traceLogOpen()) {
            return;
        }
        try {
            TagCenterBizLogParam param = TagCenterBizLogParam.builder()
                    .app(appName)
                    .time(String.valueOf(System.currentTimeMillis()))
                    .identifier(identifier.getLocationId())//埋点位置编号 string
                    .params(params == null ? null : JSON.toJSONString(params.toString()))
                    .result(result == null ? null : JSON.toJSONString(result.toString()))
                    .bizLog(String.format(identifier.getDesc(), args)) //描述，这块也通过全息系统模板的方式自动生成，详情咨询@weiqing，描述尽量站在运营或者不熟悉业务的同学角度表述，不然看了日志不明白啥意思，还得打扰你  string
                    .build();
            bizLog(param);
        } catch (Throwable t) {
            logger.error("something wrong when logging biz.", t);
        }
    }

    private void bizLog(TagCenterBizLogParam param) {
        logger.info(">>>>>>>>>>>操作记录>>>>>>>>>>>" + param.bizLog());
    }

}
