package com.neo.tagcenter.app.constants;

/*
 * 标签树等级
 * */
public enum LevelEnum {
    Level1(1, "rootId"),
    Level2(2, "level2Id"),
    Level3(3, "level3Id"),
    Level4(4, "level4Id"),
    Level5(5, "level5Id"),
    Level6(6, "level6Id"),
    Level7(7, "level7Id"),
    Level8(8, "level8Id"),
    Level9(9, "level9Id"),
    Level10(10, "level10Id"),

    ;
    private final Integer level;

    private final String dbField;

    LevelEnum(Integer level, String dbField) {
        this.level = level;
        this.dbField = dbField;
    }

    public static LevelEnum getLevelEnum(Integer level) {
        if (Integer.valueOf(1).equals(level)) {
            return Level1;
        } else if (Integer.valueOf(2).equals(level)) {
            return Level2;
        } else if (Integer.valueOf(3).equals(level)) {
            return Level3;
        } else if (Integer.valueOf(4).equals(level)) {
            return Level4;
        } else if (Integer.valueOf(5).equals(level)) {
            return Level5;
        } else {
            return null;
        }
    }

    public Integer getLevel() {
        return level;
    }

    public String getDbField() {
        return dbField;
    }

}
