package com.neo.tagcenter.app.biz.option;

import cn.hutool.db.Page;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class TagLeafInfoQueryOption extends Page implements Serializable {
    @Serial
    private static final long serialVersionUID = -7502882147090324967L;

    private List<Long> ids;

    /**
     * 批量标签名称
     */
    private List<String> names;

    /**
     * 批量父叶子id
     */
    private List<Long> parentIds;

    /**
     * 批量根节点id
     */
    private List<Long> rootIds;

    /**
     * 该叶子节点在标签等级依次是1，2，3，4，5
     */
    private List<Integer> levels;

    /**
     * 业务领域
     */
    private List<Integer> businessDomainEnumList;

    /**
     * 标签领域
     */
    private List<String> tagDomainEnumList;


    /**
     * 查询标签code
     */
    private List<String> tagCodes;

    /**
     * 查询标签名称
     */
    private String tagName;




    /**
     * 是否包含删除
     */
    private Boolean isIncludeDeleted = Boolean.FALSE;

    /**
     * 包含不可用
     */
    private Boolean includeDisable = Boolean.FALSE;


    /**
     * pos排序
     */
    private Boolean isPosSortAsc = Boolean.TRUE;


}
