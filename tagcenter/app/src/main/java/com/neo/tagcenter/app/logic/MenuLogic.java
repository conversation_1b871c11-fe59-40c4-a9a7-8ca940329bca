package com.neo.tagcenter.app.logic;

import com.neo.api.MultiResponse;
import com.neo.auth.client.constants.AuthConfigProperties;
import com.neo.auth.client.dto.AuthPlatformMenuDto;
import com.neo.tagcenter.domain.gateway.IMenuRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

//@Service
public class MenuLogic {

//    @Autowired
    private IMenuRepository iMenuRepository;

//    @Autowired
    private AuthConfigProperties authConfigProperties;

    public MultiResponse<AuthPlatformMenuDto> querySideMenuBar(long tenantId, long userId) {
        List<AuthPlatformMenuDto> menuList = iMenuRepository.getMenuByUserId(tenantId, userId, authConfigProperties.getAppKey(), authConfigProperties.getAppSecret());
        return MultiResponse.of(menuList);
    }
}
