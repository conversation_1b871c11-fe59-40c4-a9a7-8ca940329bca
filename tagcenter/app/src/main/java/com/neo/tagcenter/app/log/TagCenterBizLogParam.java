package com.neo.tagcenter.app.log;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;

@Data
@Builder
@Slf4j
public class TagCenterBizLogParam {

    private String time;

    private String app;

    private String params;

    private String result;

    private String bizLog;

    private String identifier;

    public String bizLog() {
        StringBuilder sb = new StringBuilder();
        Class<?> clazz = this.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            try {
                field.setAccessible(true); // 设置可访问私有字段
                String fieldName = field.getName();
                Object value = field.get(this);
                sb.append(fieldName).append(":").append(value).append(", ");
            } catch (IllegalAccessException e) {
                log.error("TagCenterBizLogParam bizLog error", e);
            }
        }

        return sb.toString();
    }
}
