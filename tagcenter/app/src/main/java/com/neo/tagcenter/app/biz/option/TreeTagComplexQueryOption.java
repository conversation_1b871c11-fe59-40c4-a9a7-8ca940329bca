package com.neo.tagcenter.app.biz.option;

import com.neo.tagcenter.client.param.BaseTagQueryOption;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class TreeTagComplexQueryOption extends BaseTagQueryOption {

    @Serial
    private static final long serialVersionUID = 2728804518571663575L;

    /**
     * 业务领域
     */
    private List<Integer> businessDomainEnumList;

    /**
     * 标签领域
     */
    private List<String> tagDomainEnumList;



    /**
     * 批量查询
     */
    private List<Long> tagLeafIds;

    /**
     * 查询父标签parentTagLeafIds
     */
    private List<Long> parentTagLeafIds;

    /**
     * 查询根标签
     */
    private List<Long> rootTagLeafIds;

    /**
     * 查询标签code
     */
    private List<String> tagCodes;

    /**
     * 查询标签名称
     */
    private String tagName;

}
