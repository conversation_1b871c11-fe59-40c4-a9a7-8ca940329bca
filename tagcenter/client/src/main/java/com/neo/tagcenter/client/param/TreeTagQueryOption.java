package com.neo.tagcenter.client.param;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class TreeTagQueryOption extends BaseTagQueryOption {

    @Serial
    private static final long serialVersionUID = 942506008163136841L;

    /**
     * 批量查询
     */
    private List<Long> tagLeafIds;

    /**
     * 查询父标签parentTagLeafIds
     */
    private List<Long> parentTagLeafIds;

    /**
     * 查询根标签
     */
    private List<Long> rootTagLeafIds;


    private List<Integer> businessDomains;


    private List<String> tagDomains;


    /**
     * 校验参数
     */
    public String validate() {

        return null;
    }
}
