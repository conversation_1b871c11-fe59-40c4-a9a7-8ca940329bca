package com.neo.tagcenter.client.param;

import com.neo.tagcenter.client.param.BaseTagQueryOption;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class BaseTreeTagQueryOption extends BaseTagQueryOption implements Serializable {

    @Serial
    private static final long serialVersionUID = -3855252417889523684L;


    /**
     * 是否需要查询子树，如果为true，查询的是树形标签，如果为false，查询的是叶子标签
     * 需要配合 tagLeafId 使用
     */
    private Boolean queryChild = false;
}
