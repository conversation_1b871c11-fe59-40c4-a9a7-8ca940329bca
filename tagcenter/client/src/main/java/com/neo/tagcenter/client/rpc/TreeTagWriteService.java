package com.neo.tagcenter.client.rpc;

import com.neo.api.SingleResponse;
import com.neo.tagcenter.client.param.EnableDisableFeatureParam;
import com.neo.tagcenter.client.param.TagBusinessDomainParam;
import com.neo.tagcenter.client.param.TreeTagSaveParam;
import com.neo.tagcenter.client.param.TreeTagUpdateParam;

public interface TreeTagWriteService {

    /**
     * 新增标签节点
     * TreeTagSaveParam 除了extra外的字段必填
     * 返回 标签id
     */
    SingleResponse<Long> addTagLeafInfo(TreeTagSaveParam treeTagSaveParam);

    /**
     * 根据id更新标签 编号、名称、短名称、描述、父节点、扩展信息
     */
    SingleResponse<Boolean> updateTagLeaf(TreeTagUpdateParam treeTagUpdateParam);

    /**
     * 启用禁用标签节点
     */
    SingleResponse<Boolean> enableDisableTagLeaf(EnableDisableFeatureParam enableDisableFeatureParam);

    SingleResponse<Boolean> delTagLeaf(Long tagLeafId);


    SingleResponse<Boolean> addBusinessDomain(TagBusinessDomainParam tagBusinessDomainParam);
}
