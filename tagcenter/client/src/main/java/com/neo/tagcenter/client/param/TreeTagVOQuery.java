package com.neo.tagcenter.client.param;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class TreeTagVOQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = -6705156935959317030L;

    /**
     * 查询某个业务领域下面的树形标
     */
    private Integer businessDomain;

    /**
     * 树形标签的标签域
     */
    private String tagDomain;

    /**
     * 查询某个标签id下面的树形标
     */
    private Long tagLeafId;

    /**
     * 查询编号
     */
    private String tagCode;

    /**
     * 查询名称
     */
    private String tagName;

    /**
     * 包含不可用
     */
    private Boolean includeDisable = false;

}
