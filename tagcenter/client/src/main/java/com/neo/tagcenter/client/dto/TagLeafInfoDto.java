package com.neo.tagcenter.client.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class TagLeafInfoDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 5229140712156515950L;

    /**
     * 标签叶子id
     */
    private Long id;

    /**
     * 业务域，
     */
    private Integer businessDomain;

    /**
     * 标签域，
     */
    private String tagDomain;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 编号
     */
    private String code;
    /**
     * 名称简写
     */
    private String shortName;
    /**
     * 描述
     */
    private String description;

    /**
     * 该叶子节点父叶子节点
     */
    private Long parentId;

    /**
     * 该叶子节点在标签等级依次是1，2，3，4，5
     */
    private Integer level;

    /**
     * 该叶子结点的根结点tagId
     */
    private Long rootId;

    /**
     * 该叶子结点在该层级的位置
     */
    private Integer pos;

    /**
     * 该叶子结点的扩展信息
     */
    private String extra;

    /**
     * 三方创建时间（时间戳）
     */
    private Long thirdPartyCreateTime;

    /**
     * 三方创建人
     */
    private String thirdPartyCreator;

    /**
     * 创建时间（时间戳）
     */
    private Long createTime;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 修改时间（时间戳）
     */
    private Long updateTime;

    /**
     * 修改人
     */
    private Long updater;

    /**
     * 该结点是否可用，0可用、1不可用
     */
    private Integer isEnabled;

    /**
     * 0：未删除
     * 1：已删除
     */
    private Integer isDeleted;

    private Long created;

    private Long updated;

    /**
     * outId
     */
    private String outId1;

    /**
     * outId
     */
    private String outId2;

    /**
     * outId
     */
    private String outId3;

    /**
     * 外部父节点
     */
    private String outParentId1;

    /**
     * 外部父节点
     */
    private String outParentId2;

    /**
     * 外部父节点
     */
    private String outParentId3;
}
