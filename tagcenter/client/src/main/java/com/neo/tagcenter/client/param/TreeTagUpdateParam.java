package com.neo.tagcenter.client.param;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class TreeTagUpdateParam implements Serializable {
    @Serial
    private static final long serialVersionUID = 6564872713953610045L;

    /**
     * 标签节点id
     */
    private Long tagLeafId;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签编码
     */
    private String code;

    /**
     * 短名称
     */
    private String shortName;

    /**
     * 描述
     */
    private String description;

    /**
     * 父节点id
     */
    private Long parentId;

    /**
     * extra
     */
    private String extra;

}
