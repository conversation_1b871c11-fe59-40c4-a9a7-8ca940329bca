package com.neo.tagcenter.client.rpc;

import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;
import com.neo.tagcenter.client.param.BaseTagQueryOption;
import com.neo.tagcenter.client.param.BusinessTreeTagQueryParam;

import java.util.List;
import java.util.Map;

/**
 * 业务标签读服务，提供具体接口
 */
public interface BusinessTreeTagReadReadService {

    /**
     * 销售区域设置业务 标签查询
     * <p>
     * businessDomainEnum 即租户id（下同）
     */
    MultiResponse<TagLeafInfoDto> querySalesRegionSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 行政区域设置
     */
    MultiResponse<TagLeafInfoDto> queryAdministrativeRegionSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 客户类型设置
     */
    MultiResponse<TagLeafInfoDto> queryCustomerTypeSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 价格方案设置
     */
    MultiResponse<TagLeafInfoDto> queryPricePlanSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 送货线路
     */
    MultiResponse<TagLeafInfoDto> queryDeliveryRouteSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option);


    /**
     * 货品分类设置
     */
    MultiResponse<TagLeafInfoDto> queryGoodsCategorySetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 货品分类设置-成品
     */
    MultiResponse<TagLeafInfoDto> queryGoodsCategorySettingFinished(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 货品分类设置-半成品
     */
    MultiResponse<TagLeafInfoDto> queryGoodsCategorySettingSemiFinished(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 货品分类设置-原材料
     */
    MultiResponse<TagLeafInfoDto> queryGoodsCategorySettingRawMaterial(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    // ==================== 批量查询方法 ====================

    /**
     * 销售区域设置业务 标签批量查询
     *
     * @param param 查询参数，支持outId1List、outId2List、outId3List批量查询
     * @param option 查询选项
     * @return Map<OutId, TagLeafInfoDto> 按outId分组的标签信息
     */
    SingleResponse<Map<String, TagLeafInfoDto>> batchQuerySalesRegionSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 行政区域设置批量查询
     */
    SingleResponse<Map<String, TagLeafInfoDto>> batchQueryAdministrativeRegionSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 客户类型设置批量查询
     */
    SingleResponse<Map<String, TagLeafInfoDto>> batchQueryCustomerTypeSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 价格方案设置批量查询
     */
    SingleResponse<Map<String, TagLeafInfoDto>> batchQueryPricePlanSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 送货线路批量查询
     */
    SingleResponse<Map<String, TagLeafInfoDto>> batchQueryDeliveryRouteSetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 货品分类设置批量查询
     */
    SingleResponse<Map<String, TagLeafInfoDto>> batchQueryGoodsCategorySetting(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 货品分类设置-成品批量查询
     */
    SingleResponse<Map<String, TagLeafInfoDto>> batchQueryGoodsCategorySettingFinished(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 货品分类设置-半成品批量查询
     */
    SingleResponse<Map<String, TagLeafInfoDto>> batchQueryGoodsCategorySettingSemiFinished(BusinessTreeTagQueryParam param, BaseTagQueryOption option);

    /**
     * 货品分类设置-原材料批量查询
     */
    SingleResponse<Map<String, TagLeafInfoDto>> batchQueryGoodsCategorySettingRawMaterial(BusinessTreeTagQueryParam param, BaseTagQueryOption option);


}
