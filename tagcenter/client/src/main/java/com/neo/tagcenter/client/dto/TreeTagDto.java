package com.neo.tagcenter.client.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class TreeTagDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 394893021424292473L;

    private TagLeafInfoDto tagLeafInfoDto;

    private List<TreeTagDto> children = new ArrayList<>();
}
