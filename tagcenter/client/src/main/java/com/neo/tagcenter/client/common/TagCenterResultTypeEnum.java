package com.neo.tagcenter.client.common;

import com.neo.api.code.IResponseCode;

/**
 * Created by bailun on 2017/10/25.
 */
public enum TagCenterResultTypeEnum implements IResponseCode {

    TAG_LEAF_ALREADY_EXISTS("-21", "该叶子已存在，请勿重复添加"),

    TAG_LEAF_PARAM_ERROR_PARENT_ID("-26", "非根节点需要传非空非0的parentId"),

    TAG_LEAF_NOT_EXISTS("-27", "该叶子不存在"),

    TAG_LEAF_STATUS_NOT_CHANGE("-28", "该叶子状态未变更"),

    TAG_BUSSINESS_DOMAIN_NOT_EXISTS("-29", "该业务域不存在"),

    TAG_BUSSINESS_DOMAIN_ALREADY_EXISTS("-30", "该业务域已存在"),

    TAG_NAME_REPEAT("-31", "该叶子节点名称重复"),
    ;


    private final String code;
    private final String message;

    TagCenterResultTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return message;
    }
}
