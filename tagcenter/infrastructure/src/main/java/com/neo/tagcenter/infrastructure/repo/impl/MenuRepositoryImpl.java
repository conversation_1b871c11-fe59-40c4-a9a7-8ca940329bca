package com.neo.tagcenter.infrastructure.repo.impl;

import com.neo.api.MultiResponse;
import com.neo.auth.client.dto.AuthPlatformMenuDto;
import com.neo.auth.client.rpc.MenuService;
import com.neo.tagcenter.domain.gateway.IMenuRepository;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MenuRepositoryImpl implements IMenuRepository {

    private static final Logger logger = LoggerFactory.getLogger(MenuRepositoryImpl.class);

    @Resource
    private MenuService menuService;

    @Override
    public List<AuthPlatformMenuDto> getMenuByUserId(Long tenantId, Long userId, String app, String secret) {
        MultiResponse<AuthPlatformMenuDto> response = menuService.getMenuByUserIdV3(tenantId, userId, app, secret);
        if (!response.isSuccess()) {
            logger.error("getMenuByUserId error: {}", response.getErrMessage());
            return null;
        }
        return response.getData();
    }

}
