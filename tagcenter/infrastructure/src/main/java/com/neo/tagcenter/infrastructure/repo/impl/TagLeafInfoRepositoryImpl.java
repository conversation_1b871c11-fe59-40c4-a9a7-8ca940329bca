package com.neo.tagcenter.infrastructure.repo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.tagcenter.domain.entity.TagLeafInfo;
import com.neo.tagcenter.domain.gateway.ITagLeafInfoRepository;
import com.neo.tagcenter.infrastructure.mapper.TagLeafInfoMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @apiNote 标签树叶子信息表
 * @since 2024-02-21 16:40:07
 */
@Service
public class TagLeafInfoRepositoryImpl extends ServiceImpl<TagLeafInfoMapper, TagLeafInfo>
        implements ITagLeafInfoRepository {

}