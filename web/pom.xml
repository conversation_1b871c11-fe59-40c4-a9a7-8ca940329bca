<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.neo</groupId>
        <artifactId>neo-nova</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>neo-nova-web</artifactId>
    <name>neo-nova-web</name>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-session-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-cache</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>upload-adapter</artifactId>

        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>upload-client</artifactId>

        </dependency>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>upload-domain</artifactId>

        </dependency>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>upload-infrastructure</artifactId>

        </dependency>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>nova-adapter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>nova-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>timeout-adapter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>timeout-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>xxljob-adapter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>xxljob-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>xxljob-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>tagcenter-adapter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>tagcenter-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>user-adapter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>user-infrastructure</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>auth-adapter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>auth-infrastructure</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-mcp-server-webmvc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test-autoconfigure</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>


    <build>
        <finalName>neo_nova</finalName>
        <directory>../target/</directory>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <filtering>true</filtering>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/application.properties</include>
                    <include>**/application-${spring.profiles.active}.properties</include>
                    <include>*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>

    <profiles>
        <!--本地开发使用-->
        <profile>
            <id>local</id>
            <properties>
                <active.profile>local</active.profile>
                <spring.profiles.active>local</spring.profiles.active>
            </properties>
        </profile>
        <!--线下使用-->
        <profile>
            <id>dev</id>
            <properties>
                <active.profile>dev</active.profile>
                <spring.profiles.active>dev</spring.profiles.active>
            </properties>
        </profile>
        <!--预发使用-->
        <profile>
            <id>pre</id>
            <properties>
                <active.profile>pre</active.profile>
                <spring.profiles.active>pre</spring.profiles.active>
            </properties>
        </profile>
        <!--生产使用-->
        <profile>
            <id>online</id>
            <properties>
                <active.profile>online</active.profile>
                <spring.profiles.active>online</spring.profiles.active>
            </properties>
        </profile>
    </profiles>
</project>
