package com.neo.web;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/******************************
 * 用途说明:
 * 作者姓名: 竹笋
 * 创建时间: 2024/2/19 17:40
 ******************************/
@RestController
public class Status {
    @Value("${neo.application.env}")
    private String env;

    @RequestMapping("/status")
    public String status() {
        return "SUCCESS";
    }

    @RequestMapping("/env")
    public String env() {
        return env;
    }
}
