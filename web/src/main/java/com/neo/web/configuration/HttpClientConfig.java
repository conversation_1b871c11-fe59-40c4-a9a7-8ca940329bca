package com.neo.web.configuration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.Executor;

/**
 * HTTP客户端配置
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
@Configuration
@EnableAsync
@Slf4j
public class HttpClientConfig {

    /**
     * 配置RestTemplate
     */
    @Bean
    public RestTemplate pythonServiceRestTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(30000); // 连接超时30秒
        factory.setReadTimeout(600000);   // 读取超时10分钟
        return new RestTemplate(factory);
    }

    /**
     * 配置Dify服务专用的RestTemplate
     */
    @Bean
    public RestTemplate difyServiceRestTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(10000); // 连接超时10秒
        factory.setReadTimeout(60000);    // 读取超时60秒
        return new RestTemplate(factory);
    }

    /**
     * 配置异步任务执行器
     */
    @Bean(name = "excelServiceExecutor")
    public Executor excelServiceExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(0);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("excelService-");
        executor.setKeepAliveSeconds(60);
        // 自定义线程工厂，用于线程被kill时打印日志
        executor.setThreadFactory(r -> {
            Thread thread = new Thread(r, "excelService-" + System.currentTimeMillis());
            thread.setUncaughtExceptionHandler((t, e) -> {
                log.error("Excel服务线程被异常终止: {}, 异常: {}", t.getName(), e.getMessage(), e);
            });
            return thread;
        });

        executor.initialize();
        return executor;
    }
}
