package com.neo.web.configuration;
/*
 *             ,%%%%%%%%,
 *           ,%%/\%%%%/\%%
 *          ,%%%\c "" J/%%%
 * %.       %%%%/ o  o \%%%
 * `%%.     %%%%    _  |%%%
 *  `%%     `%%%%(__Y__)%%'
 *  //       ;%%%%`\-/%%%'
 * ((       /  `%%%%%%%'
 *  \\    .'          |
 *   \\  /       \  | |
 *    \\/         ) | |
 *     \         /_ | |__
 *     (___________))))))) 攻城狮
 *
 *
 * 用途说明:
 * 作者姓名: 竹笋
 * 创建时间: 2024/3/7 11:24
 */

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.scan.StandardJarScanner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class CommonConfig {

    @Value("${neo.application.env}")
    private String env;

    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> customizer() {
        log.info("初始化配置加载webapp配置！！！！！");
        log.info("当前环境->>>>>" + env);
        return factory -> factory.addContextCustomizers(context -> ((StandardJarScanner) context.getJarScanner()).setScanManifest(false));
    }

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // MyBatis Plus 3.5.12 默认分页拦截器
        // 自动识别数据库类型，支持MySQL、SQL Server等多种数据库
        // 无需手动配置数据库方言，完全自动化
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor();
        interceptor.addInnerInterceptor(paginationInterceptor);

        log.info("MyBatis Plus 3.5.12 自动分页拦截器配置完成");

        return interceptor;
    }

}
