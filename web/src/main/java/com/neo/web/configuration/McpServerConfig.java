package com.neo.web.configuration;

import com.neo.nova.adapter.mcp.SupermarketSalesCollectionService;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2025/7/10
 **/
@Configuration
public class McpServerConfig {

    @Bean
    public ToolCallbackProvider supermarketSalesCollectionCallbackProvider(SupermarketSalesCollectionService supermarketSalesCollectionService) {
        return MethodToolCallbackProvider.builder()
                .toolObjects(supermarketSalesCollectionService)
                .build();
    }
}
