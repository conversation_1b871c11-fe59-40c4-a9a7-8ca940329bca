package com.neo.web.configuration;

import com.neo.api.Response;
import com.neo.nova.app.exception.BizCustomException;
import com.neo.session.annotation.NeedLoginException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.*;

/**
 * 增强的全局异常处理器
 * 确保所有Controller异常都能被正确记录日志
 *
 * <AUTHOR>
 * @since 2025/7/22
 */
@RestControllerAdvice
@Slf4j
@Order(1) // 设置较高优先级
public class EnhancedGlobalExceptionHandler {

    // 处理自定义异常
    @ExceptionHandler(BizCustomException.class)
    public Response handleCustomException(BizCustomException ex) {
        log.error("BizCustomException occurred - code: {}, message: {}", ex.getCode(), ex.getMessage(), ex);
        return Response.buildFailure(String.valueOf(ex.getCode()), ex.getMessage());
    }

    // 处理空指针异常
    @ExceptionHandler(NullPointerException.class)
    public Response handleNullPointerException(NullPointerException ex) {
        log.error("NullPointerException occurred - message: {}", ex.getMessage(), ex);
        return Response.buildFailure("500", "系统内部错误: " + ex.getMessage());
    }

    // 处理参数异常
    @ExceptionHandler(IllegalArgumentException.class)
    public Response handleIllegalArgumentException(IllegalArgumentException ex) {
        log.error("IllegalArgumentException occurred - message: {}", ex.getMessage(), ex);
        return Response.buildFailure("400", "参数错误: " + ex.getMessage());
    }

    // 处理运行时异常
    @ExceptionHandler(RuntimeException.class)
    public Response handleRuntimeException(RuntimeException ex) {
        // 如果是NeedLoginException，重新抛出让专门的处理器处理
        if (ex instanceof NeedLoginException) {
            log.warn("NeedLoginException occurred, re-throwing for specialized handler");
//            throw (NeedLoginException) ex;
            return Response.buildFailure("1022", ex.getMessage());
        }
        log.error("RuntimeException occurred - message: {}", ex.getMessage(), ex);
        return Response.buildFailure("500", "系统运行时错误: " + ex.getMessage());
    }

    // 处理所有未捕获的异常 - 最后的兜底
    @ExceptionHandler(Exception.class)
    public Response handleGlobalException(Exception ex) {
        // 如果是NeedLoginException，重新抛出让专门的处理器处理
        if (ex instanceof NeedLoginException) {
            log.warn("NeedLoginException occurred in global handler, re-throwing for specialized handler");
//            throw (NeedLoginException) ex;
            return Response.buildFailure("1022", ex.getMessage());
        }

        // 记录详细的异常信息
        log.error("Unhandled exception occurred - type: {}, message: {}",
                ex.getClass().getSimpleName(), ex.getMessage(), ex);

        return Response.buildFailure("500", "系统内部错误: " + ex.getMessage());
    }
}
