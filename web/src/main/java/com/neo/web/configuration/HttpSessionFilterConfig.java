package com.neo.web.configuration;

import com.neo.session.api.SessionStrategy;
import com.neo.session.filter.HttpSessionFilter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class HttpSessionFilterConfig {

    @Value("${session.server.check.url}")
    private String serverCheckUrl;

    @Value("${session.domain}")
    private String domain;

    @Value("${session.urlPatterns}")
    private String urlPatterns;

    @Value("${session.ignore}")
    private String sessionIgnore;

    @Value("${session.enableHttp}")
    private String enableHttp;

    @Value("${user.tenant.enable}")
    private String enableTenant;

    @Bean
    public FilterRegistrationBean<HttpSessionFilter> filterRegistrationBean(SessionStrategy sessionStrategy) {
        FilterRegistrationBean<HttpSessionFilter> registrationBean = new FilterRegistrationBean<>();
        HttpSessionFilter httpSessionFilter = new HttpSessionFilter();
        httpSessionFilter.setSessionStrategy(sessionStrategy);
        registrationBean.setFilter(httpSessionFilter);
        registrationBean.addUrlPatterns(urlPatterns); // 设置过滤范围为以 '/api/' 开头的路径
        registrationBean.setOrder(1); // 设置过滤器执行顺序
        registrationBean.addInitParameter("serverCheckUrl", serverCheckUrl);
        registrationBean.addInitParameter("domain", domain);
        registrationBean.addInitParameter("sessionIgnore", sessionIgnore);
        registrationBean.addInitParameter("enableHttp", enableHttp);
        registrationBean.addInitParameter("enableTenant", enableTenant);
        return registrationBean;
    }

}
