package com.neo.web.configuration;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * 注释掉 NeedLoginExceptionHandler 的配置，因为已经在统一异常处理器中处理
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    // 注释掉异常处理器配置，使用统一的 @RestControllerAdvice 处理
    // @Override
    // public void configureHandlerExceptionResolvers(List<HandlerExceptionResolver> resolvers) {
    //     resolvers.add(new NeedLoginExceptionHandler());
    // }
}
