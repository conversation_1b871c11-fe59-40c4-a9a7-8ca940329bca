package com.neo.web;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(
        scanBasePackages = {
                "com.neo.web",
                "com.neo.nova",
                "com.neo.timeout",
                "com.neo.xxljob",
                "com.neo.tagcenter",
                "com.neo.user",
                "com.neo.auth",
                "com.neo.cache",
                "com.neo.session",
                "com.neo.common",
                //upload
                "com.neo.upload",
        },
        exclude = {
                // org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration.class  // 允许动态数据源自动配置
        }
)
@MapperScan(basePackages = {
        "com.neo.nova.infrastructure.mapper",
        "com.neo.timeout.infrastructure.mapper",
        "com.neo.xxljob.adapter.dao",
        "com.neo.tagcenter.infrastructure.mapper",
        "com.neo.user.infrastructure.mapper",
        "com.neo.auth.infrastructure.mapper"
}
)
@EnableTransactionManagement
@EnableScheduling
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

}
