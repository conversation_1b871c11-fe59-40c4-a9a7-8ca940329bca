server.port=80
neo.application.env=pre

spring.datasource.url=
spring.datasource.username=
spring.datasource.password=
#tenant
user.tenant.default.domain=neo
user.tenant.enable=false
user.tenant.default.sessionDomain=mogu-inc.com
user.login.captcha.enable=false
#auth
auth.config.super.appKey=neo-user
auth.config.super.appSecret=neo-user
auth.config.super.tenantId=10000
auth.appKey=41dcd05a-03b1-4b67-9a5b-3b76460df3da
auth.appSecret=33cb37a0b32d13f628a872d3e1920d2c
#session
session.server.check.url=http://localhost/api/session/checkSign
session.enableHttp=false
session.domain=mogu-inc.com
session.urlPatterns=/*
session.ignore=/api/session/checkSign;/api/user/logout;/api/status;/api/user/login;/api/user/callback;/api/user/tenantAuth
#redis
redis.namespace=test
redis.timeout=300
redis.maxQps=3000
redis.host=***********
redis.port=6374
redis.database=
redis.username=
redis.password=
# qiyeweixin
wx.qy.token=yFbQCkQ4CbgvnZ3z6ZhGhY
wx.qy.encodingAESKey=zwSRxDu1WCSM2iDUzqU2J74T7kB3hWD06XT9jQdLd2p
wx.qy.corpId=ww9c836769f4ec7e7d
wx.qy.web.suiteId=wwa3108d26bb8d22d7
wx.qy.web.suiteSecret=OBaTQCcLuCXxmHq0fv0NmORfeSnWFHmDECGo6EioXrA
wx.qy.nova.suiteId=ww6d7ed94b8d8470a1
wx.qy.nova.suiteSecret=ltYyiqAQWgNuWZT_t1c6F1CLKgxioGhLmwxdwg0DP08

ai.apikey.checkGoodsPhoto=app-X1AakTmfdDL4D1IzAAgi8qx8

wx.qy.novaxcx.suiteId=wwf18d8a6ebceb6301
wx.qy.novaxcx.suiteSecret=rWFwHQtEElfjMOTnLt3MwZqVfr4JDTYq8ZMYsQIDzkw
