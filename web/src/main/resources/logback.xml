<configuration debug="false" scan="true" scanPeriod="10 seconds">
    <conversionRule conversionWord="ip" converterClass="com.neo.common.log.IpConvert"/>

    <property name="FILE_PATTERN" value="%d [%ip] [%t] %5p %c %F:%L - %m%n"/>

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>

    <property name="STDOUT_LOG_FILE_NAME" value="neo_nova_stdout"/>
    <property name="ERROR_LOG_FILE_NAME" value="neo_nova_error"/>
    <property name="LOG_PATH" value="logs"/>

    <appender name="STDOUT_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_PATH}/${STDOUT_LOG_FILE_NAME}.log</File>
        <encoder>
            <pattern>${FILE_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${STDOUT_LOG_FILE_NAME}.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
        <File>${LOG_PATH}/${ERROR_LOG_FILE_NAME}.log</File>

        <encoder>
            <pattern>${FILE_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${ERROR_LOG_FILE_NAME}.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
    </appender>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="STDOUT_FILE"/>
        <appender-ref ref="ERROR_FILE"/>
    </root>
</configuration>
