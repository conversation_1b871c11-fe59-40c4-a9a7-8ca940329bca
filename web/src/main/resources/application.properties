spring.profiles.active=@spring.profiles.active@
neo.application.name=neo-nova
# MyBatis Plus??
mybatis-plus.mapper-locations=classpath*:mapper/*.xml
mybatis-plus.type-aliases-package=com.neo.nova.domain.entity.*,com.neo.xxljob.adapter.core.*.model,com.neo.user.domain.entity.*,com.neo.tagcenter.domain.entity.*,com.neo.auth.domain.entity.*,com.neo.download.domain.domain.*
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.cache-enabled=false
#mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
#
spring.datasource.hikari.maximum-pool-size=50
spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.idle-timeout=30000
# ???????????
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration,org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration
spring.jackson.default-property-inclusion=NON_NULL
# null field not print
# xxl-job-admin
xxl.job.i18n=zh_CN
### server = client \u672C\u673A\u6267\u884C
xxl.job.accessToken=NOTSUPPORT
xxl.job.triggerpool.fast.max=200
xxl.job.triggerpool.slow.max=100
xxl.job.logretentiondays=30
# xxl-job-executor
xxl.job.executor.appname=neo-nova
xxl.job.executor.logretentiondays=30
### resources
spring.mvc.servlet.load-on-startup=0
spring.mvc.static-path-pattern=/static/**
spring.web.resources.static-locations=classpath:/static/
spring.servlet.multipart.max-file-size=256MB
spring.servlet.multipart.max-request-size=256MB
### freemarker
spring.freemarker.templateLoaderPath=classpath:/templates/
spring.freemarker.suffix=.ftl
spring.freemarker.charset=UTF-8
spring.freemarker.request-context-attribute=request
spring.freemarker.settings.number_format=0.##########
# context prefix
server.servlet.context-path=/api
### logback
logging.config=classpath:logback.xml
spring.ai.mcp.server.enabled=true
spring.ai.mcp.server.name=axiaoai-server
spring.ai.mcp.server.version=1.0.0
spring.ai.mcp.server.type=sync
spring.ai.mcp.server.base-url=/api
spring.ai.mcp.server.sse-message-endpoint=/mcp/message
# Dify AI????
dify.api.base-url=http://***********:8200
dify.api.key=app-AcItUN0cj2anN8JKxxcI4rl0
dify.api.chat-endpoint=/v1/chat-messages
dify.api.completion-endpoint=/v1/completion-messages
# upload
spring.datasource.url=***********************************************************************************************************************************************
spring.datasource.username=pay
spring.datasource.password=pay123
#redis
redis.namespace=test
redis.timeout=300
redis.maxQps=3000
redis.host=***********
redis.port=6374
redis.database=
redis.username=
redis.password=
# Dubbo ???
dubbo.application.name=dubbo2tesla
dubbo.application.logger=slf4j
dubbo.protocol.name=dubbo
dubbo.protocol.port=20880
dubbo.registry.address=zookeeper://************:2181
dubbo.scan.base-packages=com.neo.web
# oss
aliyun.oss.file.endpoint=oss-cn-hangzhou.aliyuncs.com
aliyun.oss.file.keyid=LTAI5tHg9FF8tSESbJrXQ7c1
aliyun.oss.file.keysecret=******************************
aliyun.oss.file.rolArn=acs:ram::1328232766592699:role/upload
aliyun.oss.file.bucketname=mogu-inc
aliyun.oss.file.region=oss-cn-hangzhou
aliyun.oss.file.duration=3600
#cos
tencent.cos.file.secretId=AKIDDvhP2jR8tfJDVltBjF4qZLrOtTKMVk3Q
tencent.cos.file.secretKey=OpHkYURJJSkJ8PZ8dCvcMD426tt2FYyg
tencent.cos.file.bucketName=neoimg-1251964405
tencent.cos.file.region=ap-shanghai
tencent.cos.file.url=https://neoimg-1251964405.cos.ap-shanghai.myqcloud.com
