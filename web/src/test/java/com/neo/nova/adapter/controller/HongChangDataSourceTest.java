package com.neo.nova.adapter.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.neo.api.Response;
import com.neo.api.SingleResponse;
import com.neo.nova.app.request.DataSourceUploadRequest;
import com.neo.nova.app.service.DataSourceService;
import com.neo.nova.app.service.SupermarketSalesService;
import com.neo.nova.domain.entity.st_trd_customer_produce_day_info;
import com.neo.nova.domain.gateway.st_trd_customer_produce_day_infoRepository;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 宏昌数据源专用测试类
 * 测试宏昌Excel文件处理功能
 *
 * <AUTHOR>
 * @since 2025/7/17
 */
@WebMvcTest(DataSourceController.class)
class HongChangDataSourceTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DataSourceService dataSourceService;

    @MockBean
    private SupermarketSalesService supermarketSalesService;

    @Autowired
    private ObjectMapper objectMapper;

    private DataSourceUploadRequest hongChangRequest;

    @BeforeEach
    void setUp() {
        // 设置宏昌Excel处理请求
        hongChangRequest = new DataSourceUploadRequest();
        hongChangRequest.setUserId(1L);
        hongChangRequest.setTenantId(10000L);
        hongChangRequest.setFileUrl("https://neoimg-1251964405.cos.ap-shanghai.myqcloud.com/neo_export/test2/a3d09272-ccd6-4758-9e4f-86fdf52e3134xls");
        hongChangRequest.setFileName("hongchang_test.xls");
        hongChangRequest.setFileType("excel");
        hongChangRequest.setShopName("宏昌测试店铺");
    }

    @Test
    void testProcessCdnFile_HongChangExcel_Success() throws Exception {
        // Given
        SingleResponse successResponse = SingleResponse.buildSuccess();
        when(dataSourceService.processDataImport(any(DataSourceUploadRequest.class)))
                .thenReturn(successResponse);

        // When & Then
        MvcResult result = mockMvc.perform(post("/dataSource/upload")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(hongChangRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andReturn();

        // 验证响应内容
        String responseContent = result.getResponse().getContentAsString();
        Response response = objectMapper.readValue(responseContent, Response.class);
        assertTrue(response.isSuccess());

        // 打印请求和响应内容用于调试
        System.out.println("宏昌测试请求: " + objectMapper.writeValueAsString(hongChangRequest));
        System.out.println("宏昌测试响应: " + responseContent);
    }

    @Test
    void testProcessCdnFile_HongChangExcel_WithShopName() throws Exception {
        // Given - 测试包含店铺名称的宏昌Excel处理
        hongChangRequest.setShopName("宏昌旗舰店");

        SingleResponse successResponse = SingleResponse.buildSuccess();
        when(dataSourceService.processDataImport(any(DataSourceUploadRequest.class)))
                .thenReturn(successResponse);

        // When & Then
        mockMvc.perform(post("/dataSource/upload")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(hongChangRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 验证请求包含店铺名称
        String requestJson = objectMapper.writeValueAsString(hongChangRequest);
        assertTrue(requestJson.contains("宏昌旗舰店"));
        System.out.println("包含店铺名称的宏昌测试请求: " + requestJson);
    }

    @Test
    void testProcessCdnFile_HongChangExcel_WithRealTestFile() throws Exception {
        // Given - 使用指定的真实测试文件URL测试宏昌处理
        DataSourceUploadRequest realFileRequest = new DataSourceUploadRequest();
        realFileRequest.setUserId(1L);
        realFileRequest.setTenantId(10000L);
        realFileRequest.setFileUrl("https://neoimg-1251964405.cos.ap-shanghai.myqcloud.com/neo_export/test2/a3d09272-ccd6-4758-9e4f-86fdf52e3134xls");
        realFileRequest.setFileName("a3d09272-ccd6-4758-9e4f-86fdf52e3134.xls");
        realFileRequest.setFileType("excel");
        realFileRequest.setShopName("宏昌实际测试店铺");

        SingleResponse successResponse = SingleResponse.buildSuccess();
        when(dataSourceService.processDataImport(any(DataSourceUploadRequest.class)))
                .thenReturn(successResponse);

        // When & Then
        MvcResult result = mockMvc.perform(post("/dataSource/upload")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(realFileRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andReturn();

        // 验证响应
        String responseContent = result.getResponse().getContentAsString();
        Response response = objectMapper.readValue(responseContent, Response.class);
        assertTrue(response.isSuccess());

        // 打印详细信息
        System.out.println("真实文件宏昌测试请求: " + objectMapper.writeValueAsString(realFileRequest));
        System.out.println("真实文件宏昌测试响应: " + responseContent);
    }

    @Test
    void testProcessCdnFile_HongChangExcel_ServiceFailure() throws Exception {
        // Given - 测试宏昌Excel处理失败场景
        SingleResponse failureResponse = SingleResponse.buildFailure("5000", "宏昌Excel数据处理失败");
        when(dataSourceService.processDataImport(any(DataSourceUploadRequest.class)))
                .thenReturn(failureResponse);

        // When & Then
        mockMvc.perform(post("/dataSource/upload")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(hongChangRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.errCode").value("5000"))
                .andExpect(jsonPath("$.errMessage").value("宏昌Excel数据处理失败"));
    }

    @Test
    void testProcessCdnFile_HongChangExcel_MissingShopName() throws Exception {
        // Given - 测试缺少店铺名称的情况
        hongChangRequest.setShopName(null);

        SingleResponse successResponse = SingleResponse.buildSuccess();
        when(dataSourceService.processDataImport(any(DataSourceUploadRequest.class)))
                .thenReturn(successResponse);

        // When & Then
        mockMvc.perform(post("/dataSource/upload")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(hongChangRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 验证请求不包含店铺名称
        String requestJson = objectMapper.writeValueAsString(hongChangRequest);
        assertFalse(requestJson.contains("shopName\":\""));
        System.out.println("缺少店铺名称的宏昌测试请求: " + requestJson);
    }

    @Test
    void testProcessCdnFile_HongChangExcel_DifferentDateRanges() throws Exception {
        // Given - 测试不同日期范围的宏昌Excel处理
        DataSourceUploadRequest[] requests = {
                createHongChangRequest("2025-01-01", "2025-01-31", "宏昌一月店铺"),
                createHongChangRequest("2025-02-01", "2025-02-28", "宏昌二月店铺"),
                createHongChangRequest("2025-03-01", "2025-03-31", "宏昌三月店铺")
        };

        SingleResponse successResponse = SingleResponse.buildSuccess();
        when(dataSourceService.processDataImport(any(DataSourceUploadRequest.class)))
                .thenReturn(successResponse);

        // When & Then
        for (DataSourceUploadRequest request : requests) {
            mockMvc.perform(post("/dataSource/upload")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true));

            System.out.println("不同日期范围宏昌测试请求: " + objectMapper.writeValueAsString(request));
        }
    }

    private DataSourceUploadRequest createHongChangRequest(String startDate, String endDate, String shopName) {
        DataSourceUploadRequest request = new DataSourceUploadRequest();
        request.setUserId(1L);
        request.setTenantId(10000L);
        request.setFileUrl("https://neoimg-1251964405.cos.ap-shanghai.myqcloud.com/neo_export/test2/a3d09272-ccd6-4758-9e4f-86fdf52e3134xls");
        request.setFileName("hongchang_" + startDate + "_to_" + endDate + ".xls");
        request.setFileType("excel");
        request.setShopName(shopName);
        return request;
    }


}
