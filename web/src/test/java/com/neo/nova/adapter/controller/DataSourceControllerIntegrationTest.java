package com.neo.nova.adapter.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.neo.api.Response;
import com.neo.nova.app.request.DataSourceUploadRequest;
import com.neo.nova.domain.entity.st_trd_customer_produce_day_info;
import com.neo.nova.domain.gateway.st_trd_customer_produce_day_infoRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * DataSourceController 集成测试类
 * 测试完整的请求处理流程
 *
 * <AUTHOR>
 * @since 2025/7/17
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,classes = com.neo.web.Application.class)
@ActiveProfiles("test")
public class DataSourceControllerIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    void testProcessCdnFile_WithRealTestFile_Integration() throws Exception {
        // Given - 使用提供的真实测试文件URL
        DataSourceUploadRequest request = new DataSourceUploadRequest();
        request.setUserId(1L);
        request.setTenantId(10000L);
        request.setFileUrl("https://neoimg-1251964405.cos.ap-shanghai.myqcloud.com/neo_export/test2/a3d09272-ccd6-4758-9e4f-86fdf52e3134xls");
        request.setFileName("a3d09272-ccd6-4758-9e4f-86fdf52e3134.xls");
        request.setFileType("excel");

        // When
        MvcResult result = mockMvc.perform(post("/dataSource/upload")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andReturn();

        // Then
        String responseContent = result.getResponse().getContentAsString();
        assertNotNull(responseContent);

        Response response = objectMapper.readValue(responseContent, Response.class);
        assertNotNull(response);

        // 打印响应内容用于调试
        System.out.println("Response: " + responseContent);

        // 验证响应结构
        assertTrue(responseContent.contains("success"));
    }

    @Test
    void testProcessCdnFile_HongChangExcel_Integration() throws Exception {
        // Given - 测试宏昌Excel处理路径
        DataSourceUploadRequest request = new DataSourceUploadRequest();
        request.setUserId(10086L);
        request.setTenantId(21L);
        request.setFileUrl("https://neoimg-1251964405.cos.ap-shanghai.myqcloud.com/neo_export/test2/72eaa407-7156-4b27-9b05-bea340b69dcc.xls");
        request.setFileName("订货商品明细 (13)");
        request.setFileType("excel");
        request.setDataSourceType("勤润订货商品明细");

        // When
        MvcResult result = mockMvc.perform(post("/dataSource/upload")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andReturn();

        // Then
        String responseContent = result.getResponse().getContentAsString();
        assertNotNull(responseContent);

        Response response = objectMapper.readValue(responseContent, Response.class);
        assertNotNull(response);

        // 打印响应内容用于调试
        System.out.println("HongChang Response: " + responseContent);
    }

    @Test
    void testProcessCdnFile_PythonProcessing_Integration() throws Exception {
        // Given - 测试Python处理路径
        DataSourceUploadRequest request = new DataSourceUploadRequest();
        request.setUserId(1L);
        request.setTenantId(10000L);
        request.setFileUrl("https://neoimg-1251964405.cos.ap-shanghai.myqcloud.com/neo_export/test2/a3d09272-ccd6-4758-9e4f-86fdf52e3134xls");
        request.setFileName("python_test.xls");
        request.setFileType("excel");

        // When
        MvcResult result = mockMvc.perform(post("/dataSource/upload")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andReturn();

        // Then
        String responseContent = result.getResponse().getContentAsString();
        assertNotNull(responseContent);

        Response response = objectMapper.readValue(responseContent, Response.class);
        assertNotNull(response);

        // 打印响应内容用于调试
        System.out.println("Python Processing Response: " + responseContent);
    }

    @Test
    void testProcessCdnFile_InvalidParameters_Integration() throws Exception {
        // Given - 测试参数验证
        DataSourceUploadRequest request = new DataSourceUploadRequest();
        // 故意不设置必要的参数

        // When
        MvcResult result = mockMvc.perform(post("/dataSource/upload")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andReturn();

        // Then
        String responseContent = result.getResponse().getContentAsString();
        assertNotNull(responseContent);

        Response response = objectMapper.readValue(responseContent, Response.class);
        assertNotNull(response);

        // 打印响应内容用于调试
        System.out.println("Invalid Parameters Response: " + responseContent);

        // 应该返回失败响应
        assertFalse(response.isSuccess());
    }

    @Test
    void testProcessCdnFile_ImageFileType_Integration() throws Exception {
        // Given - 测试图片文件类型
        DataSourceUploadRequest request = new DataSourceUploadRequest();
        request.setUserId(1L);
        request.setTenantId(10000L);
        request.setFileUrl("https://example.com/test.jpg");
        request.setFileName("test.jpg");
        request.setFileType("image");

        // When
        MvcResult result = mockMvc.perform(post("/dataSource/upload")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andReturn();

        // Then
        String responseContent = result.getResponse().getContentAsString();
        assertNotNull(responseContent);

        Response response = objectMapper.readValue(responseContent, Response.class);
        assertNotNull(response);

        // 打印响应内容用于调试
        System.out.println("Image File Response: " + responseContent);
    }

    @Test
    void testProcessCdnFile_RequestStructure() throws Exception {
        // Given - 验证请求结构
        DataSourceUploadRequest request = new DataSourceUploadRequest();
        request.setUserId(1L);
        request.setTenantId(10000L);
        request.setFileUrl("https://neoimg-1251964405.cos.ap-shanghai.myqcloud.com/neo_export/test2/a3d09272-ccd6-4758-9e4f-86fdf52e3134xls");
        request.setFileName("structure_test.xls");
        request.setFileType("excel");

        // 验证请求对象的序列化
        String requestJson = objectMapper.writeValueAsString(request);
        System.out.println("Request JSON: " + requestJson);

        // 验证包含所有必要字段
        assertTrue(requestJson.contains("userId"));
        assertTrue(requestJson.contains("tenantId"));
        assertTrue(requestJson.contains("fileUrl"));
        assertTrue(requestJson.contains("fileName"));
        assertTrue(requestJson.contains("fileType"));
        assertTrue(requestJson.contains("dataSourceName"));
        assertTrue(requestJson.contains("startDate"));
        assertTrue(requestJson.contains("endDate"));
        assertTrue(requestJson.contains("a3d09272-ccd6-4758-9e4f-86fdf52e3134"));

        // When & Then
        mockMvc.perform(post("/dataSource/upload")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }

    @Autowired
    private st_trd_customer_produce_day_infoRepository dayInfoRepository;

    @Test
    void testvist_date() throws Exception {
        // 简化查询，只查询几个字段
        LambdaQueryWrapper<st_trd_customer_produce_day_info> simpleQuery = new LambdaQueryWrapper<>();
        simpleQuery.eq(st_trd_customer_produce_day_info::getTenantId, 21L)
                .eq(st_trd_customer_produce_day_info::getIsDeleted, 0)
                .last("LIMIT 10");

        List<st_trd_customer_produce_day_info> testRecords = dayInfoRepository.list(simpleQuery);
// 打印结果检查 visit_date 字段
        testRecords.forEach(record -> {
            try {
                // 使用反射获取visitDate字段值
                java.lang.reflect.Field field = record.getClass().getDeclaredField("visitDate");
                field.setAccessible(true);
                Object visitDate = field.get(record);
                System.out.println("ID: " + record.getId() + ", visit_date: " + visitDate);
            } catch (Exception e) {
                System.out.println("ID: " + record.getId() + ", 无法获取visit_date: " + e.getMessage());
            }
        });
    }
}
