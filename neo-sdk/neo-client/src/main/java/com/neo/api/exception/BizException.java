package com.neo.api.exception;


import com.neo.api.code.IResponseCode;

/**
 * 业务异常，需要记录 error 日志、不报警，目前主要是：入参校验，准入校验，业务校验。
 */
public class BizException extends BaseException {
    public BizException() {
    }

    public BizException(String code, String msg) {
        super(code, msg);
    }

    public BizException(String code, String originalErrorCode, String msg) {
        super(code, originalErrorCode, msg);
    }

    public BizException(IResponseCode errors) {
        super(errors.getCode(), errors.getDescription());
    }
}
