package com.neo.api.exception;

import com.neo.api.code.IResponseCode;

/**
 * 依赖方调用出错。弱依赖打印日志级别：warn，强依赖打印日志级别：error。
 */
public class DependencyException extends BaseException {

    public DependencyException() {
    }

    public DependencyException(String code, String msg) {
        super(code, msg);
    }

    public DependencyException(String code, String originalErrorCode, String msg) {
        super(code, originalErrorCode, msg);
    }

    public DependencyException(String code, String msg, Exception e) {
        super(code, msg, e);
    }

    public DependencyException(IResponseCode defaultDependencyError, String msg) {
        super(defaultDependencyError.getCode(), msg);
    }

    public DependencyException(String message) {
        super(message);
    }
}
