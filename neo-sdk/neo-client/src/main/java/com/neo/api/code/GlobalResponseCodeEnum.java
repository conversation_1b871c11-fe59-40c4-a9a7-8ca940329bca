package com.neo.api.code;

/**
 * 全局响应码，不需要加系统码
 *
 * <AUTHOR>
 * @date 2022/2/8 6:49 下午
 */
public enum GlobalResponseCodeEnum implements IResponseCode {

    SUCCESS("1001", "系统调用成功"), //app层不使用
    PARAM_ERROR("1002", "参数异常"),
    USERINFO_ERROR("1003", "用户信息异常"), //仅app层内使用，adapter层不对外透出。

    INVALID_SESSION("1022", "用户未登录"),
    PERMISSION_FORBIDDEN("4003", "很抱歉，您暂无权执行此操作"),
    FAIL("4004", "系统调用失败"),
    ;

    private String code;
    private String description;

    GlobalResponseCodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}

