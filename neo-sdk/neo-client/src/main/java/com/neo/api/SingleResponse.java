package com.neo.api;

import com.neo.api.code.GlobalResponseCodeEnum;
import com.neo.api.code.IResponseCode;

/**
 * SingleResponse
 */
public class SingleResponse<T> extends Response {

    private static final long serialVersionUID = 1L;

    private T data;

    public static SingleResponse buildSuccess() {
        SingleResponse response = new SingleResponse();
        response.setSuccess(true);
        return response;
    }

    public static <T> SingleResponse<T> buildSuccess(T data) {
        SingleResponse response = new SingleResponse();
        response.setSuccess(true);
        response.setData(data);
        return response;
    }

    public static SingleResponse buildFailure(String errCode, String errMessage) {
        SingleResponse response = new SingleResponse();
        response.setSuccess(false);
        response.setErrCode(errCode);
        response.setErrMessage(errMessage);
        return response;
    }

    public static SingleResponse buildFailure(IResponseCode responseCode) {
        SingleResponse response = new SingleResponse();
        response.setSuccess(false);
        response.setErrCode(responseCode.getCode());
        response.setErrMessage(responseCode.getDescription());
        return response;
    }

    public static <T> SingleResponse<T> buildFailure(IResponseCode responseCode, T data) {
        SingleResponse response = new SingleResponse();
        response.setSuccess(false);
        response.setErrCode(responseCode.getCode());
        response.setErrMessage(responseCode.getDescription());
        return response;
    }

    public static <T> SingleResponse<T> buildFailure(T data) {
        SingleResponse response = new SingleResponse();
        response.setSuccess(false);
        response.setErrCode(GlobalResponseCodeEnum.FAIL.getCode());
        response.setErrMessage(GlobalResponseCodeEnum.FAIL.getDescription());
        return response;
    }

    public static <T> SingleResponse<T> of(T data) {
        SingleResponse<T> response = new SingleResponse<>();
        response.setSuccess(true);
        response.setData(data);
        return response;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "SingleResponse [data=" + data + ", success=" + isSuccess() + ", errCode=" + getErrCode() + ", errMessage="
                + getErrMessage() + "]";
    }
}
