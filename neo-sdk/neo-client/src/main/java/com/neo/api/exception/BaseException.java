/**
 * www.mogu.com Inc.
 * Copyright (c) 2010-2021 All Rights Reserved.
 */
package com.neo.api.exception;

import com.neo.api.code.IResponseCode;

/**
 * 业务异常状态码
 */
public class BaseException extends RuntimeException {
    /**
     * 错误码
     */
    private String code;
    /**
     * 异常消息
     */
    private String msg;

    /**
     * 原始错误码(一般记录调用方的错误码)
     */
    private String originalErrorCode;

    public BaseException() {
    }

    /**
     * 基本方式
     */
    public BaseException(String code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    /**
     * 基本方式
     */
    public BaseException(String code, String originalErrorCode, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
        this.originalErrorCode = originalErrorCode;
    }

    public BaseException(String code, String msg, Throwable e) {
        super(msg, e);
        this.code = code;
        this.msg = msg;
    }

    public BaseException(String message) {
        super(message);
    }

    public BaseException(IResponseCode responseCode) {
        this(responseCode.getCode(), responseCode.getDescription());
    }

    public BaseException(IResponseCode responseCode, Exception e) {
        this(responseCode.getCode(), responseCode.getDescription(), e);
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public String getOriginalErrorCode() {
        return originalErrorCode;
    }

}
