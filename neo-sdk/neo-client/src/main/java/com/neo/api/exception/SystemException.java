package com.neo.api.exception;


import com.neo.api.code.IResponseCode;

/**
 * 业务系统异常，需要记录 error 日志、报警。
 */
public class SystemException extends BaseException {

    public SystemException() {
    }

    public SystemException(String code, String msg) {
        super(code, msg);
    }

    public SystemException(String code, String originalErrorCode, String msg) {
        super(code, originalErrorCode, msg);
    }

    public SystemException(IResponseCode errors) {
        super(errors.getCode(), errors.getDescription());
    }
}
