package com.neo.api;

import com.neo.api.code.GlobalResponseCodeEnum;
import com.neo.api.code.IResponseCode;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * MultiResponse
 */
public class MultiResponse<T> extends Response {

    private static final long serialVersionUID = 1L;

    private Collection<T> data;

    public static MultiResponse buildSuccess() {
        MultiResponse response = new MultiResponse();
        response.setSuccess(true);
        return response;
    }

    public static MultiResponse buildFailure(String errCode, String errMessage) {
        MultiResponse response = new MultiResponse();
        response.setSuccess(false);
        response.setErrCode(errCode);
        response.setErrMessage(errMessage);
        return response;
    }

    public static MultiResponse buildFailure(IResponseCode responseCode) {
        MultiResponse response = new MultiResponse();
        response.setSuccess(false);
        response.setErrCode(responseCode.getCode());
        response.setErrMessage(responseCode.getDescription());
        return response;
    }

    public static <T> MultiResponse<T> buildFailure(IResponseCode responseCode, Collection<T> data) {
        MultiResponse response = new MultiResponse();
        response.setSuccess(false);
        response.setErrCode(responseCode.getCode());
        response.setErrMessage(responseCode.getDescription());
        return response;
    }

    public static <T> MultiResponse<T> buildFailure(Collection<T> data) {
        MultiResponse response = new MultiResponse();
        response.setSuccess(false);
        response.setErrCode(GlobalResponseCodeEnum.FAIL.getCode());
        response.setErrMessage(GlobalResponseCodeEnum.FAIL.getDescription());
        return response;
    }

    public static <T> MultiResponse<T> of(Collection<T> data) {
        MultiResponse<T> response = new MultiResponse<>();
        response.setSuccess(true);
        response.setData(data);
        return response;
    }

    public List<T> getData() {
        if (null == data) {
            return Collections.emptyList();
        }
        if (data instanceof List) {
            return (List<T>) data;
        }
        return new ArrayList<>(data);
    }

    public void setData(Collection<T> data) {
        this.data = data;
    }

    public boolean isEmpty() {
        return data == null || data.isEmpty();
    }

    public boolean isNotEmpty() {
        return !isEmpty();
    }

    @Override
    public String toString() {
        return "MultiResponse [data=" + data + ", success=" + isSuccess() + ", errCode=" + getErrCode() + ", errMessage="
                + getErrMessage() + "]";
    }

}
