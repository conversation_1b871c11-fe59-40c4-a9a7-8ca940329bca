<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.neo</groupId>
        <artifactId>xxljob</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>xxljob-app</artifactId>
    <name>xxljob-app</name>

    <!-- notify 不允许单独指定版本-->
    <dependencies>
        <!-- notify 此处为通用依赖引入-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!--  notify 仅允许引用domain      -->
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>xxljob-domain</artifactId>
        </dependency>
    </dependencies>
</project>
