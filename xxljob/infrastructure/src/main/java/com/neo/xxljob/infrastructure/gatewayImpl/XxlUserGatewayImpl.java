package com.neo.xxljob.infrastructure.gatewayImpl;

import com.neo.api.SingleResponse;
import com.neo.user.client.userinfo.api.UserService;
import com.neo.user.client.userinfo.dto.UserInfoDTO;
import com.neo.xxljob.domain.gateway.XxlUserGateway;
import com.neo.xxljob.domain.model.XxlJobUser;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2/27/24
 **/
@Slf4j
@Service
public class XxlUserGatewayImpl implements XxlUserGateway {

    @Resource
    private UserService userService;

    @Override
    public XxlJobUser queryUser(Long userId) {
        try {
            SingleResponse<UserInfoDTO> response = userService.queryUserByUserId(userId);
            if (response != null && response.getData() != null) {
                UserInfoDTO userInfoDTO = response.getData();
                XxlJobUser xxlJobUser = new XxlJobUser();
                xxlJobUser.setId(userInfoDTO.getUserId());
                xxlJobUser.setUsername(userInfoDTO.getUname());
                xxlJobUser.setRole(1);
                return xxlJobUser;
            }
            log.error("XxlUserGatewayImpl failed,userId:{},response:{}", userId, response);
        } catch (Exception e) {
            log.error("XxlUserGatewayImpl excetion,userId:{}", userId, e);
        }
        XxlJobUser xxlJobUser = new XxlJobUser();
        xxlJobUser.setId(1L);
        xxlJobUser.setUsername("admin");
        xxlJobUser.setRole(1);
        return xxlJobUser;
    }
}
