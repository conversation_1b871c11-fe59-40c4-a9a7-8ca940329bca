<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.neo</groupId>
        <artifactId>nova</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>nova-infrastructure</artifactId>
    <name>nova-infrastructure</name>

    <!-- notify 不允许单独指定版本-->
    <dependencies>
        <!-- notify 此处为通用依赖引入-->

        <!-- MySQL 驱动 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!-- SQL Server 驱动 - 兼容Spring Boot 3.x -->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
        </dependency>

        <!-- notify 此处为环境引入-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>

        <!--  notify 仅允许引用domain      -->
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>nova-domain</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>auth-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.neo</groupId>
                    <artifactId>neo-session-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
</project>
