<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.SummeryReportOKRMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.SummeryReportOKR">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="userId" jdbcType="BIGINT"/>
            <result property="uname" column="uname" jdbcType="VARCHAR"/>
            <result property="okrDate" column="okrDate" jdbcType="VARCHAR"/>
            <result property="beginDate" column="beginDate" jdbcType="VARCHAR"/>
            <result property="endDate" column="endDate" jdbcType="VARCHAR"/>
            <result property="dateType" column="dateType" jdbcType="TINYINT"/>
            <result property="objectType" column="objectType" jdbcType="TINYINT"/>
            <result property="objectAmount" column="objectAmount" jdbcType="DECIMAL"/>
            <result property="currentPeriodAmount" column="currentPeriodAmount" jdbcType="DECIMAL"/>
            <result property="circlePeriodAmount" column="circlePeriodAmount" jdbcType="DECIMAL"/>
            <result property="samePeriodAmount" column="samePeriodAmount" jdbcType="DECIMAL"/>
            <result property="aiReport" column="aiReport" jdbcType="VARCHAR"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="created" column="created" jdbcType="BIGINT"/>
            <result property="updated" column="updated" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,userId,uname,
        okrDate,beginDate,endDate,
        dateType,objectType,objectAmount,
        currentPeriodAmount,circlePeriodAmount,samePeriodAmount,
        aiReport,deleted,created,
        updated
    </sql>
</mapper>
