<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.WorkOrderDetailMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.WorkOrderDetail">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenantId" jdbcType="BIGINT"/>
            <result property="creatorId" column="creatorId" jdbcType="BIGINT"/>
            <result property="executorId" column="executorId" jdbcType="BIGINT"/>
            <result property="action" column="action" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="type" column="type" jdbcType="TINYINT"/>
            <result property="sort" column="sort" jdbcType="TINYINT"/>
            <result property="completeTime" column="completeTime" jdbcType="TIMESTAMP"/>
            <result property="workOrderId" column="workOrderId" jdbcType="BIGINT"/>
            <result property="workOrderStartTime" column="workOrderStartTime" jdbcType="TIMESTAMP"/>
            <result property="workOrderEndTime" column="workOrderEndTime" jdbcType="TIMESTAMP"/>
            <result property="actionDetail" column="actionDetail" jdbcType="VARCHAR"/>
            <result property="bizId" column="bizId" jdbcType="VARCHAR"/>
            <result property="created" column="created" jdbcType="TIMESTAMP"/>
            <result property="updated" column="updated" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenantId,creatorId,
        executorId,action,status,
        type,sort,completeTime,
        workOrderId,workOrderStartTime,workOrderEndTime,
        actionDetail,bizId,created,
        updated,deleted
    </sql>
</mapper>
