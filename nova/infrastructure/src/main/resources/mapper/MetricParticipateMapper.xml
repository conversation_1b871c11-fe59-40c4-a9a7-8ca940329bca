<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.MetricParticipateMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.MetricParticipate">
            <id property="id" column="id" />
            <result property="tenantId" column="tenantId" />
            <result property="metricId" column="metricId" />
            <result property="metricCode" column="metricCode" />
            <result property="metricCodeId" column="metricCodeId" />
            <result property="participateId" column="participateId" />
            <result property="participateMetricCode" column="participateMetricCode" />
            <result property="calcType" column="calcType" />
            <result property="createdBy" column="createdBy" />
            <result property="created" column="created" />
            <result property="updatedBy" column="updatedBy" />
            <result property="updated" column="updated" />
            <result property="isDeleted" column="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tenantId,metricId,metricCode,metricCodeId,participateId,
        participateMetricCode,calcType,createdBy,created,updatedBy,updated,
        isDeleted
    </sql>
</mapper>
