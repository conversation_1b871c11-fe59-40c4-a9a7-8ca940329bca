<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.WorkOrderRemarksMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.WorkOrderRemarks">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenantId" jdbcType="BIGINT"/>
            <result property="creatorId" column="creatorId" jdbcType="BIGINT"/>
            <result property="workOrderId" column="workOrderId" jdbcType="BIGINT"/>
            <result property="recipient" column="recipient" jdbcType="BIGINT"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="created" column="created" jdbcType="TIMESTAMP"/>
            <result property="updated" column="updated" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenantId,creatorId,
        workOrderId,recipient,content,
        status,created,updated,
        deleted
    </sql>
</mapper>
