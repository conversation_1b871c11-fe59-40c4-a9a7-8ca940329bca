<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.PerformancePlanMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.PerformancePlan">
            <id property="id" column="id" />
            <result property="tenantId" column="tenantId" />
            <result property="periodType" column="periodType" />
            <result property="periodStart" column="periodStart" />
            <result property="periodEnd" column="periodEnd" />
            <result property="createdBy" column="createdBy" />
            <result property="created" column="created" />
            <result property="updatedBy" column="updatedBy" />
            <result property="updated" column="updated" />
            <result property="isDeleted" column="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tenantId,periodType,periodStart,periodEnd,createdBy,
        created,updatedBy,updated,isDeleted
    </sql>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM PerformancePlan
        WHERE isDeleted = 0
        ORDER BY created DESC
    </select>

    <select id="selectByPlanId" resultType="com.neo.nova.domain.entity.PerformancePlan">
        SELECT
        id,
        tenantId,        <!-- 直接使用驼峰列名 -->
        periodType,          <!-- 直接使用驼峰列名 -->
        periodStart,         <!-- 直接使用驼峰列名 -->
        periodEnd          <!-- 直接使用驼峰列名 -->
        FROM PerformancePlan
        WHERE id = #{planId}
    </select>

</mapper>
