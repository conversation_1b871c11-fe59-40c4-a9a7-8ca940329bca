<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.WorkOrderExtraMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.WorkOrderExtra">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenantId" jdbcType="BIGINT"/>
            <result property="userId" column="userId" jdbcType="BIGINT"/>
            <result property="keyName" column="keyName" jdbcType="VARCHAR"/>
            <result property="keyValue" column="keyValue" jdbcType="VARCHAR"/>
            <result property="workOrderId" column="workOrderId" jdbcType="BIGINT"/>
            <result property="created" column="created" jdbcType="TIMESTAMP"/>
            <result property="updated" column="updated" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenantId,userId,
        keyName,keyValue,workOrderId,
        created,updated,deleted
    </sql>
</mapper>
