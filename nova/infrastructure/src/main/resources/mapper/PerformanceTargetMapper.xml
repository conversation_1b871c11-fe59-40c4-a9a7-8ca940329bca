<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.PerformanceTargetMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.PerformanceTarget">
            <id property="id" column="id" />
            <result property="tenantId" column="tenantId" />
            <result property="planId" column="planId" />
            <result property="targetName" column="targetName" />
            <result property="metricCode" column="metricCode" />
            <result property="metricDataType" column="metricDataType" />
            <result property="unit" column="unit" />
            <result property="targetId" column="targetId" />
            <result property="targetValue" column="targetValue" />
            <result property="parentId" column="parentId" />
            <result property="children" column="children" />
            <result property="ownerId" column="ownerId" />
            <result property="createdBy" column="createdBy" />
            <result property="created" column="created" />
            <result property="updatedBy" column="updatedBy" />
            <result property="updated" column="updated" />
            <result property="isDeleted" column="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tenantId,planId,targetName,metricCode,metricDataType,
        unit,targetId,targetValue,parentId,
        children,ownerId,createdBy,created,updatedBy,
        updated,isDeleted
    </sql>

    <select id="selectCompanyTargets" resultMap="BaseResultMap">
        SELECT *
        FROM PerformanceTarget
        WHERE planId = #{planId}
          AND isDeleted = 0
    </select>

    <select id="selectPersonalTargets" resultMap="BaseResultMap">
        SELECT *
        FROM PerformanceTarget
        WHERE planId = #{planId}
          AND ownerId = #{userId}
          AND isDeleted = 0
    </select>

    <select id="selectChildrenByParentIds" resultMap="BaseResultMap">
        SELECT *
        FROM PerformanceTarget
        WHERE planId = #{planId}
        <!-- 新增条件判断，确保parentIds不为空时才生成IN子句 -->
        <if test="parentIds != null and parentIds.size() > 0">
            AND parentId IN
            <foreach collection="parentIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        AND isDeleted = 0
    </select>

    <!-- 查询分母目标值 -->
    <select id="selectDenominatorTargets" resultMap="BaseResultMap">
        SELECT
            metricType,
            SUM(targetValue) AS targetValue
        FROM PerformanceTarget
        WHERE planId = #{planId}
          AND targetId = 0
          AND targetDimension = 1
          AND isDeleted = 0
        GROUP BY metricType
    </select>

    <update id="softDeleteBatchIds">
        UPDATE PerformanceTarget
        SET isDeleted = 1,
        updatedBy = #{updatedBy},
        updated = #{updated}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="selectBytargetId" resultType="com.neo.nova.domain.entity.PerformanceTarget">
        SELECT *
        FROM PerformanceTarget
        WHERE targetId = #{parentId}
    </select>

    <!-- 批量插入业绩目标数据 -->
    <insert id="batchInsert">
        INSERT INTO `PerformanceTarget` (
        `tenantId`,
        `planId`,
        `targetName`,
        `metricCode`,
        `metricDataType`,
        `unit`,
        `targetId`,
        `targetValue`,
        `parentId`,
        `children`,
        `ownerId`,
        `createdBy`,
        `created`,
        `updatedBy`,
        `updated`,
        `isDeleted`
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.tenantId},
            #{item.planId},
            #{item.targetName},
            #{item.metricCode},
            #{item.metricDataType},
            #{item.unit},
            #{item.targetId},
            #{item.targetValue},
            #{item.parentId},
            #{item.children},
            #{item.ownerId},
            #{item.createdBy},
            #{item.created},
            #{item.updatedBy},
            #{item.updated},
            #{item.isDeleted}
            )
        </foreach>
    </insert>
</mapper>
