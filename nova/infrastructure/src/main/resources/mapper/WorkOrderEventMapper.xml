<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.WorkOrderEventMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.WorkOrderEvent">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenantId" jdbcType="BIGINT"/>
            <result property="workOrderId" column="workOrderId" jdbcType="BIGINT"/>
            <result property="workOrderDetailId" column="workOrderDetailId" jdbcType="BIGINT"/>
            <result property="creatorId" column="creatorId" jdbcType="BIGINT"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="created" column="created" jdbcType="TIMESTAMP"/>
            <result property="updated" column="updated" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="workOrderStartTime" column="workOrderStartTime" jdbcType="TIMESTAMP"/>
            <result property="workOrderEndTime" column="workOrderEndTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenantId,workOrderId,
        workOrderDetailId,creatorId,type,
        description,status,created,
        updated,deleted,workOrderStartTime,
        workOrderEndTime
    </sql>
</mapper>
