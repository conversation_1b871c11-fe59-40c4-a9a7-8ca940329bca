<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.PerformanceTargetMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.PerformanceTarget">
            <id property="id" column="id" />
            <result property="tenantId" column="tenantId" />
            <result property="planId" column="planId" />
            <result property="targetName" column="targetName" />
            <result property="metricCode" column="metricCode" />
            <result property="metricDataType" column="metricDataType" />
            <result property="unit" column="unit" />
            <result property="targetId" column="targetId" />
            <result property="targetValue" column="targetValue" />
            <result property="parentId" column="parentId" />
            <result property="children" column="children" />
            <result property="rootId" column="rootId"/>
            <result property="ownerId" column="ownerId" />
            <result property="createdBy" column="createdBy" />
            <result property="created" column="created" />
            <result property="updatedBy" column="updatedBy" />
            <result property="updated" column="updated" />
            <result property="isDeleted" column="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tenantId,planId,targetName,metricCode,metricDataType,
        unit,targetId,targetValue,parentId,children,rootId,
        ownerId,createdBy,created,updatedBy,updated,
        isDeleted
    </sql>
</mapper>
