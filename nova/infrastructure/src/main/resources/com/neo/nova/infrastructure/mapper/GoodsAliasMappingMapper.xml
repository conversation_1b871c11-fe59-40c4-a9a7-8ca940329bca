<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.GoodsAliasMappingMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.GoodsAliasMapping">
            <id property="id" column="id" />
            <result property="tenantId" column="tenantId" />
            <result property="userId" column="userId" />
            <result property="dataSourceType" column="dataSourceType" />
            <result property="goodsAlias" column="goodsAlias" />
            <result property="goodsId" column="goodsId" />
            <result property="goodsName" column="goodsName" />
            <result property="goodsCode" column="goodsCode" />
            <result property="customerId" column="customerId" />
            <result property="createdBy" column="createdBy" />
            <result property="created" column="created" />
            <result property="updatedBy" column="updatedBy" />
            <result property="updated" column="updated" />
            <result property="isDeleted" column="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tenantId,userId,dataSourceType,goodsAlias,goodsId,
        goodsName,goodsCode,customerId,createdBy,created,
        updatedBy,updated,isDeleted
    </sql>
</mapper>
