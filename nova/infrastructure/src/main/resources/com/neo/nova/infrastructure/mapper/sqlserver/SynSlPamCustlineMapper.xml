<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.sqlserver.SynSlPamCustlineMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.sqlserver.SynSlPamCustline">
        <result property="id" column="ID" jdbcType="INTEGER"/>
        <result property="custlineno" column="CustLineNo" jdbcType="VARCHAR"/>
        <result property="custlinename" column="CustLineName" jdbcType="VARCHAR"/>
        <result property="ustate" column="UState" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,Cust<PERSON>ineNo,CustLineName,
        UState
    </sql>
</mapper>
