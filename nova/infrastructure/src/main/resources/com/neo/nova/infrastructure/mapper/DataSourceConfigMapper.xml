<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.DataSourceConfigMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.DataSourceConfig">
            <id property="id" column="id" />
            <result property="tenantId" column="tenantId" />
            <result property="userId" column="userId" />
            <result property="conversationId" column="conversationId" />
            <result property="dataSource" column="dataSource" />
            <result property="fileName" column="fileName" />
            <result property="fileType" column="fileType" />
            <result property="originFilePath" column="originFilePath" />
            <result property="preProcessFilePath" column="preProcessFilePath" />
            <result property="formatedInfo" column="formatedInfo" />
            <result property="periodType" column="periodType" />
            <result property="startDate" column="startDate" />
            <result property="endDate" column="endDate" />
            <result property="dataStatus" column="dataStatus" />
            <result property="createdBy" column="createdBy" />
            <result property="created" column="created" />
            <result property="updatedBy" column="updatedBy" />
            <result property="updated" column="updated" />
            <result property="remark" column="remark" />
            <result property="isDeleted" column="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tenantId,userId,conversationId,dataSource,fileName,
        fileType,originFilePath,preProcessFilePath,formatedInfo,periodType,
        startDate,endDate,dataStatus,createdBy,created,
        updatedBy,updated,remark,isDeleted
    </sql>
</mapper>
