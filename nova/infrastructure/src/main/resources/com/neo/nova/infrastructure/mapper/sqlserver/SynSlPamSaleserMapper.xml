<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.sqlserver.SynSlPamSaleserMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.sqlserver.SynSlPamSaleser">
        <result property="id" column="ID" jdbcType="INTEGER"/>
        <result property="saleserno" column="SaleserNo" jdbcType="VARCHAR"/>
        <result property="salesername" column="SaleserName" jdbcType="VARCHAR"/>
        <result property="ustate" column="UState" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,SaleserNo,SaleserName,
        UState
    </sql>
</mapper>
