<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.PriceschemepurchaselistMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.Priceschemepurchaselist">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="priceTypeID" column="priceTypeID" jdbcType="BIGINT"/>
        <result property="matelID" column="matelID" jdbcType="BIGINT"/>
        <result property="sellPrice" column="sellPrice" jdbcType="DECIMAL"/>
        <result property="printPrice" column="printPrice" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,priceTypeID,matelID,sellPrice,printPrice
    </sql>
</mapper>
