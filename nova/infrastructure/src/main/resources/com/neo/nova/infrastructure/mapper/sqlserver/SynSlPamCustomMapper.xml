<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.sqlserver.SynSlPamCustomMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.sqlserver.SynSlPamCustom">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="custno" column="CustNo" jdbcType="VARCHAR"/>
        <result property="custname" column="CustName" jdbcType="VARCHAR"/>
        <result property="mnemocode" column="MnemoCode" jdbcType="VARCHAR"/>
        <result property="linkman" column="LinkMan" jdbcType="VARCHAR"/>
        <result property="contactnumber" column="ContactNumber" jdbcType="VARCHAR"/>
        <result property="tdelivyaddress" column="TdelivyAddress" jdbcType="VARCHAR"/>
        <result property="custtypeid" column="CustTypeID" jdbcType="INTEGER"/>
        <result property="custlineid" column="CustLineID" jdbcType="INTEGER"/>
        <result property="custdistrictid" column="CustDistrictID" jdbcType="INTEGER"/>
        <result property="saleserid" column="saleserid" jdbcType="INTEGER"/>
        <result property="ustate" column="UState" jdbcType="CHAR"/>
        <result property="dispprice" column="DispPrice" jdbcType="CHAR"/>
        <result property="padpsw" column="Padpsw" jdbcType="VARCHAR"/>
        <result property="handSelIds" column="hand_sel_ids" jdbcType="VARCHAR"/>
        <result property="custareaid" column="CustAreaID" jdbcType="INTEGER"/>
        <result property="pricetypeid" column="PriceTypeID" jdbcType="INTEGER"/>
        <result property="opendate" column="OpenDate" jdbcType="TIMESTAMP"/>
        <result property="auditflag" column="AuditFlag" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,CustNo,CustName,
        MnemoCode,LinkMan,ContactNumber,
        TdelivyAddress,CustTypeID,CustLineID,
        CustDistrictID,saleserid,UState,
        DispPrice,Padpsw,hand_sel_ids,
        CustAreaID,PriceTypeID,OpenDate,
        AuditFlag
    </sql>
</mapper>
