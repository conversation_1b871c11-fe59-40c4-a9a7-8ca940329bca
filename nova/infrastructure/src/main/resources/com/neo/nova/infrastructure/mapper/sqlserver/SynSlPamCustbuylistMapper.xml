<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.sqlserver.SynSlPamCustbuylistMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.sqlserver.SynSlPamCustbuylist">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="custtypeid" column="CustTypeID" jdbcType="INTEGER"/>
        <result property="mateid" column="MateID" jdbcType="INTEGER"/>
        <result property="sellprice" column="SellPrice" jdbcType="NUMERIC"/>
        <result property="printprice" column="PrintPrice" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,CustTypeID,MateID,
        SellPrice,PrintPrice
    </sql>
</mapper>
