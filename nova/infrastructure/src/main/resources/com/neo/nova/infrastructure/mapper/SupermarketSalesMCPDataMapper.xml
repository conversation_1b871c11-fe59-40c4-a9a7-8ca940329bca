<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.SupermarketSalesMCPDataMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.SupermarketSalesMCPData">
            <id property="id" column="id" />
            <result property="tenantId" column="tenantId" />
            <result property="userIdentity" column="userIdentity" />
            <result property="conversationId" column="conversationId" />
            <result property="supermarketName" column="supermarketName" />
            <result property="itemName" column="itemName" />
            <result property="startDate" column="startDate" />
            <result property="endDate" column="endDate" />
            <result property="count" column="count" />
            <result property="unit" column="unit" />
            <result property="amount" column="amount" />
            <result property="dataStatus" column="dataStatus" />
            <result property="createdBy" column="createdBy" />
            <result property="created" column="created" />
            <result property="updatedBy" column="updatedBy" />
            <result property="updated" column="updated" />
            <result property="isDeleted" column="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tenantId,userIdentity,conversationId,supermarketName,itemName,
        startDate,endDate,count,unit,amount,
        dataStatus,createdBy,created,updatedBy,updated,
        isDeleted
    </sql>
</mapper>
