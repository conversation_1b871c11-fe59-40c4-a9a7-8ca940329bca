<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.CustomerAliasMappingMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.CustomerAliasMapping">
            <id property="id" column="id" />
            <result property="tenantId" column="tenantId" />
            <result property="userId" column="userId" />
            <result property="dataSourceType" column="dataSourceType" />
            <result property="customerAlias" column="customerAlias" />
            <result property="customerId" column="customerId" />
            <result property="customerName" column="customerName" />
            <result property="createdBy" column="createdBy" />
            <result property="created" column="created" />
            <result property="updatedBy" column="updatedBy" />
            <result property="updated" column="updated" />
            <result property="isDeleted" column="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tenantId,userId,dataSourceType,customerAlias,customerId,
        customerName,createdBy,created,updatedBy,updated,
        isDeleted
    </sql>
</mapper>
