<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.sqlserver.SynIvPamMatetypeMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.sqlserver.SynIvPamMatetype">
        <result property="id" column="ID" jdbcType="INTEGER"/>
        <result property="matetypeno" column="MateTypeNo" jdbcType="VARCHAR"/>
        <result property="matetypename" column="MateTypeName" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="ustate" column="Ustate" jdbcType="CHAR"/>
        <result property="depth" column="Depth" jdbcType="INTEGER"/>
        <result property="parentid" column="ParentID" jdbcType="INTEGER"/>
        <result property="ordercode" column="OrderCode" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,MateTypeNo,MateTypeName,
        description,Ustate,Depth,
        ParentID,OrderCode
    </sql>
</mapper>
