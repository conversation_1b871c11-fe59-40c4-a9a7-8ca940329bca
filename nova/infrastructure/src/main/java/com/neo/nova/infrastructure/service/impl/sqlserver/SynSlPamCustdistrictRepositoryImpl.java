package com.neo.nova.infrastructure.service.impl.sqlserver;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.sqlserver.SynSlPamCustdistrict;
import com.neo.nova.domain.gateway.sqlserver.SynSlPamCustdistrictRepository;
import com.neo.nova.infrastructure.mapper.sqlserver.SynSlPamCustdistrictMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【syn_sl_pam_custdistrict】的数据库操作Service实现
 * @createDate 2025-07-18 11:31:05
 */
@Service
public class SynSlPamCustdistrictRepositoryImpl extends ServiceImpl<SynSlPamCustdistrictMapper, SynSlPamCustdistrict>
        implements SynSlPamCustdistrictRepository {

}
