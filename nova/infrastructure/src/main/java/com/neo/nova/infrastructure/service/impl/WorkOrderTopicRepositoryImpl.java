package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.WorkOrderTopic;
import com.neo.nova.domain.gateway.IWorkOrderTopicRepository;
import com.neo.nova.infrastructure.mapper.WorkOrderTopicMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【WorkOrderTopic(工单话题表)】的数据库操作Service实现
* @createDate 2025-07-10 12:06:35
*/
@Service
public class WorkOrderTopicRepositoryImpl extends ServiceImpl<WorkOrderTopicMapper, WorkOrderTopic>
    implements IWorkOrderTopicRepository{

}




