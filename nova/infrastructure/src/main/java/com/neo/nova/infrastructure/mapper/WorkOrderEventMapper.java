package com.neo.nova.infrastructure.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.neo.nova.domain.entity.WorkOrderEvent;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【WorkOrderEvent(工单事件表)】的数据库操作Mapper
* @createDate 2025-07-23 20:58:43
* @Entity com.neo.nova.domain.entity.WorkOrderEvent
*/
@Mapper
@DS("mysql")
public interface WorkOrderEventMapper extends BaseMapper<WorkOrderEvent> {

}




