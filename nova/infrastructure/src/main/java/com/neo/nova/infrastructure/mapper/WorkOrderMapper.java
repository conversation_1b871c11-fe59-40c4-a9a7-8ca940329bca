package com.neo.nova.infrastructure.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.neo.nova.domain.entity.WorkOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【WorkOrder(工单主表)】的数据库操作Mapper
* @createDate 2025-07-10 12:06:35
* @Entity com.neo.nova.domain.entity.WorkOrder
*/
@Mapper
@DS("mysql")
public interface WorkOrderMapper extends BaseMapper<WorkOrder> {

    /**
     * 即将超时工单
     *
     * @param excutorId
     * @return
     */
    long getExpiringCount(Long excutorId);
}




