package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.WorkOrderEvent;
import com.neo.nova.domain.gateway.IWorkOrderEventRepository;
import com.neo.nova.infrastructure.mapper.WorkOrderEventMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【WorkOrderEvent(工单事件表)】的数据库操作Service实现
* @createDate 2025-07-23 20:58:43
*/
@Service
public class WorkOrderEventRepositoryImpl extends ServiceImpl<WorkOrderEventMapper, WorkOrderEvent>
    implements IWorkOrderEventRepository{

}




