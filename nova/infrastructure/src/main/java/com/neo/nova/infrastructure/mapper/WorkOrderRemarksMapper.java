package com.neo.nova.infrastructure.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.neo.nova.domain.entity.WorkOrderRemarks;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【WorkOrderRemarks(工单表备注)】的数据库操作Mapper
* @createDate 2025-07-10 12:06:35
* @Entity com.neo.nova.domain.entity.WorkOrderRemarks
*/
@Mapper
@DS("mysql")
public interface WorkOrderRemarksMapper extends BaseMapper<WorkOrderRemarks> {

}




