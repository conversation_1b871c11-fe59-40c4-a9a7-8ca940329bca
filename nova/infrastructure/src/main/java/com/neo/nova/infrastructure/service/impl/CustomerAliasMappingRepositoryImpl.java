package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.CustomerAliasMapping;
import com.neo.nova.domain.gateway.CustomerAliasMappingRepository;
import com.neo.nova.infrastructure.mapper.CustomerAliasMappingMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【CustomerAliasMapping(客户别名映射表)】的数据库操作Service实现
* @createDate 2025-07-24 21:20:42
*/
@Service
public class CustomerAliasMappingRepositoryImpl extends ServiceImpl<CustomerAliasMappingMapper, CustomerAliasMapping>
    implements CustomerAliasMappingRepository {

}




