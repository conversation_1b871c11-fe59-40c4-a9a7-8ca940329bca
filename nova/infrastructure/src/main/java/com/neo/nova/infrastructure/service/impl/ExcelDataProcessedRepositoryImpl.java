package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.ExcelDataProcessed;
import com.neo.nova.domain.gateway.ExcelDataProcessedRepository;
import com.neo.nova.infrastructure.mapper.ExcelDataProcessedMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【ExcelDataProcessed(Excel加工后数据表 - 存储匹配后的数据)】的数据库操作Service实现
* @createDate 2025-07-24 21:20:42
*/
@Service
public class ExcelDataProcessedRepositoryImpl extends ServiceImpl<ExcelDataProcessedMapper, ExcelDataProcessed>
    implements ExcelDataProcessedRepository {

}




