package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.DataSourceConfig;
import com.neo.nova.domain.gateway.DataSourceConfigRepository;
import com.neo.nova.infrastructure.mapper.DataSourceConfigMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【DataSourceConfig(数据源配置表)】的数据库操作Service实现
* @createDate 2025-07-24 21:20:42
*/
@Service
public class DataSourceConfigRepositoryImpl extends ServiceImpl<DataSourceConfigMapper, DataSourceConfig>
    implements DataSourceConfigRepository {

}




