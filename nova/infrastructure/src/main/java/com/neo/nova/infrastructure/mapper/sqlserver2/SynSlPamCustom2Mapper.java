package com.neo.nova.infrastructure.mapper.sqlserver2;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.domain.entity.sqlserver.SynSlPamCustom;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【syn_sl_pam_Custom】的数据库操作Mapper
* @createDate 2025-07-18 11:31:05
* @Entity .domain.SynSlPamCustom
*/
@Mapper
@DS("sqlserver2")
public interface SynSlPamCustom2Mapper extends BaseMapper<SynSlPamCustom> {


}
