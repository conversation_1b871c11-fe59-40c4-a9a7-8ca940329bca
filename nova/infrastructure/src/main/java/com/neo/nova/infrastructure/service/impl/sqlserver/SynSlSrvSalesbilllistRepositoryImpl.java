package com.neo.nova.infrastructure.service.impl.sqlserver;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.sqlserver.SynSlSrvSalesbilllist;
import com.neo.nova.domain.gateway.sqlserver.SynSlSrvSalesbilllistRepository;
import com.neo.nova.infrastructure.mapper.sqlserver.SynSlSrvSalesbilllistMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【syn_sl_srv_SalesBillList】的数据库操作Service实现
 * @createDate 2025-07-18 11:31:11
 */
@Service
public class SynSlSrvSalesbilllistRepositoryImpl extends ServiceImpl<SynSlSrvSalesbilllistMapper, SynSlSrvSalesbilllist>
        implements SynSlSrvSalesbilllistRepository {

}
