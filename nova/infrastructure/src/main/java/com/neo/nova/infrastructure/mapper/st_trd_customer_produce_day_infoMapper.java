package com.neo.nova.infrastructure.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.neo.nova.domain.entity.st_trd_customer_produce_day_info;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【st_trd_customer_produce_day_info(销售额日统计表)】的数据库操作Mapper
* @createDate 2025-07-08 17:24:03
* @Entity com.neo.nova.domain.entity.st_trd_customer_produce_day_info
*/
@Mapper
@DS("mysql")
public interface st_trd_customer_produce_day_infoMapper extends BaseMapper<st_trd_customer_produce_day_info> {

}




