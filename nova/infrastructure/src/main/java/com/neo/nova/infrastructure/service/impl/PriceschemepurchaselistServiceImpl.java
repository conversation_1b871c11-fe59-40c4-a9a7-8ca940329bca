package com.neo.nova.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.Priceschemepurchaselist;
import com.neo.nova.domain.gateway.PriceschemepurchaselistService;
import com.neo.nova.infrastructure.mapper.PriceschemepurchaselistMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【PriceSchemePurchaseList(价格方案对应的购货清单)】的数据库操作Service实现
 * @createDate 2025-07-19 13:58:06
 */
@Service
public class PriceschemepurchaselistServiceImpl extends ServiceImpl<PriceschemepurchaselistMapper, Priceschemepurchaselist>
        implements PriceschemepurchaselistService {

}
