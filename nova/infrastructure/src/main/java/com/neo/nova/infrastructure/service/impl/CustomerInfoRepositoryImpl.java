package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.CustomerInfo;
import com.neo.nova.domain.gateway.CustomerInfoRepository;
import com.neo.nova.infrastructure.mapper.CustomerInfoMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【CustomerInfo(客户信息表)】的数据库操作Service实现
* @createDate 2025-07-18 15:45:07
*/
@Service
public class CustomerInfoRepositoryImpl extends ServiceImpl<CustomerInfoMapper, CustomerInfo>
    implements CustomerInfoRepository {

}




