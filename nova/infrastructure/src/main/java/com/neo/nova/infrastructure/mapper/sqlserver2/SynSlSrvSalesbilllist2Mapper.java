package com.neo.nova.infrastructure.mapper.sqlserver2;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.domain.entity.sqlserver.SynSlSrvSalesBill;
import com.neo.nova.domain.entity.sqlserver.SynSlSrvSalesbilllist;
import org.apache.ibatis.annotations.Mapper;


/**
 * <AUTHOR>
 * @description 针对表【syn_sl_srv_SalesBillList】的数据库操作Mapper
 * @createDate 2025-07-18 11:31:11
 * @Entity .domain.SynSlSrvSalesbilllist
 */
@Mapper
@DS("sqlserver2")
public interface SynSlSrvSalesbilllist2Mapper extends BaseMapper<SynSlSrvSalesbilllist> {

    // 使用动态分页拦截器，无需自定义分页方法
}
