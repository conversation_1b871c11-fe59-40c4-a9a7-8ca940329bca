package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.WorkOrderExtra;
import com.neo.nova.domain.gateway.IWorkOrderExtraRepository;
import com.neo.nova.infrastructure.mapper.WorkOrderExtraMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【WorkOrderExtra(工单关联信息)】的数据库操作Service实现
* @createDate 2025-07-10 12:06:35
*/
@Service
public class WorkOrderExtraRepositoryImpl extends ServiceImpl<WorkOrderExtraMapper, WorkOrderExtra>
    implements IWorkOrderExtraRepository{

}




