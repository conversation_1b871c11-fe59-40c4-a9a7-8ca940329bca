package com.neo.nova.infrastructure.service.impl.sqlserver;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.sqlserver.SynIvPamMatetype;
import com.neo.nova.domain.gateway.sqlserver.SynIvPamMatetypeRepository;
import com.neo.nova.infrastructure.mapper.sqlserver.SynIvPamMatetypeMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【syn_iv_pam_matetype】的数据库操作Service实现
 * @createDate 2025-07-18 11:31:05
 */
@Service
public class SynIvPamMatetypeRepositoryImpl extends ServiceImpl<SynIvPamMatetypeMapper, SynIvPamMatetype>
        implements SynIvPamMatetypeRepository {

}
