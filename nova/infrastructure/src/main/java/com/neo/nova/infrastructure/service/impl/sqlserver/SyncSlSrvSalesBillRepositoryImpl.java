package com.neo.nova.infrastructure.service.impl.sqlserver;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.sqlserver.SynSlSrvSalesBill;
import com.neo.nova.domain.gateway.sqlserver.SyncSlSrvSalesBillRepository;
import com.neo.nova.infrastructure.mapper.sqlserver.SynSlSrvSalesBillMapper;
import org.springframework.stereotype.Service;


@Service
public class SyncSlSrvSalesBillRepositoryImpl extends ServiceImpl<SynSlSrvSalesBillMapper, SynSlSrvSalesBill>
        implements SyncSlSrvSalesBillRepository {

}




