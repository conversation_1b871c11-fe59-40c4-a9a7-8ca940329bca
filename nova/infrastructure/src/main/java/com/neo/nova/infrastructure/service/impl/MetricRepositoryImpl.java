package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.Metric;
import com.neo.nova.domain.gateway.MetricRepository;
import com.neo.nova.infrastructure.mapper.MetricMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【Metric(指标表)】的数据库操作Service实现
* @createDate 2025-07-03 14:54:38
*/
@Service
public class MetricRepositoryImpl extends ServiceImpl<MetricMapper, Metric>
    implements MetricRepository {

}




