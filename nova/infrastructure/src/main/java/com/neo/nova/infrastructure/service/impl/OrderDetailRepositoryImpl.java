package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.OrderDetail;
import com.neo.nova.domain.gateway.OrderDetailRepository;
import com.neo.nova.infrastructure.mapper.OrderDetailMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【OrderDetail(订单明细表)】的数据库操作Service实现
* @createDate 2025-07-18 15:45:07
*/
@Service
public class OrderDetailRepositoryImpl extends ServiceImpl<OrderDetailMapper, OrderDetail>
    implements OrderDetailRepository {

}




