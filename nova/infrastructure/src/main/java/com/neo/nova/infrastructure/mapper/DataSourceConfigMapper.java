package com.neo.nova.infrastructure.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.domain.entity.DataSourceConfig;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【DataSourceConfig(数据源配置表)】的数据库操作Mapper
* @createDate 2025-07-24 21:20:42
* @Entity com.neo.nova.infrastructure.domain.DataSourceConfig
*/
@Mapper
@DS("mysql")
public interface DataSourceConfigMapper extends BaseMapper<DataSourceConfig> {

}




