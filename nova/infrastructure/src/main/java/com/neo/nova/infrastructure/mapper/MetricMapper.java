package com.neo.nova.infrastructure.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.neo.nova.domain.entity.Metric;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【Metric(指标表)】的数据库操作Mapper
* @createDate 2025-07-03 14:54:38
* @Entity com.neo.nova.domain.entity.Metric
*/
@Mapper
@DS("mysql")
public interface MetricMapper extends BaseMapper<Metric> {

}




