package com.neo.nova.infrastructure.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.neo.nova.domain.entity.WorkOrderDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【WorkOrderDetail(工单明细)】的数据库操作Mapper
* @createDate 2025-07-10 12:06:35
* @Entity com.neo.nova.domain.entity.WorkOrderDetail
*/
@Mapper
@DS("mysql")
public interface WorkOrderDetailMapper extends BaseMapper<WorkOrderDetail> {


}




