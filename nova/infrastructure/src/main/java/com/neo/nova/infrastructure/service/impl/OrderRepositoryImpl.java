package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.Order;
import com.neo.nova.domain.gateway.OrderRepository;
import com.neo.nova.infrastructure.mapper.OrderMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【Order(订单基本信息表)】的数据库操作Service实现
* @createDate 2025-07-18 15:45:07
*/
@Service
public class OrderRepositoryImpl extends ServiceImpl<OrderMapper, Order>
    implements OrderRepository {

}




