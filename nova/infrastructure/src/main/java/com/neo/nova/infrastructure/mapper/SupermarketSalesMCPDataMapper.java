package com.neo.nova.infrastructure.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.neo.nova.domain.entity.SupermarketSalesMCPData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【SupermarketSalesMCPData(商超数据收集表)】的数据库操作Mapper
* @createDate 2025-07-16 18:38:58
* @Entity com.neo.nova.domain.entity.SupermarketSalesMCPData
*/
@Mapper
@DS("mysql")
public interface SupermarketSalesMCPDataMapper extends BaseMapper<SupermarketSalesMCPData> {

}




