package com.neo.nova.infrastructure.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.neo.nova.domain.entity.OrderDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【OrderDetail(订单明细表)】的数据库操作Mapper
* @createDate 2025-07-18 15:45:07
* @Entity com.neo.nova.domain.entity.OrderDetail
*/
@Mapper
@DS("mysql")
public interface OrderDetailMapper extends BaseMapper<OrderDetail> {

}




