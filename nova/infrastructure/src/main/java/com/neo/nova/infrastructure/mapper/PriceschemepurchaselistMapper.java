package com.neo.nova.infrastructure.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.domain.entity.Priceschemepurchaselist;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description 针对表【PriceSchemePurchaseList(价格方案对应的购货清单)】的数据库操作Mapper
 * @createDate 2025-07-19 13:58:06
 * @Entity .domain.Priceschemepurchaselist
 */
@Mapper
@DS("mysql")
public interface PriceschemepurchaselistMapper extends BaseMapper<Priceschemepurchaselist> {


}
