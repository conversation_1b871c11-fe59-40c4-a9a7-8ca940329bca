package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.WorkOrderDetail;
import com.neo.nova.domain.gateway.IWorkOrderDetailRepository;
import com.neo.nova.infrastructure.mapper.WorkOrderDetailMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【WorkOrderDetail(工单明细)】的数据库操作Service实现
* @createDate 2025-07-10 12:06:35
*/
@Service
public class WorkOrderDetailRepositoryImpl extends ServiceImpl<WorkOrderDetailMapper, WorkOrderDetail>
    implements IWorkOrderDetailRepository{

}




