package com.neo.nova.infrastructure.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.neo.nova.domain.entity.SummeryReportOKR;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【SummeryReportOKR(每日总结报告表)】的数据库操作Mapper
* @createDate 2025-08-06 12:24:17
* @Entity com.neo.nova.infrastructure.domain.nova.Summeryreportokr
*/
@Mapper
@DS("mysql")
public interface SummeryReportOKRMapper extends BaseMapper<SummeryReportOKR> {

}




