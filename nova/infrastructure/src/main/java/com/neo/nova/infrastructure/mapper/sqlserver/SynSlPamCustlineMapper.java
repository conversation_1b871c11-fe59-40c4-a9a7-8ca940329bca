package com.neo.nova.infrastructure.mapper.sqlserver;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.domain.entity.sqlserver.SynSlPamCustline;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【syn_sl_pam_custline】的数据库操作Mapper
* @createDate 2025-07-18 11:31:05
* @Entity .domain.SynSlPamCustline
*/
@Mapper
@DS("sqlserver1")
public interface SynSlPamCustlineMapper extends BaseMapper<SynSlPamCustline> {


}
