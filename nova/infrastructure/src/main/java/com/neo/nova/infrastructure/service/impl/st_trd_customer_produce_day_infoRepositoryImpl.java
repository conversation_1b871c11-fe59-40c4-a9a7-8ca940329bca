package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.st_trd_customer_produce_day_info;
import com.neo.nova.domain.gateway.st_trd_customer_produce_day_infoRepository;
import com.neo.nova.infrastructure.mapper.st_trd_customer_produce_day_infoMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【st_trd_customer_produce_day_info(销售额日统计表)】的数据库操作Service实现
* @createDate 2025-07-08 17:24:03
*/
@Service
public class st_trd_customer_produce_day_infoRepositoryImpl extends ServiceImpl<st_trd_customer_produce_day_infoMapper, st_trd_customer_produce_day_info>
    implements st_trd_customer_produce_day_infoRepository {

}




