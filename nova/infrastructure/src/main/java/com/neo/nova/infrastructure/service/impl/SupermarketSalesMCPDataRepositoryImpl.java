package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.SupermarketSalesMCPData;
import com.neo.nova.domain.gateway.SupermarketSalesMCPDataRepository;
import com.neo.nova.infrastructure.mapper.SupermarketSalesMCPDataMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【SupermarketSalesMCPData(商超数据收集表)】的数据库操作Service实现
* @createDate 2025-07-16 18:38:58
*/
@Service
public class SupermarketSalesMCPDataRepositoryImpl extends ServiceImpl<SupermarketSalesMCPDataMapper, SupermarketSalesMCPData>
    implements SupermarketSalesMCPDataRepository {

}




