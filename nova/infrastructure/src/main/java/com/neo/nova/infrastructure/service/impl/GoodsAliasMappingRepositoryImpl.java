package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.GoodsAliasMapping;
import com.neo.nova.domain.gateway.GoodsAliasMappingRepository;
import com.neo.nova.infrastructure.mapper.GoodsAliasMappingMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【GoodsAliasMapping(货品别名映射表)】的数据库操作Service实现
* @createDate 2025-07-24 21:20:42
*/
@Service
public class GoodsAliasMappingRepositoryImpl extends ServiceImpl<GoodsAliasMappingMapper, GoodsAliasMapping>
    implements GoodsAliasMappingRepository {

}




