package com.neo.nova.infrastructure.mapper.sqlserver;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.domain.entity.sqlserver.SynIvPamMatetype;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【syn_iv_pam_matetype】的数据库操作Mapper
* @createDate 2025-07-18 11:31:05
* @Entity com.neo.nova.domain.entity.sqlserver.SynIvPamMatetype
*/
@Mapper
@DS("sqlserver1")
public interface SynIvPamMatetypeMapper extends BaseMapper<SynIvPamMatetype> {


}
