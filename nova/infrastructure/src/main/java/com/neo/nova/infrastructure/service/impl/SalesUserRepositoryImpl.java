package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.neo.nova.domain.entity.SalesUser;

import com.neo.nova.domain.gateway.SalesUserRepository;

import com.neo.nova.infrastructure.mapper.SalesUserMapper;
import org.springframework.stereotype.Service;

@Service
public class SalesUserRepositoryImpl extends ServiceImpl<SalesUserMapper, SalesUser> implements SalesUserRepository {
}
