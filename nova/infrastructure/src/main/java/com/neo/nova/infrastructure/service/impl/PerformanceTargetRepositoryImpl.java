package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.PerformanceTarget;
import com.neo.nova.domain.gateway.PerformanceTargetRepository;
import com.neo.nova.infrastructure.mapper.PerformanceTargetMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【PerformanceTarget(业绩目标表)】的数据库操作Service实现
* @createDate 2025-07-08 20:33:27
*/
@Service
public class PerformanceTargetRepositoryImpl extends ServiceImpl<PerformanceTargetMapper, PerformanceTarget>
    implements PerformanceTargetRepository {

}




