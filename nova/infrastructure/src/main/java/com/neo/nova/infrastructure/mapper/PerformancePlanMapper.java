package com.neo.nova.infrastructure.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.neo.nova.domain.entity.PerformancePlan;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【PerformancePlan(业绩计划表)】的数据库操作Mapper
* @createDate 2025-07-03 14:54:38
* @Entity com.neo.nova.domain.entity.PerformancePlan
*/
@Mapper
@DS("mysql")
public interface PerformancePlanMapper extends BaseMapper<PerformancePlan> {

    List<PerformancePlan> selectAll();

    PerformancePlan selectByPlanId(Long planId);

}




