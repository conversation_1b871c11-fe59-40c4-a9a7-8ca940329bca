package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.GoodsInfo;
import com.neo.nova.domain.gateway.GoodsInfoRepository;
import com.neo.nova.infrastructure.mapper.GoodsInfoMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【GoodsInfo(货品信息表)】的数据库操作Service实现
* @createDate 2025-07-18 15:45:07
*/
@Service
public class GoodsInfoRepositoryImpl extends ServiceImpl<GoodsInfoMapper, GoodsInfo>
    implements GoodsInfoRepository {

}




