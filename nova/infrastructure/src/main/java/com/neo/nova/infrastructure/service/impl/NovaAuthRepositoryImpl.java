package com.neo.nova.infrastructure.service.impl;

import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.auth.client.rpc.RbacService;
import com.neo.auth.client.util.TenantGenAppKeyOrSecretUtil;
import com.neo.nova.domain.gateway.INovaAuthRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class NovaAuthRepositoryImpl implements INovaAuthRepository {

    @Value("${auth.appKey}")
    private String appKey;


    @Value("${auth.appSecret}")
    private String appSecret;

    @Autowired
    private RbacService rbacService;

    @Override
    public List<Long> getAllUserIdsByPermission(Long tenantId, String permission) {
        try {
            MultiResponse<Long> response = rbacService.getAllWorkIdByPermissionV2(
                    tenantId,
                    permission,
                    TenantGenAppKeyOrSecretUtil.genAppKeyOrSecret(tenantId, appKey),
                    TenantGenAppKeyOrSecretUtil.genAppKeyOrSecret(tenantId, appSecret));

            if (!response.isSuccess()) {
                log.error("checkRoleByWorkIdV2 error:{}", response.getErrMessage());
                throw new RuntimeException(response.getErrMessage());
            }
            return response.getData();
        } catch (Exception e) {
            log.error("getAllPermissionsByWorkIdV2 error", e);
        }
        return new ArrayList<>();
    }
}
