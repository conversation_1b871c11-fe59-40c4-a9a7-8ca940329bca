package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.PerformancePlan;
import com.neo.nova.domain.gateway.PerformancePlanRepository;
import com.neo.nova.infrastructure.mapper.PerformancePlanMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【PerformancePlan(业绩计划表)】的数据库操作Service实现
* @createDate 2025-07-03 14:54:38
*/
@Service
public class PerformancePlanRepositoryImpl extends ServiceImpl<PerformancePlanMapper, PerformancePlan>
    implements PerformancePlanRepository {

}




