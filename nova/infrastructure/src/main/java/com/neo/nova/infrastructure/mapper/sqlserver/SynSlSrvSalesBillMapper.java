package com.neo.nova.infrastructure.mapper.sqlserver;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.domain.entity.sqlserver.SynSlSrvSalesBill;
import org.apache.ibatis.annotations.Mapper;

/**
 * SQL Server 1 数据源的销售单据Mapper
 * 使用动态分页拦截器，无需自定义分页方法
 */
@Mapper
@DS("sqlserver1")
public interface SynSlSrvSalesBillMapper extends BaseMapper<SynSlSrvSalesBill> {

}
