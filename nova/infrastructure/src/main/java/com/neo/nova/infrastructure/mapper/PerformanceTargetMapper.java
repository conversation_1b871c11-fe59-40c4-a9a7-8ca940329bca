package com.neo.nova.infrastructure.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.neo.nova.domain.entity.PerformanceTarget;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
* <AUTHOR>
* @description 针对表【PerformanceTarget(业绩目标表)】的数据库操作Mapper
* @createDate 2025-07-08 20:33:27
* @Entity com.neo.nova.domain.entity.PerformanceTarget
*/
@Mapper
@DS("mysql")
public interface PerformanceTargetMapper extends BaseMapper<PerformanceTarget> {
    @Select("SELECT SUM(targetValue) FROM performance_target WHERE planId = #{planId} AND isDeleted = 0")
    Double selectTargetSumByPlanId(Long planId);


}




