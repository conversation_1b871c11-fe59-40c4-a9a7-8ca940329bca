package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.SummeryReportOKR;
import com.neo.nova.domain.gateway.SummeryReportOKRService;
import com.neo.nova.infrastructure.mapper.SummeryReportOKRMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【SummeryReportOKR(每日总结报告表)】的数据库操作Service实现
* @createDate 2025-08-06 12:24:17
*/
@Service
public class SummeryReportOKRServiceImpl extends ServiceImpl<SummeryReportOKRMapper, SummeryReportOKR>
    implements SummeryReportOKRService {

}




