package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.WorkOrderRemarks;
import com.neo.nova.domain.gateway.IWorkOrderRemarksRepository;
import com.neo.nova.infrastructure.mapper.WorkOrderRemarksMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【WorkOrderRemarks(工单表备注)】的数据库操作Service实现
* @createDate 2025-07-10 12:06:35
*/
@Service
public class WorkOrderRemarksRepositoryImpl extends ServiceImpl<WorkOrderRemarksMapper, WorkOrderRemarks>
    implements IWorkOrderRemarksRepository{

}




