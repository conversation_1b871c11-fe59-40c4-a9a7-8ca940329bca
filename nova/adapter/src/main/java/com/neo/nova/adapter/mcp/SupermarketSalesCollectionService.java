package com.neo.nova.adapter.mcp;

import com.neo.nova.app.request.SupermarketSalesInfo;
import com.neo.nova.domain.dto.SupermarketSalesMCPDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/10
 **/
public interface SupermarketSalesCollectionService {

    /**
     * 上传商超名称、货品名称，指定时间段内的销售类数据
     *
     * @param supermarketSalesInfo
     * @return
     */
    String supermarketSalesCollect(SupermarketSalesInfo supermarketSalesInfo);

    /**
     * 查询商超销量数据
     *
     * @param conversationId
     * @return
     */
    List<SupermarketSalesMCPDTO> supermarketSalesQuery(Long conversationId);


}
