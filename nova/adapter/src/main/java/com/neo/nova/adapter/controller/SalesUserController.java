package com.neo.nova.adapter.controller;

import com.neo.api.MultiResponse;
import com.neo.api.PageResponse;
import com.neo.api.SingleResponse;
import com.neo.nova.app.service.SalesUserService;
import com.neo.nova.app.vo.SalesUserQueryVO;
import com.neo.nova.app.vo.SalesUserSearchVO;
import com.neo.nova.domain.dto.SalesUserDTO;
import com.neo.nova.domain.dto.SalesUserDetailDTO;
import com.neo.nova.domain.dto.SalesUserInfoDTO;
import com.neo.session.SessionContextHolder;
import com.neo.session.annotation.NeedLogin;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 业务员管理
 */
@Slf4j
@RestController
@RequestMapping("/salesuser")
public class SalesUserController {
    @Autowired
    private SalesUserService salesUserService;

    /**
     * 获取业务员列表
     *
     * @param salesUserQueryVO
     * @return
     */
    @PostMapping("/list")
    @ResponseBody
    @NeedLogin
    public PageResponse<SalesUserDTO> list(@RequestBody(required = false) SalesUserQueryVO salesUserQueryVO) {
        Long tenantId = SessionContextHolder.getTenantId();
        salesUserQueryVO.setTenantId(tenantId);
        SalesUserInfoDTO salesUserInfoDTO = salesUserService.list(salesUserQueryVO);
        return PageResponse.of(salesUserInfoDTO.getSalesUserDTOS(), salesUserInfoDTO.getTotalCount(), salesUserQueryVO.getPageSize(), salesUserQueryVO.getPageIndex());

    }

    /**
     * 获取业务员详情
     *
     * @param userId
     * @return
     */
    @GetMapping("/detail")
    @NeedLogin
    @ResponseBody
    public SingleResponse<SalesUserDetailDTO> getDetail(Long userId) {
        Long tenantId = SessionContextHolder.getTenantId();
        SalesUserDetailDTO salesUserDetailDTO = salesUserService.getDetail(userId, tenantId);
        return SingleResponse.buildSuccess(salesUserDetailDTO);
    }

//    /**
//     * 模糊查询
//     *
//     * @param salesUserSearchVO
//     * @return
//     */
//
//    @PostMapping("/search")
//    @ResponseBody
//    @NeedLogin
//    public PageResponse<SalesUserDTO> search(@RequestBody(required = false) SalesUserSearchVO salesUserSearchVO) {
//        Long tenantId = SessionContextHolder.getTenantId();
//        salesUserSearchVO.setTenantId(tenantId);
//        SalesUserInfoDTO salesUserInfoDTO = salesUserService.search(salesUserSearchVO);
//        return PageResponse.of(salesUserInfoDTO.getSalesUserDTOS(), salesUserInfoDTO.getTotalCount(), salesUserSearchVO.getPageSize(), salesUserSearchVO.getPageIndex());
//
//    }


}
