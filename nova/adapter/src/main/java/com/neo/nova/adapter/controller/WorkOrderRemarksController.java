package com.neo.nova.adapter.controller;

import com.neo.api.MultiResponse;
import com.neo.api.Response;
import com.neo.nova.app.service.WorkOrderRemarksService;
import com.neo.nova.app.vo.WorkOrderRemarkVO;
import com.neo.nova.domain.dto.WorkOrderRemarkDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 工单备注
 */
@RequestMapping("/workOrderRemarks")
@RestController
public class WorkOrderRemarksController {
    @Autowired
    private WorkOrderRemarksService workOrderRemarksService;

    /**
     * 增加工单备注
     * @param workOrderRemarkVO
     * @return
     */

    @PostMapping("save")
    public Response saveWorkOrderRemarks(@RequestBody WorkOrderRemarkVO workOrderRemarkVO) {
        Boolean result= workOrderRemarksService.saveWorkOrderRemark(workOrderRemarkVO);
        if (Boolean.TRUE.equals(result)) {
            return Response.buildSuccess();
        } else {
            return Response.buildFailure("PERFORMANCE_OPERATION_FAILED", "工单备注新增失败");
        }
    }

    /**
     * 更新工单备注
     * @param workOrderRemarkVO
     * @return
     */
    @PostMapping("update")
    public Response updateWorkOrderRemarks(@RequestBody WorkOrderRemarkVO workOrderRemarkVO) {
        Boolean result= workOrderRemarksService.updateWorkOrderRemark(workOrderRemarkVO);
        if (Boolean.TRUE.equals(result)) {
            return Response.buildSuccess();
        } else {
            return Response.buildFailure("PERFORMANCE_OPERATION_FAILED", "工单备注修改失败");
        }
    }

    /**
     * 工单备注列表查询
     * @param workOrderRemarkVO
     * @return
     */
    @PostMapping("list")
    public MultiResponse<WorkOrderRemarkDTO> listWorkOrderRemarks(@RequestBody WorkOrderRemarkVO workOrderRemarkVO) {
        List<WorkOrderRemarkDTO> workOrderRemarkDTOList= workOrderRemarksService.listWorkOrderRemark(workOrderRemarkVO);
   return MultiResponse.of(workOrderRemarkDTOList);
    }

    /**
     * 删除工单备注
     * @param id
     * @return
     */
    @PostMapping("delete")
    public Response deleteWorkOrderRemarks(@Param("id") Long id) {
        Boolean result = workOrderRemarksService.deleteWorkOrderRemark(id);
        if (Boolean.TRUE.equals(result)) {
            return Response.buildSuccess();
        } else {
            return Response.buildFailure("PERFORMANCE_OPERATION_FAILED", "工单备注删除失败");
        }
    }

}
