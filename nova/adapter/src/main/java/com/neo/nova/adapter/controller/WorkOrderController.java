package com.neo.nova.adapter.controller;

import com.neo.api.PageResponse;
import com.neo.api.SingleResponse;
import com.neo.nova.app.action.ActionFactory;
import com.neo.nova.app.action.actionType.ActionSubTypeEnums;
import com.neo.nova.app.action.enums.ActionRelationEnums;
import com.neo.nova.app.action.models.ActionBaseModel;
import com.neo.nova.app.action.models.ActionTaskModel;
import com.neo.nova.app.service.CustomerRemarkService;
import com.neo.nova.app.service.WorkOrderOperationService;
import com.neo.nova.app.vo.*;
import com.neo.nova.domain.gateway.IWorkOrderEventRepository;
import com.neo.session.SessionContextHolder;
import com.neo.session.annotation.NeedLogin;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 *
 * 工单表的接口
 *
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/10 14:42
 */
@RequestMapping("/workOrder")
@RestController
@Slf4j
public class WorkOrderController {

    @Resource
    private WorkOrderOperationService workOrderOperationService;
    @Resource
    private IWorkOrderEventRepository workOrderEventRepository;

    @Resource
    private CustomerRemarkService customerRemarkService;

    @Resource
    private ActionFactory actionFactory;

    /**
     * 执行任务
     *
     * @param actionBaseModel
     * @return
     */
    @NeedLogin
    @PostMapping("/execute")
    public Boolean execute(@RequestBody ActionBaseModel actionBaseModel) {

        actionBaseModel.setUserId(SessionContextHolder.getUserId());

        return actionFactory.execute(actionBaseModel);

    }

    /**
     * 创建工单
     *
     * @param actionTaskModel
     * @return
     */
    @PostMapping("/create")
    @NeedLogin
    public SingleResponse<Boolean> create(@RequestBody ActionTaskModel actionTaskModel) {
        actionTaskModel.setTenantId(SessionContextHolder.getTenantId());
        boolean result = false;
        result = actionFactory.createTask(actionTaskModel);
        return SingleResponse.buildSuccess(result);
    }

    /**
     * 更新任务
     *
     * @param actionTaskModel
     * @return
     */
    @PostMapping("/update")
    @NeedLogin
    public SingleResponse<Boolean> update(@RequestBody ActionTaskModel actionTaskModel) {
        actionTaskModel.setTenantId(SessionContextHolder.getTenantId());
        return workOrderOperationService.updateWorkOrderAndDetail(actionTaskModel);
    }

    /**
     * 查询工单列表
     *
     * @param request 查询请求参数
     * @return 工单列表响应
     */
    @PostMapping("/query")
    public PageResponse<WorkOrderListItemDTO> queryWorkOrders(@Valid @RequestBody WorkOrderQueryRequest request) {
        //request.setExecutorId(null);
        return workOrderOperationService.queryWorkOrders(request);
    }

    @PostMapping("/queryexception")
    public PageResponse<WorkOrderListItemDTO> queryExceptionWorkOrders(@Valid @RequestBody WorkOrderQueryRequest request) {
        //request.setExecutorId(null);
        return workOrderOperationService.queryExceptionWorkOrders(request);
    }

    /**
     * 查询首页工单数量
     *
     * @return
     */
    @NeedLogin
    @GetMapping("/queryOrderCount")
    public SingleResponse<WorkOrderCountVo> queryOrderCount() {
        return SingleResponse.buildSuccess(workOrderOperationService.queryWorkOrderCount());
    }


    /**
     * 查询我的工单列表
     *
     * @param request 查询请求参数
     * @return 工单列表响应
     */
    @PostMapping("/querymine")
    @NeedLogin
    public PageResponse<WorkOrderListItemDTO> queryMyWorkOrders(@Valid @RequestBody WorkOrderQueryRequest request) {
        request.setExecutorId(SessionContextHolder.getUserId());
        return workOrderOperationService.queryWorkOrders(request);
    }

    /**
     * 已读
     *
     * @return 工单列表响应
     */
    @PostMapping("/read/{workOrderId}")
    @NeedLogin
    public SingleResponse<Boolean> read(@PathVariable Long workOrderId) {
        boolean res = workOrderOperationService.read(workOrderId);
        return SingleResponse.buildSuccess(res);
    }

    /**
     * 查询工单详情
     *
     * @param workOrderId 工单ID
     * @return 工单详情响应
     */
    @GetMapping("/detail/{workOrderId}")
    public SingleResponse<WorkOrderDetailVO> queryOrderDetail(@PathVariable Long workOrderId) {
        return workOrderOperationService.getWorkOrderDetail(workOrderId);
    }

    /**
     * 工单事件详情
     * @param workOrderId
     * @return
     */
    @GetMapping("/eventdetail/{workOrderId}")
    public SingleResponse<List<WorkOrderEventVO>> eventDetail(@PathVariable Long workOrderId) {
        return workOrderOperationService.eventDetail(workOrderId);
    }



//    /**
//     * 查询简单工单详情，就是每个子tab的详情
//     *
//     * @param workOrderId 工单ID
//     * @return 详情响应
//     */
//    @GetMapping("/simple/detail/{workOrderId}")
//    public SingleResponse<SimpleDetailResponse> querySimpleDetail(@PathVariable Long workOrderId) {
//        return workOrderOperationService.getVisitDetail(workOrderId);
//    }


    /**
     * 查询导购日常打卡详情
     *
     * @param workOrderId 工单ID
     * @return 打卡详情响应
     */
    @GetMapping("/checkin/detail/{workOrderId}")
    public SingleResponse<CheckInDetailResponse> queryCheckInDetail(@PathVariable Long workOrderId) {
        return workOrderOperationService.getCheckInDetail(workOrderId);
    }

    /**
     * 获取工单类型列表
     *
     * @return 工单类型响应
     */
    @GetMapping("/workordertypes")
    public SingleResponse<WorkOrderTypeResponse> getWorkOrderTypes() {
        return workOrderOperationService.getWorkOrderTypes();
    }

    /**
     * 根据工单类型获取子类型列表
     *
     * @param actionName 工单类型名称
     * @return 工单子类型响应
     */
    @GetMapping("/subtypes/{actionName}")
    public SingleResponse<WorkOrderSubTypeResponse> getWorkOrderSubTypes(@PathVariable Integer actionName) {
        return workOrderOperationService.getWorkOrderSubTypes(actionName);
    }

    /**
     * 根据子类型获取处理工单的可选项
     *
     * @param subType
     * @return
     */
    @GetMapping("/event/{subType}")
    public SingleResponse<WorkOrderSubTypeResponse> getEventBySubType(@PathVariable Integer subType) {
        return workOrderOperationService.getEventBySubType(subType);
    }

    /**
     * 组装标题
     *
     * @param
     * @return
     */
    @GetMapping("/title")
    public SingleResponse<String> generateTitle(WorkOrderTitleRequest workOrderTitleRequest) {

        String title = ActionSubTypeEnums.map.get(workOrderTitleRequest.getSubType());
        if(StringUtils.isNotBlank(workOrderTitleRequest.getCustomerName())){
            title  += "-" + workOrderTitleRequest.getCustomerName();
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        //汇报加上时间
        if(workOrderTitleRequest.getType() == ActionRelationEnums.REPORT.getActionType()
                || workOrderTitleRequest.getType() == ActionRelationEnums.CHECK_IN.getActionType()){
            title += " "+ LocalDateTime.now().format(formatter);
        }

        return SingleResponse.buildSuccess(title);
    }

    @GetMapping("/status")
    public SingleResponse<WorkOrderSubTypeResponse> getWorkOrderStatus() {
        return workOrderOperationService.getWorkOrderStatus();
    }


}
