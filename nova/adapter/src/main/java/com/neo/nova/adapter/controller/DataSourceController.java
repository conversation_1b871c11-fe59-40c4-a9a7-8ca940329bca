package com.neo.nova.adapter.controller;

import com.alibaba.fastjson.JSON;
import com.neo.api.MultiResponse;
import com.neo.api.PageResponse;
import com.neo.api.Response;
import com.neo.api.SingleResponse;
import com.neo.common.time.TimeUtils;
import com.neo.nova.app.request.DataSourceListRequest;
import com.neo.nova.app.request.DataSourceUploadRequest;
import com.neo.nova.app.service.DataSourceService;
import com.neo.nova.app.service.ExcelDataProcessService;
import com.neo.nova.app.service.SupermarketSalesService;
import com.neo.nova.app.vo.DataSourceConfigVO;
import com.neo.nova.app.vo.PendingDataResponse;
import com.neo.nova.app.vo.PendingQueryVO;
import com.neo.nova.domain.dto.TimeCondition;
import com.neo.nova.domain.entity.DataSourceConfig;
import com.neo.nova.domain.entity.ExcelDataProcessed;
import com.neo.nova.domain.entity.SupermarketSalesMCPData;
import com.neo.nova.domain.enums.DataSourceDataStatus;
import com.neo.session.SessionContextHolder;
import com.neo.session.annotation.NeedLogin;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 数据源管理
 *
 * <AUTHOR>
 * @since 2025/7/4
 **/
@RestController
@RequestMapping("/dataSource")
@Slf4j
public class DataSourceController {

    @Resource
    private DataSourceService dataSourceService;

    @Resource
    private SupermarketSalesService supermarketSalesService;

    @Resource
    private ExcelDataProcessService excelDataProcessService;

    @PostMapping("/list")
    @NeedLogin
    @ResponseBody
    public PageResponse<DataSourceConfigVO> list(@RequestBody(required = false) DataSourceListRequest dataSourceListRequest) {
        if (dataSourceListRequest == null) dataSourceListRequest = new DataSourceListRequest();
        dataSourceListRequest.setTenantId(SessionContextHolder.getTenantId());
        return dataSourceService.list(dataSourceListRequest);
    }

    /**
     * 查询数据源类型列表
     *
     * @return
     */
    @GetMapping("/listDataSourceType")
    @NeedLogin
    public MultiResponse<Map<String, String>> listDataSourceType() {

        List<Map<String, String>> list = Lists.newArrayList();
        list.add(Map.of("id", "商超通用", "name", "商超销售额汇总模板", "templateUrl", "https://neoimg-1251964405.cos.ap-shanghai.myqcloud.com/neo_export/test2/e6fb5e1d-6daf-472d-be09-0a68881db2a2.xlsx"));
        list.add(Map.of("id", "宏昌", "name", "宏昌销售额模板", "templateUrl", "https://neoimg-1251964405.cos.ap-shanghai.myqcloud.com/neo_export/test2/9be8d7ea-a257-4436-b623-222808b1decf.xlsx"));
        list.add(Map.of("id", "国光", "name", "国光销售额模板", "templateUrl", ""));
        list.add(Map.of("id", "勤润订货商品明细", "name", "勤润订货商品明细", "templateUrl", ""));
        return MultiResponse.of(list);
    }

    /**
     * 文件上传接口，支持excel、image
     * 新增入参dataSourceName和数据开始时间、数据结束时间（yyyy-MM-dd格式）
     * 根据dataSourceName不同，支持内存处理或原python处理
     * 如果dataSourceName = 宏昌，使用easyexcel读取文档并根据时间范围存入对应表
     *
     * @param dataSourceUploadRequest 数据上传请求，包含dataSourceName、startDate、endDate等参数
     * @return 处理结果
     */
    @PostMapping("/upload")
    @NeedLogin
    public SingleResponse<Long> processCdnFile(@RequestBody DataSourceUploadRequest dataSourceUploadRequest) {
        dataSourceUploadRequest.setUserId(SessionContextHolder.getUserId());
        dataSourceUploadRequest.setTenantId(SessionContextHolder.getTenantId());
        return dataSourceService.processDataImport(dataSourceUploadRequest);
    }

    @PostMapping("/uploadForRpa")
    public SingleResponse<Long> uploadForRpa(@RequestBody DataSourceUploadRequest dataSourceUploadRequest) {
        return dataSourceService.processDataImport(dataSourceUploadRequest);
    }

    @GetMapping("/reUpload")
    @NeedLogin
    public SingleResponse<Object> reUpload(@RequestParam(value = "dataSourceId", required = false) Long dataSourceId,
                                           @RequestParam(value = "dataSourceIds", required = false) String dataSourceIds) {
        List<SingleResponse> responseList = new ArrayList<>();
        if (dataSourceId != null) {
            responseList.add(_reload(dataSourceId));
        }
        if (dataSourceIds != null) {
            for (String id : dataSourceIds.split(",")) {
                responseList.add(_reload(Long.parseLong(id)));
            }
        }
        return SingleResponse.buildSuccess(JSON.toJSONString(responseList));
    }

    private SingleResponse _reload(Long dataSourceId) {
        DataSourceConfig oldDataSourceConfig = dataSourceService.getById(dataSourceId);
        if (oldDataSourceConfig == null) {
            return SingleResponse.buildFailure("400", "未找到数据源配置");
        }
        DataSourceUploadRequest dataSourceUploadRequest = new DataSourceUploadRequest();
        dataSourceUploadRequest.setUserId(SessionContextHolder.getUserId());
        dataSourceUploadRequest.setTenantId(SessionContextHolder.getTenantId());
        dataSourceUploadRequest.setFileUrl(oldDataSourceConfig.getOriginFilePath());
        dataSourceUploadRequest.setFileType(oldDataSourceConfig.getFileType());
        dataSourceUploadRequest.setFileName(oldDataSourceConfig.getFileName());
        if (oldDataSourceConfig.getPeriodType() != null && oldDataSourceConfig.getPeriodType() != 0) {
            dataSourceUploadRequest.setTimeCondition(TimeCondition.builder()
                    .periodType(oldDataSourceConfig.getPeriodType())
                    .startDate(oldDataSourceConfig.getStartDate())
                    .endDate(oldDataSourceConfig.getEndDate())
                    .build());
        }
        dataSourceUploadRequest.setDataSourceType(oldDataSourceConfig.getDataSource());
        return dataSourceService.processDataImport(dataSourceUploadRequest);
    }


    /**
     * 获取待确认的数据列表
     *
     * @return 待确认的数据列表和对应的数据源配置信息
     */
    @PostMapping("/pending")
    @NeedLogin
    public SingleResponse<PendingDataResponse> getPendingData(@RequestBody PendingQueryVO pendingQueryVO) {
        try {
            PendingDataResponse response = excelDataProcessService.getPendingDataWithConfig(pendingQueryVO);
            return SingleResponse.buildSuccess(response);

        } catch (Exception e) {
            log.error("获取待确认数据失败", e);
            return SingleResponse.buildFailure("100", "获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 更新处理后的数据
     *
     * @param ids                处理后的数据ID
     * @param excelDataProcessed 处理后的数据
     * @return 更新结果
     */
    @PostMapping("/update")
    @NeedLogin
    public Response updateProcessedData(@RequestParam(value = "ids", required = false) List<Long> ids, @RequestParam("dataSourceId") Long dataSourceId,
                                        @RequestBody ExcelDataProcessed excelDataProcessed) {
        try {
            long userId = SessionContextHolder.getUserId();
            return excelDataProcessService.updateProcessedData(userId, dataSourceId, ids, excelDataProcessed);
        } catch (Exception e) {
            log.error("更新处理后数据失败", e);
            return Response.buildFailure("100", "更新数据失败: " + e.getMessage());
        }
    }


    /**
     * 确认保存数据到统计表
     *
     * @param dataSourceId 包含数据ID列表的请求
     * @return 保存结果
     */
    @PostMapping("/confirm")
    @NeedLogin
    public Response confirmData(@RequestParam("dataSourceId") Long dataSourceId, @RequestParam(value = "remark", required = false) String remark) {
        try {
            // 获取当前用户ID和租户ID
            Long userId = SessionContextHolder.getUserId();
            Long tenantId = SessionContextHolder.getTenantId();
            return excelDataProcessService.confirmAndSaveData(userId, tenantId, dataSourceId, remark);
        } catch (Exception e) {
            log.error("确认保存数据失败", e);
            return Response.buildFailure("100", "确认保存失败: " + e.getMessage());
        }
    }

    /**
     * 查询商超MPC数据
     *
     * @param conversationId 会话id
     * @return 处理结果
     */
    @PostMapping("/querySupermarketMCPData")
    public MultiResponse<SupermarketSalesMCPData> querySupermarketMCPData(@RequestParam("conversationId") Long conversationId) {
        Long tenantId = SessionContextHolder.getTenantId();
        Long userId = SessionContextHolder.getUserId();
        return MultiResponse.of(supermarketSalesService.list(tenantId, userId, conversationId));
    }


    /**
     * 更新商超MPC数据
     *
     * @param ids                     要更新的id列表
     * @param supermarketSalesMCPData 要更新的数据
     * @return 处理结果
     */
    @PostMapping("/updateSupermarketMCPData")
    public Response updateSupermarketMCPData(@RequestParam("ids") List<Long> ids,
                                             @RequestBody SupermarketSalesMCPData supermarketSalesMCPData) {
        Long tenantId = SessionContextHolder.getTenantId();
        Long userId = SessionContextHolder.getUserId();
        boolean success = supermarketSalesService.batchUpdateByIds(ids, tenantId, userId, supermarketSalesMCPData);
        if (success) {
            return Response.buildSuccess();
        }
        return Response.buildFailure("1005", "批量更新失败");
    }


}
