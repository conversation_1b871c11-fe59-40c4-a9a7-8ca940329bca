package com.neo.nova.adapter.controller;

import com.neo.api.PageResponse;
import com.neo.api.SingleResponse;
import com.neo.nova.app.service.OrderService;
import com.neo.nova.app.vo.OrderQueryVO;
import com.neo.nova.domain.dto.OrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 订单管理
 */
@Slf4j
@RestController
@RequestMapping("/order")
@Validated
public class OrderController {

    @Autowired
    private OrderService orderService;

    /**
     * 创建订单
     *
     * @param orderDTO 订单信息
     * @return 创建结果
     */
    @PostMapping("/create")
    public SingleResponse<OrderDTO> createOrder(@Valid @RequestBody OrderDTO orderDTO) {
        log.info("创建订单请求: {}", orderDTO.getOrderCode());
        return orderService.createOrder(orderDTO);
    }

    /**
     * 统一更新订单接口
     * 支持：订单信息更新、状态更新、发货、完成、取消、付款等操作
     *
     * @param orderDTO 订单信息（包含ID和要更新的字段）
     * @return 更新结果
     */
    @PutMapping("/update")
    public SingleResponse<Boolean> updateOrder(@Valid @RequestBody OrderDTO orderDTO) {
        log.info("更新订单请求: id={}, orderCode={}", orderDTO.getId(), orderDTO.getOrderCode());
        return orderService.updateOrder(orderDTO);
    }

    /**
     * 删除订单
     *
     * @param id 订单ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{id}")
    public SingleResponse<Boolean> deleteOrder(@PathVariable @NotNull Integer id) {
        log.info("删除订单请求: {}", id);
        return orderService.deleteOrder(id);
    }

    /**
     * 统一查询订单接口
     * 支持：单个查询、分页查询、按各种条件查询
     *
     * @param queryVO 查询条件
     * @return 订单列表或单个订单详情
     */
    @PostMapping("/query")
    public Object queryOrders(@RequestBody OrderQueryVO queryVO) {
        log.info("查询订单请求: {}", queryVO);

        // 如果指定了ID，返回单个订单详情
        if (queryVO.getId() != null) {
            log.info("查询单个订单详情: id={}", queryVO.getId());
            return orderService.getOrderById(queryVO.getId());
        }

        // 如果指定了订单编号且要求精确匹配，返回单个订单
        if (queryVO.getOrderCode() != null && queryVO.getExactMatch() != null && queryVO.getExactMatch()) {
            log.info("根据订单编号精确查询: orderCode={}", queryVO.getOrderCode());
            PageResponse<OrderDTO> pageResponse = orderService.pageOrders(queryVO);
            if (pageResponse.isSuccess() && !pageResponse.getData().isEmpty()) {
                return SingleResponse.buildSuccess(pageResponse.getData().get(0));
            } else {
                SingleResponse<OrderDTO> response = new SingleResponse<>();
                response.setSuccess(false);
                response.setErrCode("ORDER_NOT_FOUND");
                response.setErrMessage("订单不存在");
                return response;
            }
        }

        // 其他情况返回分页查询结果
        log.info("分页查询订单: 页码={}, 页大小={}", queryVO.getPageIndex(), queryVO.getPageSize());
        return orderService.pageOrders(queryVO);
    }

    /**
     * 批量删除订单
     *
     * @param ids 订单ID列表
     * @return 删除结果
     */
    @DeleteMapping("/batchDelete")
    public SingleResponse<Boolean> batchDeleteOrders(@RequestBody @NotEmpty List<Integer> ids) {
        log.info("批量删除订单请求: {}", ids);
        return orderService.batchDeleteOrders(ids);
    }

    /**
     * 根据客户ID查询订单
     *
     * @param customerId 客户ID
     * @param pageIndex 页码（可选，默认1）
     * @param pageSize 页大小（可选，默认10）
     * @param includeDetails 是否包含订单明细（可选，默认false）
     * @return 订单列表
     */
    @GetMapping("/customer/{customerId}")
    public PageResponse<OrderDTO> getOrdersByCustomerId(
            @PathVariable @NotNull Long customerId,
            @RequestParam(value = "pageIndex", defaultValue = "1") Integer pageIndex,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "includeDetails", defaultValue = "false") Boolean includeDetails) {
        log.info("根据客户ID查询订单请求: customerId={}, pageIndex={}, pageSize={}, includeDetails={}",
                customerId, pageIndex, pageSize, includeDetails);
        return orderService.getOrdersByCustomerId(customerId, pageIndex, pageSize, includeDetails);
    }

    /**
     * 订单状态常量
     */
    private static class OrderStatus {
        public static final int PENDING = 0;    // 未处理
        public static final int SHIPPED = 1;    // 已发货
        public static final int COMPLETED = 2;  // 已完成
        public static final int CANCELLED = 3;  // 已取消
        public static final int PAID = 4;       // 已付款
        public static final int REFUNDING = 5;  // 退款中
        public static final int REFUNDED = 6;   // 已退款
    }

    /**
     * 订单状态操作快捷方法
     *
     * @param id 订单ID
     * @param operation 操作类型
     * @return 更新结果
     */
    @PutMapping("/status/{id}/{operation}")
    public SingleResponse<Boolean> orderStatusOperation(
            @PathVariable @NotNull Long id,
            @PathVariable @NotNull String operation) {

        log.info("订单状态操作请求: id={}, operation={}", id, operation);

        // 根据操作类型设置相应的状态
        int status;
        switch (operation.toLowerCase()) {
            case "ship":
                status = OrderStatus.SHIPPED;
                break;
            case "complete":
                status = OrderStatus.COMPLETED;
                break;
            case "cancel":
                status = OrderStatus.CANCELLED;
                break;
            case "pay":
                status = OrderStatus.PAID;
                break;
            case "refund":
                status = OrderStatus.REFUNDING;
                break;
            case "refunded":
                status = OrderStatus.REFUNDED;
                break;
            default:
                SingleResponse<Boolean> response = new SingleResponse<>();
                response.setSuccess(false);
                response.setErrCode("INVALID_OPERATION");
                response.setErrMessage("无效的操作类型");
                return response;
        }

        // 创建订单DTO并设置状态
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setId(id);
        orderDTO.setStatus(status);

        // 调用统一更新接口
        return orderService.updateOrder(orderDTO);
    }
}
