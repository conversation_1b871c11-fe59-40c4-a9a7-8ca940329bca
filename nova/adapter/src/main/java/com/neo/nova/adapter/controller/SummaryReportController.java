package com.neo.nova.adapter.controller;


import com.neo.api.SingleResponse;
import com.neo.nova.app.service.SummaryReportService;
import com.neo.nova.domain.dto.SummaryReportDTO;
import com.neo.nova.domain.dto.SummaryReportReqDTO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/report")
public class SummaryReportController {


    @Resource
    private SummaryReportService summaryReportService;

    /**
     * AI员工OKR总结报告
     * @param summaryReportReqDTO 参数，用户id必填
     * @return
     */
    @RequestMapping("/showAISummaryReport")
    public SingleResponse<SummaryReportDTO> showAISummaryReport(@RequestBody SummaryReportReqDTO summaryReportReqDTO) {

        return summaryReportService.getAISummaryReport(summaryReportReqDTO);
    }
}
