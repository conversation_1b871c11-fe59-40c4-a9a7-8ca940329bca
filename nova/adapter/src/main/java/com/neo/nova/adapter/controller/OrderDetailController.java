package com.neo.nova.adapter.controller;

import com.neo.api.PageResponse;
import com.neo.api.SingleResponse;
import com.neo.nova.app.service.OrderDetailService;
import com.neo.nova.app.vo.CustomerPurchasedGoodsVO;
import com.neo.nova.app.vo.OrderDetailQueryVO;
import com.neo.nova.domain.dto.OrderDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 订单明细管理
 */
@Slf4j
@RestController
@RequestMapping("/orderDetail")
@Validated
public class OrderDetailController {

    @Autowired
    private OrderDetailService orderDetailService;

    /**
     * 创建订单明细
     *
     * @param orderDetailDTO 订单明细信息
     * @return 创建结果
     */
    @PostMapping("/create")
    public SingleResponse<OrderDetailDTO> createOrderDetail(@Valid @RequestBody OrderDetailDTO orderDetailDTO) {
        log.info("创建订单明细请求: orderId={}, goodsId={}", orderDetailDTO.getOrderId(), orderDetailDTO.getGoodsId());
        return orderDetailService.createOrderDetail(orderDetailDTO);
    }

    /**
     * 更新订单明细
     *
     * @param orderDetailDTO 订单明细信息（包含ID和要更新的字段）
     * @return 更新结果
     */
    @PutMapping("/update")
    public SingleResponse<Boolean> updateOrderDetail(@Valid @RequestBody OrderDetailDTO orderDetailDTO) {
        log.info("更新订单明细请求: id={}, goodsId={}", orderDetailDTO.getId(), orderDetailDTO.getGoodsId());
        return orderDetailService.updateOrderDetail(orderDetailDTO);
    }

    /**
     * 删除订单明细
     *
     * @param id 订单明细ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{id}")
    public SingleResponse<Boolean> deleteOrderDetail(@PathVariable @NotNull Integer id) {
        log.info("删除订单明细请求: {}", id);
        return orderDetailService.deleteOrderDetail(id);
    }

    /**
     * 获取订单明细详情
     *
     * @param id 订单明细ID
     * @return 订单明细详情
     */
    @GetMapping("/get/{id}")
    public SingleResponse<OrderDetailDTO> getOrderDetailById(@PathVariable @NotNull Integer id) {
        log.info("查询订单明细详情请求: {}", id);
        return orderDetailService.getOrderDetailById(id);
    }

    /**
     * 根据订单ID查询订单明细列表
     *
     * @param orderId 订单ID
     * @return 订单明细列表
     */
    @GetMapping("/order/{orderId}")
    public SingleResponse<List<OrderDetailDTO>> getOrderDetailsByOrderId(@PathVariable @NotNull Long orderId) {
        log.info("根据订单ID查询明细列表请求: orderId={}", orderId);
        return orderDetailService.getOrderDetailsByOrderId(orderId);
    }

    /**
     * 根据客户ID查询订单明细列表
     *
     * @param customerId 客户ID
     * @return 订单明细列表
     */
    @GetMapping("/customer/{customerId}")
    public SingleResponse<List<OrderDetailDTO>> getOrderDetailsByCustomerId(@PathVariable @NotNull Long customerId) {
        log.info("根据客户ID查询明细列表请求: customerId={}", customerId);
        return orderDetailService.getOrderDetailsByCustomerId(customerId);
    }

    /**
     * 根据客户ID查询客户购买过的所有商品信息
     *
     * @param customerId 客户ID
     * @param includeDetails 是否包含购买详情（可选，默认false）
     * @return 客户购买的商品列表
     */
    @GetMapping("/customer/{customerId}/goods")
    public SingleResponse<List<CustomerPurchasedGoodsVO>> getCustomerPurchasedGoods(
            @PathVariable @NotNull Long customerId,
            @RequestParam(value = "includeDetails", defaultValue = "false") Boolean includeDetails) {
        log.info("根据客户ID查询购买商品请求: customerId={}, includeDetails={}", customerId, includeDetails);
        return orderDetailService.getCustomerPurchasedGoods(customerId, includeDetails);
    }

    /**
     * 分页查询订单明细列表
     * 支持多种查询条件：订单ID、客户ID、商品ID、价格范围、时间范围等
     *
     * @param queryVO 查询条件
     * @return 分页查询结果
     */
    @PostMapping("/page")
    public PageResponse<OrderDetailDTO> pageOrderDetails(@RequestBody OrderDetailQueryVO queryVO) {
        log.info("分页查询订单明细请求: pageIndex={}, pageSize={}, orderId={}, customerId={}, goodsId={}",
                queryVO.getPageIndex(), queryVO.getPageSize(), queryVO.getOrderId(),
                queryVO.getCustomerId(), queryVO.getGoodsId());
        return orderDetailService.pageOrderDetails(queryVO);
    }

    /**
     * 统一查询订单明细接口
     * 支持：单个查询、分页查询、按各种条件查询
     *
     * @param queryVO 查询条件
     * @return 订单明细列表或单个订单明细详情
     */
    @PostMapping("/query")
    public Object queryOrderDetails(@RequestBody OrderDetailQueryVO queryVO) {
        log.info("查询订单明细请求: {}", queryVO);

        // 如果指定了ID，返回单个订单明细详情
        if (queryVO.getId() != null) {
            log.info("查询单个订单明细详情: id={}", queryVO.getId());
            return orderDetailService.getOrderDetailById(queryVO.getId());
        }

        // 其他情况返回分页查询结果
        log.info("分页查询订单明细: 页码={}, 页大小={}", queryVO.getPageIndex(), queryVO.getPageSize());
        return orderDetailService.pageOrderDetails(queryVO);
    }
}
