package com.neo.nova.adapter.controller;

import com.neo.api.Response;
import com.neo.nova.app.service.CustomerRemarkService;
import com.neo.nova.domain.dto.CustomerRemarkDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/customerremark")
public class CustomerRemarkController {
    @Autowired
    private CustomerRemarkService customerRemarkService;

    /**
     * 添加跟进记录
     * @param customerRemarkDTO
     * @return
     */
    @RequestMapping("/add")
    public Response add(CustomerRemarkDTO customerRemarkDTO) {
        return customerRemarkService.addCustomerRemark(customerRemarkDTO);
    }
}
