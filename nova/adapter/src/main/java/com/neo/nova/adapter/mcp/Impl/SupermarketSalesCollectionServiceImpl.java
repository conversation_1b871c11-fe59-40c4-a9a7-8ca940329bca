package com.neo.nova.adapter.mcp.Impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.neo.common.time.TimeUtils;
import com.neo.nova.adapter.mcp.SupermarketSalesCollectionService;
import com.neo.nova.app.request.SupermarketSalesInfo;
import com.neo.nova.app.service.ExcelDataProcessService;
import com.neo.nova.domain.constants.DataConstants;
import com.neo.nova.domain.dto.SupermarketSalesMCPDTO;
import com.neo.nova.domain.entity.DataSourceConfig;
import com.neo.nova.domain.entity.ExcelDataProcessed;
import com.neo.nova.domain.entity.SupermarketSalesMCPData;
import com.neo.nova.domain.gateway.DataSourceConfigRepository;
import com.neo.nova.domain.gateway.SupermarketSalesMCPDataRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/10
 **/
@Slf4j
@Service
public class SupermarketSalesCollectionServiceImpl implements SupermarketSalesCollectionService {

    @Resource
    private SupermarketSalesMCPDataRepository supermarketSalesMCPDataRepository;

    @Resource
    private DataSourceConfigRepository dataSourceConfigRepository;

    @Resource
    private ExcelDataProcessService excelDataProcessService;

    @Override
    @Tool(name = "supermarketSalesCollect", description = "上传商超名称、货品名称，指定时间段内的销售类数据")
    public String supermarketSalesCollect(SupermarketSalesInfo supermarketSalesInfo) {
        log.info("开始上传商超销量数据");
        log.info("请求数据: {}", JSON.toJSONString(supermarketSalesInfo));

        try {
            // 参数校验
            paramCheck(supermarketSalesInfo);

            String dataSourceId = supermarketSalesInfo.getDataSourceId();
            DataSourceConfig dataSourceConfig = dataSourceConfigRepository.getById(Long.valueOf(dataSourceId));
            if (dataSourceConfig == null) {
                log.error("数据源不存在, dataSourceId: {}", dataSourceId);
                throw new IllegalArgumentException("数据源不存在");
            }

            List<ExcelDataProcessed> originalDataList = Lists.newArrayList();
            for (SupermarketSalesInfo.SingleSalesInfo singleSalesInfo : supermarketSalesInfo.getSalesInfo()) {
                ExcelDataProcessed data = new ExcelDataProcessed();
                data.setTenantId(dataSourceConfig.getTenantId());
                data.setDataSourceConfigId(dataSourceConfig.getId());
                data.setCustomerNameOriginal(singleSalesInfo.getSupermarketName());
                data.setItemNameOriginal(singleSalesInfo.getItemName());
                if (DataConstants.SUMMARY_GOODS_NAME.equals(singleSalesInfo.getItemName())) {
                    data.setGoodsId(DataConstants.SUMMARY_GOODS_ID);
                }
                data.setStartDate(singleSalesInfo.getStartDate());
                data.setEndDate(singleSalesInfo.getEndDate());
                if (singleSalesInfo.getCount() != null) {
                    data.setSalesQty(new BigDecimal(singleSalesInfo.getCount()));
                }
                data.setSalesUnit(singleSalesInfo.getUnit());
                if (singleSalesInfo.getAmount() != null) {
                    data.setSalesAmount(new BigDecimal(singleSalesInfo.getAmount()));
                }
                data.setConfirmStatus(0);
                data.setCreated(TimeUtils.getCurrentTime());
                data.setUpdated(TimeUtils.getCurrentTime());
                data.setIsDeleted(0);
                originalDataList.add(data);
            }


            if (CollectionUtils.isNotEmpty(originalDataList)) {
                excelDataProcessService.processAndSave(dataSourceConfig.getTenantId(), dataSourceConfig.getUserId(), Long.valueOf(dataSourceId), originalDataList);
                return "数据上传成功，共保存 " + originalDataList.size() + " 条记录";
            }
            log.warn("商超销量数据保存失败, supermarketSalesInfo: {}", JSON.toJSONString(supermarketSalesInfo));
            return "商超销量数据保存失败";
        } catch (Exception e) {
            log.error("上传商超销量数据异常", e);
            return "数据上传失败: " + e.getMessage();
        }
    }

    @Override
    @Tool(name = "supermarketSalesQuery", description = "查询当前会话中被处理的商超销售数据")
    public List<SupermarketSalesMCPDTO> supermarketSalesQuery(Long conversationId) {
        LambdaQueryWrapper<SupermarketSalesMCPData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupermarketSalesMCPData::getConversationId, conversationId);
        List<SupermarketSalesMCPData> list = supermarketSalesMCPDataRepository.list(queryWrapper);
        List<SupermarketSalesMCPDTO> result = new ArrayList<>();
        for (SupermarketSalesMCPData supermarketSalesMCPData : list) {
            SupermarketSalesMCPDTO dto = new SupermarketSalesMCPDTO();
            BeanUtils.copyProperties(supermarketSalesMCPData, dto);
            result.add(dto);
        }
        return result;
    }


    private static void paramCheck(SupermarketSalesInfo supermarketSalesInfo) {
        if (supermarketSalesInfo == null) {
            throw new IllegalArgumentException("请求数据不能为空");
        }
        if (supermarketSalesInfo.getDataSourceId() == null) {
            throw new IllegalArgumentException("用户标识不能为空");
        }
        if (CollectionUtils.isEmpty(supermarketSalesInfo.getSalesInfo())) {
            throw new IllegalArgumentException("销售信息不能为空");
        }
    }
}
