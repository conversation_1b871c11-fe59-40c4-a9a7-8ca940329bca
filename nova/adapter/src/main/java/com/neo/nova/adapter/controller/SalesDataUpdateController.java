package com.neo.nova.adapter.controller;

import com.neo.api.SingleResponse;
import com.neo.nova.app.service.SalesDataUpdateService;
import com.neo.nova.app.vo.SalesDataUpdateRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 销售数据更新控制器
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
@RestController
@RequestMapping("/sales-data")
public class SalesDataUpdateController {

    @Autowired
    private SalesDataUpdateService salesDataUpdateService;

    /**
     * 更新销售数据统计（近一个月）
     */
    @GetMapping("/update")
    public SingleResponse<Boolean> updateSalesStatistics() {
        try {
            log.info("开始执行销售数据统计更新");
            boolean result = salesDataUpdateService.updateSalesStatistics();
            log.info("销售数据统计更新完成，结果：{}", result);
            return SingleResponse.buildSuccess(result);
        } catch (Exception e) {
            log.error("销售数据统计更新失败", e);
            return SingleResponse.buildFailure("5000", "销售数据统计更新失败：" + e.getMessage());
        }
    }


    /**
     * 根据请求参数更新销售数据统计
     * 支持按用户ID、客户ID、时间范围等条件进行过滤更新
     */
    @PostMapping("/update-with-filters")
    public SingleResponse<Boolean> updateSalesStatistics(@RequestBody SalesDataUpdateRequest request) {
        try {
            log.info("开始执行带过滤条件的销售数据统计更新，请求参数：{}", request);
            boolean result = salesDataUpdateService.updateSalesStatistics(request);
            log.info("销售数据统计更新完成，结果：{}", result);
            return SingleResponse.buildSuccess(result);
        } catch (Exception e) {
            log.error("销售数据统计更新失败", e);
            return SingleResponse.buildFailure("5000", "销售数据统计更新失败：" + e.getMessage());
        }
    }

}
