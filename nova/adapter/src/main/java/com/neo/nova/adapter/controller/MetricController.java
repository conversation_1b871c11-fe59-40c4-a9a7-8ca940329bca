package com.neo.nova.adapter.controller;

import com.google.common.collect.Lists;
import com.neo.api.MultiResponse;
import com.neo.api.PageResponse;
import com.neo.api.Response;
import com.neo.nova.app.service.MetricService;
import com.neo.nova.app.vo.MetricCodeVO;
import com.neo.nova.app.vo.MetricDataTypeVO;
import com.neo.nova.domain.dto.MetricDTO;
import com.neo.nova.app.request.ComplexMetricListRequest;
import com.neo.nova.domain.enums.MetricCodeEnum;
import com.neo.nova.domain.enums.MetricCodeIdEnum;
import com.neo.nova.domain.enums.MetricDataTypeEnum;
import com.neo.session.SessionContextHolder;
import com.neo.session.annotation.NeedLogin;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 指标管理
 *
 * <AUTHOR>
 * @since 2025/6/30
 **/
@RestController
@RequestMapping("/metric")
public class MetricController {
    @Autowired
    private MetricService metricService;

    /**
     * 获取指标数据类型枚举
     *
     * @return
     */
    @RequestMapping("/listMetricDataType")
    @NeedLogin
    public MultiResponse<MetricDataTypeVO> listMetricDataType() {
        return MultiResponse.of(Arrays.stream(MetricDataTypeEnum.values()).map(MetricDataTypeVO::new).toList());
    }

    /**
     * 查询复杂指标列表
     *
     * @param complexMetricListRequest
     * @return
     */
    @PostMapping("/queryComplexMetricList")
    @NeedLogin
    public PageResponse<MetricDTO> queryComplexMetricList(@RequestBody ComplexMetricListRequest complexMetricListRequest) {
//        MetricListDTO metricListDTO = metricService.list(complexMetricListRequest);
//
//        return PageResponse.of(metricListDTO.getList(), metricListDTO.getTotalCount(), complexMetricListRequest.getPageSize(), complexMetricListRequest.getPageIndex());
        List<MetricDTO> result = Lists.newArrayList();
        if (StringUtils.isNotBlank(complexMetricListRequest.getMetricCode())) {
            for (MetricCodeIdEnum value : MetricCodeIdEnum.values()) {
                if (value.getParentCode().getCode().equals(complexMetricListRequest.getMetricCode())) {
                    result.add(new MetricDTO(value));
                }
            }
        }
        return PageResponse.of(result, 0, complexMetricListRequest.getPageSize(), complexMetricListRequest.getPageIndex());
    }


    /**
     * @return
     */
    @GetMapping("/init")
    @NeedLogin
    public Response init() {
        if (SessionContextHolder.getTenantId() <= 0L) {
            return Response.buildSuccess();
        }
        metricService.init(SessionContextHolder.getTenantId());
        return Response.buildSuccess();
    }


}
