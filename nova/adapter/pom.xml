<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.neo</groupId>
        <artifactId>nova</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>nova-adapter</artifactId>
    <name>nova-adapter</name>

    <!-- notify 不允许单独指定版本-->
    <dependencies>
        <!-- notify 此处为通用依赖引入-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- notify 此处为环境引入-->

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-mcp-server-webmvc</artifactId>
        </dependency>


        <!-- notify 仅允许引app和client-->
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>nova-app</artifactId>
        </dependency>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>nova-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-session-sdk</artifactId>
        </dependency>


    </dependencies>
</project>
