package com.neo.nova.app.sync.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.api.SingleResponse;
import com.neo.common.time.TimeUtils;
import com.neo.nova.app.action.enums.SqlServerSourceEnums;
import com.neo.nova.app.sync.AbstractTableSyncConfig;
import com.neo.nova.domain.entity.GoodsInfo;
import com.neo.nova.domain.entity.sqlserver.SynIvPamMaterial;
import com.neo.nova.domain.enums.GoodsStatusEnum;
import com.neo.nova.infrastructure.mapper.GoodsInfoMapper;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;
import com.neo.tagcenter.client.param.BaseTagQueryOption;
import com.neo.tagcenter.client.param.BusinessTreeTagQueryParam;
import com.neo.tagcenter.client.rpc.BusinessTreeTagReadReadService;
import com.neo.user.client.userinfo.api.UserService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

public abstract class AbstractSynIvPamMaterialConfigImpl extends AbstractTableSyncConfig<SynIvPamMaterial, GoodsInfo> {

    @Resource
    private GoodsInfoMapper goodsInfoMapper;

    @Resource
    private BusinessTreeTagReadReadService businessTreeTagReadReadService;

    @Resource
    private UserService userService;

    @Override
    public BaseMapper<GoodsInfo> getMysqlMapper() {
        return goodsInfoMapper;
    }

    @Override
    public String getSqlServerTableName() {
        return "syn_iv_pam_Material";
    }

    @Override
    public String getMysqlTableName() {
        return "GoodsInfo";
    }

    @Override
    public QueryWrapper<SynIvPamMaterial> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynIvPamMaterial> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("ID");
        return null;
    }

    @Override
    public boolean needUpdate(GoodsInfo existData, GoodsInfo updateData) {
        if (existData == null || updateData == null) {
            return false;
        }

        int enableToUpdate = enableToUpdate(getSqlserverSource().getValue(), existData.getOutType(),
                updateData.getStatus(), existData.getStatus(), GoodsStatusEnum.NORMAL.getCode());
        if (enableToUpdate == 1) {
            return true;
        } else if (enableToUpdate == -1) {
            if (getSqlserverSource() == SqlServerSourceEnums.SQLSERVER2 &&
                    StrUtil.isBlank(existData.getOutId2())) {
                String outId2 = updateData.getOutId2();
                BeanUtils.copyProperties(new GoodsInfo(), updateData);

                updateData.setOutId2(outId2);
                return true;
            }
            return false;
        }

        if (!stringCompare(existData.getCode(), updateData.getCode())) {
            return true;
        }
        if (!stringCompare(existData.getName(), updateData.getName())) {
            return true;
        }
        if (!stringCompare(existData.getMnemoCode(), updateData.getMnemoCode())) {
            return true;
        }
        if (!Objects.equals(existData.getGoodsTypeId(), updateData.getGoodsTypeId())) {
            return true;
        }
        long updateShipperId = updateData.getShipperId() == null ? 0 : updateData.getShipperId();
        if (!Objects.equals(existData.getShipperId(), updateShipperId)) {
            return true;
        }
        if (!stringCompare(existData.getSpec(), updateData.getSpec())) {
            return true;
        }
        if (!stringCompare(existData.getUnit(), updateData.getUnit())) {
            return true;
        }
        if (!bigDecimalCompare(existData.getJWeight(), updateData.getJWeight())) {
            return true;
        }
        if (!bigDecimalCompare(existData.getMWeight(), updateData.getMWeight())) {
            return true;
        }
        if (!bigDecimalCompare(existData.getRspPrice(), updateData.getRspPrice())) {
            return true;
        }
        if (!bigDecimalCompare(existData.getWsPrice(), updateData.getWsPrice())) {
            return true;
        }
        if (!bigDecimalCompare(existData.getGbPrice(), updateData.getGbPrice())) {
            return true;
        }
        if (!bigDecimalCompare(existData.getSbPrice(), updateData.getSbPrice())) {
            return true;
        }
        if (!Objects.equals(existData.getOemFlag(), updateData.getOemFlag())) {
            return true;
        }

        return false;
    }

    @Override
    public String getMysqlPrimaryKeyValue(GoodsInfo data) {
        return switch (getSqlserverSource()) {
            case LOCAL -> "";
            case SQLSERVER1 -> data.getOutId1();
            case SQLSERVER2 -> data.getOutId2();
        };
    }

    @Override
    public String getSqlServerPrimaryKeyValue(SynIvPamMaterial data) {
        return String.valueOf(data.getId());
    }

    @Override
    public String getSqlServerNameValue(SynIvPamMaterial data) {
        return data.getMatename();
    }

    @Override
    public String getMysqlNameColumnName() {
        return "name";
    }

    @Override
    public String getMysqlNameValue(GoodsInfo data) {
        return data.getName();
    }

    @Override
    public GoodsInfo convertToMysqlEntity(SynIvPamMaterial data) {
        return null;
    }


    @Override
    public Map<String, GoodsInfo> batchConvertToMysqlEntityWithPrimaryKey(List<SynIvPamMaterial> updatedData) {
        return convertToGoodsInfoMap(updatedData, this::getMysqlPrimaryKeyValue);
    }

    @Override
    public Map<String, GoodsInfo> batchConvertToMysqlEntityWithName(List<SynIvPamMaterial> updatedData) {
        return convertToGoodsInfoMap(updatedData, this::getMysqlNameValue);
    }

    private Map<String, GoodsInfo> convertToGoodsInfoMap(List<SynIvPamMaterial> updatedData, Function<GoodsInfo, String> keyMapper) {
        List<Integer> mateTypeIds = updatedData.stream()
                .map(SynIvPamMaterial::getMatetypeid)
                .toList();

        Map<String, TagLeafInfoDto> tagLeafInfoDtos = batchQueryGoodsCategorySetting(mateTypeIds);

        Map<String, GoodsInfo> goodsInfoMap = new HashMap<>();
        for (SynIvPamMaterial data : updatedData) {
            TagLeafInfoDto tagLeafInfoDto = tagLeafInfoDtos.get(String.valueOf(data.getMatetypeid()));
            GoodsInfo goodsInfo = fromMaterial(data, tagLeafInfoDto);
            goodsInfoMap.put(keyMapper.apply(goodsInfo), goodsInfo);
        }
        return goodsInfoMap;
    }

    @Override
    public void setId(GoodsInfo existData, GoodsInfo updateData) {
        updateData.setId(existData.getId());
    }

    private Map<String, TagLeafInfoDto> batchQueryGoodsCategorySetting(List<Integer> matetypeids) {
        // 关联ID映射
        BusinessTreeTagQueryParam param = new BusinessTreeTagQueryParam();
        // 使用动态的租户ID作为businessDomain
        param.setBusinessDomain(getTenantId().intValue());
        switch (getSqlserverSource()) {
            case SQLSERVER1:
                param.setOutId1List(CollUtil.isNotEmpty(matetypeids) ? matetypeids.stream().map(String::valueOf).toList() : null);
                break;
            case SQLSERVER2:
                param.setOutId2List(CollUtil.isNotEmpty(matetypeids) ? matetypeids.stream().map(String::valueOf).toList() : null);
                break;
        }

        BaseTagQueryOption option = new BaseTagQueryOption();
        option.setIncludeDeleted(true);
        option.setIncludeDisable(true);
        SingleResponse<Map<String, TagLeafInfoDto>> queryResult = businessTreeTagReadReadService.batchQueryGoodsCategorySetting(param, option);
        if (queryResult.isSuccess() && MapUtil.isNotEmpty(queryResult.getData())) {
            return queryResult.getData();
        }

        return MapUtil.newHashMap();
    }

    /**
     * SynIvPamMaterial转GoodsInfo
     *
     * @param material
     * @return
     */
    public GoodsInfo fromMaterial(SynIvPamMaterial material, TagLeafInfoDto tagLeafInfoDto) {
        if (material == null) {
            return null;
        }

        GoodsInfo goodsInfo = new GoodsInfo();

        // 基本信息映射
        switch (getSqlserverSource()) {
            case SQLSERVER1:
                goodsInfo.setOutId1(String.valueOf(material.getId()));
                break;
            case SQLSERVER2:
                goodsInfo.setOutId2(String.valueOf(material.getId()));
                break;
        }
        goodsInfo.setOutType(getSqlserverSource().getValue());

        goodsInfo.setCode(String.valueOf(material.getMateno()));
        goodsInfo.setName(material.getMatename());
        goodsInfo.setMnemoCode(material.getMnemocode());
        goodsInfo.setSpec(material.getSpec());
        goodsInfo.setUnit(material.getUnit());
        goodsInfo.setJWeight(material.getJweight() == null ? BigDecimal.ZERO : material.getJweight());
        goodsInfo.setMWeight(material.getMweight() == null ? BigDecimal.ZERO : material.getMweight());


        goodsInfo.setGoodsTypeId(tagLeafInfoDto != null ? tagLeafInfoDto.getId() : -1);

//        if (material.getShipperid() != null) {
//            SingleResponse<UserInfoDTO> userInfoDTO = userService.queryByUserThirdParty(String.valueOf(material.getShipperid()), ThirdPartyEnum.OUT1.getCode(), "ww9c836769f4ec7e7d");
//            if (userInfoDTO.isSuccess()) {
//                if (userInfoDTO.getData() != null) {
//                    goodsInfo.setShipperId(userInfoDTO.getData().getUserId());
//                } else {
//                    goodsInfo.setShipperId(-1L);
//                }
//            }
//        }
        //TODO
        goodsInfo.setShipperId(-1L);

        goodsInfo.setRspPrice(material.getRspPrice() == null ? BigDecimal.ZERO : material.getRspPrice().setScale(5, RoundingMode.HALF_UP));
        goodsInfo.setWsPrice(material.getWsPrice() == null ? BigDecimal.ZERO : material.getWsPrice().setScale(5, RoundingMode.HALF_UP));
        goodsInfo.setGbPrice(material.getGbPrice() == null ? BigDecimal.ZERO : material.getGbPrice().setScale(5, RoundingMode.HALF_UP));
        goodsInfo.setSbPrice(material.getSbPrice() == null ? BigDecimal.ZERO : material.getSbPrice().setScale(5, RoundingMode.HALF_UP));
        goodsInfo.setOemFlag(material.getOemFlag());

        // 状态映射
        goodsInfo.setStatus(material.getUstate());
        goodsInfo.setIsDeleted(0);

        // 设置默认值
        goodsInfo.setTenantId(getTenantId()); // 默认租户ID
        goodsInfo.setCreated(TimeUtils.getCurrentTime());
        goodsInfo.setUpdated(TimeUtils.getCurrentTime());

        return goodsInfo;
    }
}
