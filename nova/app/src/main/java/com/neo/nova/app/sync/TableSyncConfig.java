package com.neo.nova.app.sync;

import java.util.List;
import java.util.Map;

/**
 * 表同步配置接口
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
public interface TableSyncConfig<T, R> extends SqlServerTableConfig<T>, MysqlTableConfig<R> {

    /**
     * 从MySQL查询现有数据
     *
     * @param updatedData 更新的数据
     * @return 现有数据Map，key为主键值
     */
    Map<String, R> queryExistingDataFromMysql(List<T> updatedData);


    /**
     * 从MySQL查询现有数据
     *
     * @param updatedData 更新的数据
     * @return 现有数据Map，key为主键值
     */
    Map<String, R> queryRepeatNameFromMysql(List<T> updatedData);

    /**
     * 转换为MySQL实体
     *
     * @param data SQL Server中的数据
     * @return MySQL实体
     */
    R convertToMysqlEntity(T data);

    /**
     * 批量转换为MySQL实体
     *
     * @param dataList SQL Server中的数据列表
     * @return MySQL实体列表
     */
//    List<R> batchConvertToMysqlEntity(List<T> dataList);


    /**
     * 转换为MySQL实体
     * TODO
     *
     * @return MySQL实体
     */
//    List<R> batchConvertToMysqlEntity(List<T> dataList);

    /**
     * 设置租户ID，用于businessDomain
     *
     * @param tenantId 租户ID
     */
    void setTenantId(Long tenantId);

    /**
     * 获取租户ID
     *
     * @return 租户ID
     */
    Long getTenantId();

    Map<String, R> batchConvertToMysqlEntityWithPrimaryKey(List<T> updatedData);

    Map<String, R> batchConvertToMysqlEntityWithName(List<T> updatedData);
}
