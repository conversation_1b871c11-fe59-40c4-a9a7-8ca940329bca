package com.neo.nova.app.sync;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 内部ID关联表同步配置抽象基类
 * 专门用于处理使用内部ID的关联表（多对多关系表）的同步逻辑
 *
 * 这类关联表的特点：
 * 1. 主键是自增ID
 * 2. 外键字段存储的是内部系统的ID，不是外部系统的ID
 * 3. 不需要outId1、outId2等外部ID字段
 * 4. 同步时根据业务唯一键（外键组合）来判断是否为同一条记录
 * 5. 需要处理外键关联的数据转换和查找
 *
 * <AUTHOR>
 * @since 2025/8/4
 */
@Slf4j
public abstract class AbstractInternalIdRelationTableSyncConfig<T, R> extends AbstractTableSyncConfig<T, R> {

    /**
     * 获取关联表的业务唯一键组合
     * 用于判断两条记录是否为同一条业务记录
     *
     * @param data 数据对象
     * @return 业务唯一键的组合字符串
     */
    public abstract String getBusinessUniqueKey(R data);

    /**
     * 获取SQL Server数据的业务唯一键组合
     * 需要先转换外键关联后再生成业务唯一键
     *
     * @param data SQL Server数据对象
     * @return 业务唯一键的组合字符串，如果外键转换失败返回null
     */
    public abstract String getSqlServerBusinessUniqueKey(T data);

    /**
     * 重写查询现有数据的方法，针对内部ID关联表的特殊处理
     * 由于关联表使用内部ID，无法通过外部ID匹配，只能通过业务唯一键匹配
     */
    @Override
    public Map<String, R> queryExistingDataFromMysql(List<T> updatedData) {
        if (updatedData.isEmpty()) {
            return new HashMap<>();
        }

        log.debug("从MySQL查询内部ID关联表{}的现有数据，数据量：{}", getMysqlTableName(), updatedData.size());

        // 根据业务唯一键查询现有数据
        Map<String, R> resultByBusinessKey = queryByBusinessUniqueKey(updatedData);

        // 构建最终的映射关系：SQL Server主键 -> MySQL数据
        Map<String, R> finalResult = new HashMap<>();

        for (T sqlServerData : updatedData) {
            String sqlServerPrimaryKey = getSqlServerPrimaryKeyValue(sqlServerData);
            String businessKey = getSqlServerBusinessUniqueKey(sqlServerData);

            if (businessKey != null) {
                R existingData = resultByBusinessKey.get(businessKey);
                if (existingData != null) {
                    finalResult.put(sqlServerPrimaryKey, existingData);
                    log.debug("通过业务唯一键找到匹配数据：sqlServerKey={}, businessKey={}, mysqlId={}",
                            sqlServerPrimaryKey, businessKey, getMysqlPrimaryKeyValue(existingData));
                }
            } else {
                log.warn("无法生成业务唯一键，可能是外键转换失败：sqlServerKey={}", sqlServerPrimaryKey);
            }
        }

        log.debug("查询结果：业务键匹配{}条，最终匹配{}条",
                resultByBusinessKey.size(), finalResult.size());

        return finalResult;
    }

    /**
     * 根据业务唯一键查询现有数据
     *
     * @param updatedData 更新的数据列表
     * @return 现有数据Map，key为业务唯一键
     */
    private Map<String, R> queryByBusinessUniqueKey(List<T> updatedData) {
        Map<String, R> resultMap = new HashMap<>();

        // 分批查询，避免IN条件过长
        for (List<T> batch : Lists.partition(updatedData, 50)) {
            List<R> batchResult = queryBatchByBusinessKey(batch);

            for (R data : batchResult) {
                String businessKey = getBusinessUniqueKey(data);
                resultMap.put(businessKey, data);
            }
        }

        return resultMap;
    }

    /**
     * 批量按业务键查询数据
     * 子类需要实现具体的查询逻辑，构建合适的查询条件
     *
     * @param batch 批量数据
     * @return 查询结果列表
     */
    protected abstract List<R> queryBatchByBusinessKey(List<T> batch);

    /**
     * 内部ID关联表通常不需要名称字段的处理
     */
    @Override
    public String getMysqlNameColumnName() {
        throw new RuntimeException("内部ID关联表不需要名称字段处理");
    }

    @Override
    public String getMysqlNameValue(R data) {
        throw new RuntimeException("内部ID关联表不需要名称字段处理");
    }

    /**
     * 内部ID关联表不需要重复名称检查
     */
    @Override
    public Map<String, R> queryRepeatNameFromMysql(List<T> updatedData) {
        return new HashMap<>();
    }

    /**
     * 记录外键转换失败的情况
     *
     * @param sqlServerData SQL Server数据
     * @param reason 失败原因
     */
    protected void logForeignKeyConversionFailure(T sqlServerData, String reason) {
        log.warn("外键转换失败：sqlServerKey={}, reason={}, data={}",
                getSqlServerPrimaryKeyValue(sqlServerData), reason, sqlServerData);
    }

    /**
     * 批量转换为MySQL实体（按主键）
     * 内部ID关联表使用业务唯一键作为主键
     */
    @Override
    public Map<String, R> batchConvertToMysqlEntityWithPrimaryKey(List<T> updatedData) {
        return convertToEntityMap(updatedData, this::getSqlServerBusinessUniqueKey);
    }

    /**
     * 批量转换为MySQL实体（按名称）
     * 内部ID关联表通常不需要按名称转换
     */
    @Override
    public Map<String, R> batchConvertToMysqlEntityWithName(List<T> updatedData) {
        // 内部ID关联表不需要按名称转换
        return new HashMap<>();
    }

    /**
     * 通用的批量转换方法
     */
    protected Map<String, R> convertToEntityMap(List<T> updatedData, Function<T, String> keyMapper) {
        Map<String, R> entityMap = new HashMap<>();
        for (T data : updatedData) {
            String key = keyMapper.apply(data);
            if (key != null && !key.isEmpty()) {
                R entity = convertToMysqlEntity(data);
                if (entity != null) {
                    entityMap.put(key, entity);
                }
            }
        }
        return entityMap;
    }
}
