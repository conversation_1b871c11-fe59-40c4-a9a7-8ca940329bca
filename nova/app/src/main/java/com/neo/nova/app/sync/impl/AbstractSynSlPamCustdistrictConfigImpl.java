package com.neo.nova.app.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.nova.app.action.enums.SqlServerSourceEnums;
import com.neo.nova.app.sync.AbstractTagLeafInfoTableSyncConfig;
import com.neo.nova.domain.entity.Priceschemepurchaselist;
import com.neo.nova.domain.entity.sqlserver.SynSlPamCustbuylist;
import com.neo.nova.domain.entity.sqlserver.SynSlPamCustdistrict;
import com.neo.tagcenter.app.constants.EnableStatus;
import com.neo.tagcenter.app.constants.TagDomainEnums;
import com.neo.tagcenter.domain.entity.TagLeafInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

@Slf4j
public abstract class AbstractSynSlPamCustdistrictConfigImpl extends AbstractTagLeafInfoTableSyncConfig<SynSlPamCustdistrict> {

    @Override
    protected TagDomainEnums getTagDomainEnums() {
        return TagDomainEnums.ADMINISTRATIVE_REGION;
    }

    @Override
    public String getSqlServerTableName() {
        return "syn_sl_pam_custdistrict";
    }


    @Override
    public QueryWrapper<SynSlPamCustdistrict> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynSlPamCustdistrict> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("Depth").orderByAsc("ParentID");

        return queryWrapper;
    }


    @Override
    public String getSqlServerPrimaryKeyValue(SynSlPamCustdistrict data) {
        return String.valueOf(data.getId());
    }

    @Override
    public String getMysqlPrimaryKeyValue(TagLeafInfo data) {
        SqlServerSourceEnums sqlserverSource = getSqlserverSource();
        return switch (sqlserverSource) {
            case LOCAL -> "";
            case SQLSERVER1 -> data.getOutId1();
            case SQLSERVER2 -> data.getOutId2();
        };
    }

    public String getSqlServerNameValue(SynSlPamCustdistrict data) {
        return data.getCustdistrictname();
    }

    @Override
    public TagLeafInfo convertToMysqlEntity(SynSlPamCustdistrict data) {
        return fromCustdistrict(data);
    }

    /**
     * SynSlPamCustdistrict转TagLeafInfo
     *
     * @param custdistrict
     * @return
     */
    private TagLeafInfo fromCustdistrict(SynSlPamCustdistrict custdistrict) {
        if (custdistrict == null) {
            return null;
        }

        TagLeafInfo tagLeafInfo = new TagLeafInfo();

        Long parentId = custdistrict.getParentid() != null ? custdistrict.getParentid().longValue() : null;

        // 基本信息映射
        switch (getSqlserverSource()) {
            case SQLSERVER1:
                tagLeafInfo.setOutId1(String.valueOf(custdistrict.getId()));
                tagLeafInfo.setOutParentId1(String.valueOf(parentId));
                break;
            case SQLSERVER2:
                tagLeafInfo.setOutId2(String.valueOf(custdistrict.getId()));
                tagLeafInfo.setOutParentId2(String.valueOf(parentId));
                break;
        }
        tagLeafInfo.setOutType(getSqlserverSource().getValue());

        tagLeafInfo.setName(custdistrict.getCustdistrictname());
        tagLeafInfo.setCode(custdistrict.getCustdistrictno());
        tagLeafInfo.setDescription(custdistrict.getDescription());

        // 层级信息映射
        tagLeafInfo.setLevel(custdistrict.getDepth());

        tagLeafInfo.setCode(custdistrict.getOrdercode());

        // 状态映射
        tagLeafInfo.setIsEnabled("1".equals(custdistrict.getUstate()) ? EnableStatus.ENABLE.getValue() : EnableStatus.DISABLE.getValue());
        tagLeafInfo.setIsDeleted(0);

        return tagLeafInfo;
    }

}
