package com.neo.nova.app.sync.impl.sqlserver;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.app.action.enums.SqlServerSourceEnums;
import com.neo.nova.app.sync.impl.AbstractSynIvPamMaterialConfigImpl;
import com.neo.nova.domain.entity.GoodsInfo;
import com.neo.nova.domain.entity.sqlserver.SynIvPamMaterial;
import com.neo.nova.infrastructure.mapper.sqlserver.SynIvPamMaterialMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SynIvPamMaterialConfigImpl extends AbstractSynIvPamMaterialConfigImpl {

    @Resource
    private SynIvPamMaterialMapper synIvPamMaterialMapper;

    @Override
    public BaseMapper<SynIvPamMaterial> getSqlServerMapper() {
        return synIvPamMaterialMapper;
    }

    @Override
    public SqlServerSourceEnums getSqlserverSource() {
        return SqlServerSourceEnums.SQLSERVER1;
    }
}
