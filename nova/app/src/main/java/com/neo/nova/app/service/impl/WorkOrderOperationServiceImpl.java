package com.neo.nova.app.service.impl;/*
 *
 *
 *                                                    __----~~~~~~~~~~~------___
 *                                   .  .   ~~//====......          __--~ ~~
 *                   -.            \_|//     |||\\  ~~~~~~::::... /~
 *                ___-==_       _-~o~  \/    |||  \\            _/~~-
 *        __---~~~.==~||\=_    -_--~/_-~|-   |\\   \\        _/~
 *    _-~~     .=~    |  \\-_    '-~7  /-   /  ||    \      /
 *  .~       .~       |   \\ -_    /  /-   /   ||      \   /
 * /  ____  /         |     \\ ~-_/  /|- _/   .||       \ /
 * |~~    ~~|--~~~~--_ \     ~==-/   | \~--===~~        .\
 *          '         ~-|      /|    |-~\~~       __--~~
 *                      |-~~-_/ |    |   ~\_   _-~            /\
 *                           /  \     \__   \/~                \__
 *                       _--~ _/ | .-~~____--~-/                  ~~==.
 *                      ((->/~   '.|||' -_|    ~~-/ ,              . _||
 *                                 -_     ~\      ~~---l__i__i__i--~~_/
 *                                 _-~-__   ~)  \--______________--~~
 *                               //.-~~~-~_--~- |-------~~~~~~~~
 *                                      //.-~~~--\
 *                               神兽保佑
 *
 *
 * 用途说明:
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/10 15:16
 */

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.neo.api.PageResponse;
import com.neo.api.SingleResponse;
import com.neo.common.time.TimeUtils;
import com.neo.nova.app.action.actionType.VisitingRecordEnum;
import com.neo.nova.app.action.enums.*;
import com.neo.nova.app.action.enums.WorkOrderPriorityEnums;
import com.neo.nova.app.action.enums.WorkOrderStatusEnums;
import com.neo.nova.app.action.actionType.ActionSubTypeEnums;
import com.neo.nova.app.action.models.ActionTaskModel;
import com.neo.nova.app.action.models.EventContent;
import com.neo.nova.app.service.*;
import com.neo.nova.app.util.WorkOrderUtil;
import com.neo.nova.app.vo.*;
import com.neo.nova.domain.dto.WorkOrderDetailDto;
import com.neo.nova.domain.dto.WorkOrderDto;
import com.neo.nova.domain.entity.CustomerInfo;
import com.neo.nova.domain.entity.WorkOrder;
import com.neo.nova.domain.entity.WorkOrderDetail;
import com.neo.nova.domain.entity.WorkOrderEvent;
import com.neo.nova.domain.enums.MetricCodeEnum;
import com.neo.nova.domain.gateway.IWorkOrderDetailRepository;
import com.neo.nova.domain.gateway.IWorkOrderEventRepository;
import com.neo.nova.domain.gateway.IWorkOrderRepository;
import com.neo.nova.infrastructure.service.impl.WorkOrderRepositoryImpl;
import com.neo.session.SessionContextHolder;
import com.neo.user.client.notify.api.NotifyService;
import com.neo.user.client.notify.dto.TicketNotifyParam;
import com.neo.user.client.tenant.api.UserTenantService;
import com.neo.user.client.tenant.dto.TenantUserInfoDTO;
import com.neo.user.client.userinfo.api.UserService;
import com.neo.user.client.userinfo.dto.UserInfoDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.StringUtil;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WorkOrderOperationServiceImpl implements WorkOrderOperationService {

    @Resource
    private IWorkOrderDetailRepository iworkOrderDetailRepository;

    @Resource
    private IWorkOrderRepository iWorkOrderRepository;

    @Resource
    private IWorkOrderEventRepository iWorkOrderEventRepository;

    @Resource
    private WorkOrderRemarksService workOrderRemarksService;

    @Resource
    private UserService userService;

    @Resource
    private MetricService metricService;

    @Resource
    UserTenantService userTenantService;

    @Resource
    DepartmentRemoteService departmentRemoteService;

    @Resource
    NotifyService notifyService;

    @Resource
    CustomerMetricService customerMetricService;

    @Resource
    CustomerService customerService;


    /**
     * 根据id查询工单信息
     *
     * @param id
     * @return
     */
    @Override
    public WorkOrderDto getWorkOrderById(Long id) {
        if (id != null) {
            WorkOrder workOrder = iWorkOrderRepository.getById(id);
            return WorkOrderDto.covertDto(workOrder);
        }
        return null;
    }

    /**
     * 根据工单id查询工单详情
     *
     * @param workOrderId
     * @return
     */
    @Override
    public List<WorkOrderDetailDto> getWorkOrderDetailByWorkOrderId(Long workOrderId) {
        if (workOrderId != null) {
            LambdaQueryWrapper<WorkOrderDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WorkOrderDetail::getWorkOrderId, workOrderId);
            queryWrapper.orderByAsc(WorkOrderDetail::getSort);
            List<WorkOrderDetail> res = iworkOrderDetailRepository.list(queryWrapper);
            if (!CollectionUtils.isEmpty(res)) {
                return res.stream().map(WorkOrderDetailDto::covertDto).collect(Collectors.toList());
            }
        }
        return List.of();
    }

    /**
     * 根据客户id查询工单详情
     *
     * @return
     */
    @Override
    public List<WorkOrderDetailDto> getWorkOrderDetailByCustomerId(Long CustomerId) {
        if (CustomerId != null) {
            LambdaQueryWrapper<WorkOrderDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WorkOrderDetail::getBizId, CustomerId);
            queryWrapper.orderByAsc(WorkOrderDetail::getSort);
            List<WorkOrderDetail> res = iworkOrderDetailRepository.list(queryWrapper);
            if (!CollectionUtils.isEmpty(res)) {
                return res.stream().map(WorkOrderDetailDto::covertDto).collect(Collectors.toList());
            }
        }
        return List.of();
    }

    @Override
    public List<WorkOrderDetailDto> getWorkOrderDetailBySalesId(Long salesId) {
        if (salesId != null) {
            LambdaQueryWrapper<WorkOrderDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WorkOrderDetail::getBizId, salesId);
            queryWrapper.eq(WorkOrderDetail::getDeleted, 0);
            queryWrapper.orderByAsc(WorkOrderDetail::getCompleteTime);
            List<WorkOrderDetail> res = iworkOrderDetailRepository.list(queryWrapper);
            if (!CollectionUtils.isEmpty(res)) {
                return res.stream().map(WorkOrderDetailDto::covertDto).collect(Collectors.toList());
            }
        }
        return List.of();
    }

    /**
     * 查询工单列表
     *
     * @param request 查询请求参数
     * @return 工单列表响应
     */
    @Override
    public PageResponse<WorkOrderListItemDTO> queryWorkOrders(WorkOrderQueryRequest request) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<WorkOrder> queryWrapper = buildQueryWrapper(request);

            // 分页查询
            Page<WorkOrder> page = new Page<>(request.getPageIndex(), request.getPageSize());
            IPage<WorkOrder> workOrderPage = iWorkOrderRepository.page(page, queryWrapper);

            // 转换为DTO
            List<WorkOrderListItemDTO> workOrderDTOs = convertToListItemDTOs(request, workOrderPage.getRecords());

            return PageResponse.of(
                    workOrderDTOs,
                    workOrderPage.getTotal(),
                    request.getPageSize(),
                    request.getPageIndex()
            );

        } catch (Exception e) {
            log.error("查询工单列表失败", e);
            return PageResponse.buildFailure("QUERY_ERROR", "查询工单列表失败: " + e.getMessage());
        }
    }

    @Override
    public PageResponse<WorkOrderListItemDTO> queryExceptionWorkOrders(WorkOrderQueryRequest request) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<WorkOrder> queryWrapper = new LambdaQueryWrapper<>();

            // 状态筛选
            queryWrapper.eq(WorkOrder::getStatus, WorkOrderStatusEnums.EXPIRING.getCode())
                    .or()
                    .eq(WorkOrder::getNormal, 2);

            // 分页查询
            Page<WorkOrder> page = new Page<>(request.getPageIndex(), request.getPageSize());
            IPage<WorkOrder> workOrderPage = iWorkOrderRepository.page(page, queryWrapper);

            // 转换为DTO
            List<WorkOrderListItemDTO> workOrderDTOs = convertToListItemDTOs(request, workOrderPage.getRecords());

            return PageResponse.of(
                    workOrderDTOs,
                    workOrderPage.getTotal(),
                    request.getPageSize(),
                    request.getPageIndex()
            );

        } catch (Exception e) {
            log.error("查询工单列表失败", e);
            return PageResponse.buildFailure("QUERY_ERROR", "查询工单列表失败: " + e.getMessage());
        }
    }

    /**
     * 查询工单列表
     *
     * @return 工单列表响应
     */
    @Override
    public WorkOrderCountVo queryWorkOrderCount() {
        try {
            // 构建查询条件
            LambdaQueryWrapper<WorkOrder> queryWrapper = new LambdaQueryWrapper<>();

            // 排除已删除的记录
            queryWrapper.eq(WorkOrder::getDeleted, 0);

            // 状态筛选
            queryWrapper.in(WorkOrder::getStatus, Arrays.asList(WorkOrderStatusEnums.UNDERWAY.getCode()
                    ,  WorkOrderStatusEnums.EXPIRING.getCode(),WorkOrderStatusEnums.OVERTIME.getCode()));

            // 执行人筛选
            queryWrapper.eq(WorkOrder::getExecutorId, SessionContextHolder.getUserId());

            // 紧急工单数
            List<WorkOrder> workOrders = iWorkOrderRepository.list(queryWrapper);

            WorkOrderQueryRequest request = new WorkOrderQueryRequest();
            request.setStatuses(Arrays.asList(WorkOrderStatusEnums.UNDERWAY.getCode(), WorkOrderStatusEnums.EXPIRING.getCode()));
            request.setExecutorId(SessionContextHolder.getUserId());
            PageResponse<WorkOrderListItemDTO> response = queryWorkOrders(request);


            WorkOrderCountVo workOrderCountVo = new WorkOrderCountVo();
            int urgentCount = 0;
            int expiringCount = 0;
            int expiredCount = 0;
            int underwayCount = 0;
            for (WorkOrder workOrder : workOrders) {

                if (workOrder.getPriority().equals(WorkOrderPriorityEnums.URGENT.getCode())) {
                    urgentCount++;
                }
                // 24小时以内的 3小时是临期
                if(workOrder.getStatus().equals(WorkOrderStatusEnums.EXPIRING.getCode())){
                    expiringCount++;
                }
//                if ((workOrder.getWorkOrderEndTime().compareTo(workOrder.getWorkOrderStartTime().plusDays(1)) <= 0 && workOrder.getWorkOrderEndTime().compareTo(LocalDateTimeUtil.now().plusHours(3)) <= 0)
//                        || (workOrder.getWorkOrderEndTime().compareTo(workOrder.getWorkOrderStartTime().plusDays(1)) > 0 && workOrder.getWorkOrderEndTime().compareTo(LocalDateTimeUtil.now().plusHours(24)) <= 0)) {
//                    expiringCount++;
//                }
                if (workOrder.getStatus().equals(WorkOrderStatusEnums.OVERTIME.getCode())) {
                    expiredCount++;
                }else{
                    underwayCount++;
                }
            }
            workOrderCountVo.setUrgentCount(urgentCount);
            workOrderCountVo.setExpiringCount(expiringCount);
            workOrderCountVo.setExpiredCount(expiredCount);
            workOrderCountVo.setUnhandledCount(underwayCount);

            //取第一条
            if (response != null && CollectionUtils.isNotEmpty(response.getData())) {
                workOrderCountVo.setWorkOrderListItemDTO(response.getData().get(0));
            }

            return workOrderCountVo;

        } catch (Exception e) {
            log.error("查询工单列表失败", e);
            return new WorkOrderCountVo();
        }
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<WorkOrder> buildQueryWrapper(WorkOrderQueryRequest request) {
        LambdaQueryWrapper<WorkOrder> queryWrapper = new LambdaQueryWrapper<>();

        // 排除已删除的记录
        queryWrapper.eq(WorkOrder::getDeleted, 0);

        // 状态筛选
        if (request.getStatus() != null) {
            queryWrapper.eq(WorkOrder::getStatus, request.getStatus());
        }

        if (request.getStatuses() != null && CollectionUtils.isNotEmpty(request.getStatuses())) {
            queryWrapper.in(WorkOrder::getStatus, request.getStatuses());
        }

        // 加急工单筛选
        if (request.getUrgentOnly() != null && request.getUrgentOnly()) {
            queryWrapper.eq(WorkOrder::getPriority, WorkOrderPriorityEnums.URGENT.getCode());
        }

        // 执行人筛选
        if (request.getExecutorId() != null) {
            queryWrapper.eq(WorkOrder::getExecutorId, request.getExecutorId());
        }

        //创建人
        if (request.getCreatorId() != null) {
            queryWrapper.eq(WorkOrder::getCreatorId, request.getCreatorId());
        }

        // 类型筛选
        if (request.getType() != null) {
            queryWrapper.eq(WorkOrder::getType, request.getType());
        }

        // 类型筛选
        if (request.getSubType() != null) {
            queryWrapper.eq(WorkOrder::getSubType, request.getSubType());
        }

        // 直属下属筛选
        if (request.getOnlySub() != null) {
            //queryWrapper.eq(WorkOrder::getType, request.getType());
            List<Long> subUserIds = departmentRemoteService.getSubUserIdsByUserId(SessionContextHolder.getTenantId(), SessionContextHolder.getUserId());
            if (!CollectionUtils.isEmpty(subUserIds)) {
                queryWrapper.in(WorkOrder::getExecutorId, subUserIds);
            }
        }

        // 关键字搜索
        if (StringUtils.hasText(request.getKeyword())) {
            queryWrapper.like(WorkOrder::getTitle, request.getKeyword());
        }

        if (request.getStartTime() != null) {
            queryWrapper.ge(WorkOrder::getCreated, request.getStartTime());
        }

        if (request.getEndTime() != null) {
            queryWrapper.le(WorkOrder::getCreated, request.getEndTime());
        }

        // 按创建时间倒序排列
        queryWrapper.orderByAsc(WorkOrder::getStatus).orderByDesc(WorkOrder::getPriority).orderByAsc(WorkOrder::getWorkOrderEndTime);

        return queryWrapper;
    }

    /**
     * 转换为列表项DTO
     */
    private List<WorkOrderListItemDTO> convertToListItemDTOs(WorkOrderQueryRequest request, List<WorkOrder> workOrders) {
        if (CollectionUtils.isEmpty(workOrders)) {
            return new ArrayList<>();
        }

        // 收集所有用户ID
        Set<Long> userIds = new HashSet<>();
        Set<Long> excutorIds = new HashSet<>();
        for (WorkOrder workOrder : workOrders) {
            if (workOrder.getExecutorId() != null) {
                userIds.add(workOrder.getExecutorId());
            }
            if (workOrder.getCreatorId() != null) {
                userIds.add(workOrder.getCreatorId());
            }
            if (workOrder.getType() == ActionRelationEnums.CHECK_IN.getActionType()) {
                excutorIds.add(workOrder.getExecutorId());
            }
        }

        // 批量查询用户信息
        //Map<Long, String> userNameMap = getUserNameMap(userIds);

        Map<Long, TenantUserInfoDTO> map = departmentRemoteService.queryUserInfoMapByUserIds(workOrders.get(0).getTenantId(), new ArrayList<>(userIds));

//        Map<Long, CustomerInfo> customerInfoMap = new HashMap<>();
//        Map<Long, String> areaNameMap = new HashMap<>();

        //设置大区名字
//        if (request.getType() == ActionRelationEnums.CHECK_IN.getActionType() || request.getType() == ActionRelationEnums.VISITING_RECORD.getActionType()) {
//            customerInfoMap = customerMetricService.queryMapBySalesIds(workOrders.get(0).getTenantId(), new ArrayList<>(excutorIds));
//            //获取customerInfoMap中所有的areaId
//            Set<Long> areaIds = customerInfoMap.values().stream().map(CustomerInfo::getCustomerAreaId).collect(Collectors.toSet());
//            areaNameMap = metricService.queryNames(workOrders.get(0).getTenantId(), MetricCodeEnum.CUSTOMER_SALES_REGION.getCode(), new HashSet<>(areaIds));
//
//        }

        // 转换为DTO
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        List<WorkOrderListItemDTO> workOrderDTOs = new ArrayList<>();
        for (WorkOrder workOrder : workOrders) {
            WorkOrderListItemDTO dto = new WorkOrderListItemDTO();
            dto.setId(workOrder.getId());
            dto.setTitle(workOrder.getTitle());
            dto.setExecutorId(workOrder.getExecutorId());

            //填充数据
            if (map.get(workOrder.getExecutorId()) != null) {
                dto.setExecutorName(map.get(workOrder.getExecutorId()).getUnick());
                dto.setDeptName(map.get(workOrder.getExecutorId()).getDeptName());
            }

            if (map.get(workOrder.getCreatorId()) != null) {
                dto.setCreatorName(map.get(workOrder.getCreatorId()).getUnick());
            } else {
                dto.setCreatorName("系统自动");
            }
            dto.setCreatorId(workOrder.getCreatorId());

            dto.setStatus(workOrder.getStatus());
            dto.setStatusDesc(getStatusDesc(workOrder.getStatus(), workOrder.getType()));
            dto.setPriority(workOrder.getPriority());
            dto.setPriorityDesc(getPriorityDesc(workOrder.getPriority()));
            dto.setType(workOrder.getType());
            if(ActionRelationEnums.actionMap.get(workOrder.getType()) != null){
                dto.setTypeName(ActionRelationEnums.actionMap.get(workOrder.getType()).getActionDesc());
            }

            //1是正常，2以上是不同种类的异常
            if(workOrder.getNormal() > 1){
                dto.setException("异常");
            }

            //只有拜访需要
            if (VisitingRecordEnum.map.get(workOrder.getSubType()) != null) {
                dto.setSubTypeDesc(VisitingRecordEnum.map.get(workOrder.getSubType()).getActionDesc());
            }


//            if (request.getType() == ActionRelationEnums.CHECK_IN.getActionType()) {
//                if (customerInfoMap.get(workOrder.getExecutorId()) != null) {
//                    CustomerInfo customerInfo = customerInfoMap.get(workOrder.getExecutorId());
//                    dto.setAreaName(areaNameMap.get(customerInfo.getCustomerAreaId()));
//
//                    HashSet<Long> areaIds = new HashSet<>();
//                    areaIds.add(customerInfo.getCustomerAreaId());
//                    areaNameMap = metricService.queryNames(customerInfo.getTenantId(), MetricCodeEnum.CUSTOMER_SALES_REGION.getCode(), areaIds);
//
//                    if(areaNameMap.get(customerInfo.getCustomerAreaId()) != null){
//
//                    }
//                }
//            }

            dto.setCreated(workOrder.getCreated());
            if (workOrder.getCreated() != null) {
                dto.setCreatedStr(workOrder.getCreated().format(formatter));
            }
            workOrderDTOs.add(dto);
        }
        return workOrderDTOs;
    }

    /**
     * 批量获取用户名称
     */
    private Map<Long, String> getUserNameMap(Set<Long> userIds) {
        Map<Long, String> result = new HashMap<>();
        if (CollectionUtils.isEmpty(userIds)) {
            return result;
        }

        try {
            // 调用用户服务批量查询
            SingleResponse<Map<Long, UserInfoDTO>> response = userService.queryMapByUserIds(new ArrayList<>(userIds));
            if (response.isSuccess() && response.getData() != null) {
                Map<Long, UserInfoDTO> userMap = response.getData();
                for (Map.Entry<Long, UserInfoDTO> entry : userMap.entrySet()) {
                    UserInfoDTO userInfo = entry.getValue();
                    String displayName = userInfo.getUnick() != null ? userInfo.getUnick() : userInfo.getUname();
                    result.put(entry.getKey(), displayName);
                }
            }
        } catch (Exception e) {
            log.error("批量查询用户名称失败", e);
        }

        return result;
    }

    /**
     * 获取状态描述
     */
    private String getStatusDesc(Integer status, Integer type) {
        if (status == null) {
            return "未知";
        }
        WorkOrderStatusEnums statusEnum = WorkOrderStatusEnums.getByCode(status);

        return statusEnum != null ? statusEnum.getDesc() : "未知";
    }

    /**
     * 获取优先级描述
     */
    private String getPriorityDesc(Integer priority) {
        if (priority == null) {
            return "未知";
        }
        return WorkOrderPriorityEnums.getNameByCode(priority);
    }

    /**
     * 获取工单详情
     *
     * @param workOrderId 工单ID
     * @return 工单详情响应
     */
    @Override
    public SingleResponse<WorkOrderDetailVO> getWorkOrderDetail(Long workOrderId) {
        try {
            if (workOrderId == null) {
                return SingleResponse.buildFailure("INVALID_PARAM", "工单ID不能为空");
            }

            // 查询工单基本信息
            WorkOrder workOrder = iWorkOrderRepository.getById(workOrderId);
            if (workOrder == null) {
                return SingleResponse.buildFailure("WORK_ORDER_NOT_FOUND", "工单不存在");
            }

            // 构建响应对象
            WorkOrderDetailVO response = new WorkOrderDetailVO();

            // 1. 工单基本信息
            response = buildWorkOrderBasicInfo(workOrder);
//
//            // 2. 系统信息
//            response.setSystemInfo(buildWorkOrderSystemInfo(workOrder));
//
//            // 3. 工单进度
//            response.setProgressList(buildWorkOrderProgress(workOrder));
//
//            // 4. 工单备注
//            response.setRemarkList(buildWorkOrderRemarks(workOrderId));
//
//            // 5. 工单事件
//            response.setEventList(buildWorkOrderEvents(workOrderId));

            return SingleResponse.buildSuccess(response);

        } catch (Exception e) {
            log.error("查询工单详情失败, workOrderId: {}", workOrderId, e);
            return SingleResponse.buildFailure("QUERY_ERROR", "查询工单详情失败: " + e.getMessage());
        }
    }


    public SingleResponse<List<WorkOrderEventVO>> eventDetail(@PathVariable Long workOrderId) {

        LambdaQueryWrapper<WorkOrderEvent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkOrderEvent::getWorkOrderId, workOrderId);
        queryWrapper.orderByDesc(WorkOrderEvent::getCreated);
        List<WorkOrderEvent> workOrderEvents = iWorkOrderEventRepository.list(queryWrapper);

        List<Long> creatorIds = workOrderEvents.stream().map(WorkOrderEvent::getCreatorId).collect(Collectors.toList());

        //获取用户信息
        Map<Long, TenantUserInfoDTO> userInfoDTOMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(creatorIds)){
            userInfoDTOMap = departmentRemoteService.queryUserInfoMapByUserIds(workOrderEvents.get(0).getTenantId(), new ArrayList<>(creatorIds));
        }


        List<WorkOrderEventVO> workOrderEventVOS = new ArrayList<>();
        int i = 0;
        for (WorkOrderEvent workOrderEvent : workOrderEvents) {
            WorkOrderEventVO workOrderEventVO = new WorkOrderEventVO();
            if(userInfoDTOMap.get(workOrderEvent.getCreatorId()) != null){
                workOrderEventVO.setCreatorName(userInfoDTOMap.get(workOrderEvent.getCreatorId()).getUnick());
            }


            if(workOrderEvent.getType() > 3 && workOrderEvent.getType() != ActionSubTypeEnums.COMMENT.getType()){
                workOrderEventVO.setImportant(true);
                i++;
            }

            if(i < 3){
                workOrderEventVO.setImportant(true);
            }

            workOrderEventVO.setId(workOrderEvent.getId());
            workOrderEventVO.setTitle(ActionSubTypeEnums.mapActionName.get(workOrderEvent.getType()));
            // 时间转为 yyyy-mm-dd hh:mm 格式
            workOrderEventVO.setCreatedStr(workOrderEvent.getCreated().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
            try{
                workOrderEventVO.setExtraInfo(JSON.parseObject(workOrderEvent.getDescription(), EventContent.class));
            }catch (Exception e){

            }
            workOrderEventVOS.add(workOrderEventVO);
        }

        return SingleResponse.buildSuccess(workOrderEventVOS);

    }

    /**
     * 通过工单详情id查询工单详情
     *
     * @param workOrderDetailId
     */
    @Override
    public WorkOrderDetailDto getWorkOrderDetailById(Long workOrderDetailId) {
        WorkOrderDetail workOrderDetail = iworkOrderDetailRepository.getById(workOrderDetailId);
        return WorkOrderDetailDto.covertDto(workOrderDetail);
    }

    /**
     * 构建工单基本信息
     */
    private WorkOrderDetailVO buildWorkOrderBasicInfo(WorkOrder workOrder) {
        WorkOrderDetailVO basicInfo = new WorkOrderDetailVO();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        basicInfo.setId(workOrder.getId());
        basicInfo.setWorkOrderNo(workOrder.getId().toString()); // 使用ID作为工单编号
       // basicInfo.setDescription(workOrder.getDescription());
        basicInfo.setCreated(workOrder.getCreated());
        if (workOrder.getCreated() != null) {
            basicInfo.setCreatedStr(workOrder.getCreated().format(formatter));
        }

        basicInfo.setExecutorId(workOrder.getExecutorId());
        basicInfo.setCreatorId(workOrder.getCreatorId());
        basicInfo.setRead(workOrder.getRead() == 1);

        // 获取用户名称 todo 看看是否需要 @muzhao
//        Map<Long, String> userNameMap = getUserNameMap(Set.of(
//            workOrder.getExecutorId(), workOrder.getCreatorId()
//        ));
//        basicInfo.setExecutorName(userNameMap.get(workOrder.getExecutorId()));
//        basicInfo.setCreatorName(userNameMap.get(workOrder.getCreatorId()));

        // 设置时间信息
        basicInfo.setWorkOrderStartTime(workOrder.getWorkOrderStartTime());
        if (workOrder.getWorkOrderStartTime() != null) {
            basicInfo.setWorkOrderStartTimeStr(workOrder.getWorkOrderStartTime().format(formatter));
        }

        basicInfo.setWorkOrderEndTime(workOrder.getWorkOrderEndTime());
        if (workOrder.getWorkOrderEndTime() != null) {
            basicInfo.setWorkOrderEndTimeStr(workOrder.getWorkOrderEndTime().format(formatter));
        }

        // 根据状态设置完成时间
        if (WorkOrderStatusEnums.COMPLETED.getCode().equals(workOrder.getStatus())) {
            basicInfo.setCompletedTime(workOrder.getUpdated());
            if (workOrder.getUpdated() != null) {
                basicInfo.setCompletedTimeStr(workOrder.getUpdated().format(formatter));
            }
        }

        if (workOrder.getExecutorId() != null) {
            // 获取处理人详细信息
            SingleResponse<Map<Long, TenantUserInfoDTO>>  tenantUserInfoDTOSingleResponse = userTenantService.queryMapByUserIds(workOrder.getTenantId(), Arrays.asList(workOrder.getExecutorId(), workOrder.getCreatorId()));
            if (tenantUserInfoDTOSingleResponse != null && tenantUserInfoDTOSingleResponse.isSuccess()) {
                if(tenantUserInfoDTOSingleResponse.getData().get(workOrder.getExecutorId()) != null){
                    basicInfo.setExecutorPhone(tenantUserInfoDTOSingleResponse.getData().get(workOrder.getExecutorId()).getMobile());
                    basicInfo.setExcutorDeptName(tenantUserInfoDTOSingleResponse.getData().get(workOrder.getExecutorId()).getDeptName());
                    basicInfo.setExecutorName(tenantUserInfoDTOSingleResponse.getData().get(workOrder.getExecutorId()).getUnick());
                }

                if(tenantUserInfoDTOSingleResponse.getData().get(workOrder.getCreatorId()) != null){
                    basicInfo.setCreatorName(tenantUserInfoDTOSingleResponse.getData().get(workOrder.getCreatorId()).getUnick());
                    basicInfo.setCreatorDeptName(tenantUserInfoDTOSingleResponse.getData().get(workOrder.getCreatorId()).getDeptName());
                }

            }
        }

        //客户名称
        //大区名称
        if(StringUtil.isNotBlank(workOrder.getBizId())){
           // Map<Long, CustomerInfo> customerInfoMap = customerMetricService.queryMapBySalesIds(workOrder.getTenantId(), Arrays.asList(Long.parseLong(workOrder.getBizId())));
            CustomerInfo customerInfo = customerService.getById(Long.parseLong(workOrder.getBizId()));
            if(customerInfo != null){
                basicInfo.setCustomerName(customerInfo.getName());

                Set<Long> areaIds  = new HashSet<>();
                areaIds.add(customerInfo.getCustomerAreaId());
                Map<Long,String> areaNameMap = metricService.queryNames(customerInfo.getTenantId(), MetricCodeEnum.CUSTOMER_SALES_REGION.getCode(), new HashSet<>(areaIds));

                if(areaNameMap != null && areaNameMap.get(customerInfo.getCustomerAreaId()) != null){
                    basicInfo.setAreaName(areaNameMap.get(customerInfo.getCustomerAreaId()));
                }
            }

        }

        basicInfo.setType(workOrder.getType());
        basicInfo.setCustomerId(workOrder.getBizId());
        basicInfo.setSubType(workOrder.getSubType());
        basicInfo.setTypeDesc(ActionRelationEnums.actionMap.get(workOrder.getType()).getActionDesc());
        basicInfo.setStatus(workOrder.getStatus());
        basicInfo.setStatusDesc(getStatusDesc(workOrder.getStatus(), workOrder.getType()));
        basicInfo.setPriority(workOrder.getPriority());
        basicInfo.setTitle(workOrder.getTitle());
        basicInfo.setPriorityDesc(getPriorityDesc(workOrder.getPriority()));
        if(workOrder.getNormal() > 1){
            basicInfo.setException("异常");
        }


        // 设置工单内容（从WorkOrderExtra中获取）
        if(StringUtils.hasText(workOrder.getDescription())){
            basicInfo.setWorkContent(JSON.parseObject(workOrder.getDescription(), Map.class));
        }

        return basicInfo;
    }

    /**
     * 构建系统信息
     */
    private WorkOrderDetailVO buildWorkOrderSystemInfo(WorkOrder workOrder) {
        WorkOrderDetailVO systemInfo = new WorkOrderDetailVO();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        systemInfo.setType(workOrder.getType());
        systemInfo.setStatus(workOrder.getStatus());
        systemInfo.setStatusDesc(getStatusDesc(workOrder.getStatus(), workOrder.getType()));
        systemInfo.setPriority(workOrder.getPriority());
        systemInfo.setPriorityDesc(getPriorityDesc(workOrder.getPriority()));
        systemInfo.setCreated(workOrder.getCreated());
        if (TimeUtils.getCurrentTime() > workOrder.getWorkOrderEndTime().atZone(ZoneId.of("UTC")).toEpochSecond()) {
           // systemInfo.setIsDelay(true);
        }


        // 设置处理人信息
        systemInfo.setExecutorId(workOrder.getExecutorId());
        if (workOrder.getExecutorId() != null) {
            // 获取处理人详细信息
            SingleResponse<TenantUserInfoDTO> tenantUserInfoDTOSingleResponse = userTenantService.queryByUserId(workOrder.getTenantId(), workOrder.getExecutorId());
            if (tenantUserInfoDTOSingleResponse != null && tenantUserInfoDTOSingleResponse.isSuccess()) {
                systemInfo.setExecutorPhone(tenantUserInfoDTOSingleResponse.getData().getMobile());
                systemInfo.setExcutorDeptName(tenantUserInfoDTOSingleResponse.getData().getDeptName());
            }
        }

        return systemInfo;
    }



    /**
     * 构建工单备注
     */
    private List<WorkOrderRemarkInfo> buildWorkOrderRemarks(Long workOrderId) {
        List<WorkOrderRemarkInfo> remarkList = new ArrayList<>();

        try {
            // 查询工单备注
            WorkOrderRemarkVO queryVO = new WorkOrderRemarkVO();
            queryVO.setWorkOrderId(workOrderId);
            List<com.neo.nova.domain.dto.WorkOrderRemarkDTO> remarks = workOrderRemarksService.listWorkOrderRemark(queryVO);

            if (!CollectionUtils.isEmpty(remarks)) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                // 获取所有用户ID
                Set<Long> userIds = new HashSet<>();
                for (com.neo.nova.domain.dto.WorkOrderRemarkDTO remark : remarks) {
                    if (remark.getCreatorId() != null) {
                        userIds.add(remark.getCreatorId());
                    }
                    if (remark.getRecipient() != null) {
                        userIds.add(remark.getRecipient());
                    }
                }

                Map<Long, String> userNameMap = getUserNameMap(userIds);

                for (com.neo.nova.domain.dto.WorkOrderRemarkDTO remark : remarks) {
                    WorkOrderRemarkInfo remarkInfo = new WorkOrderRemarkInfo();
                    remarkInfo.setId(remark.getId());
                    remarkInfo.setCreatorId(remark.getCreatorId());
                    remarkInfo.setCreatorName(userNameMap.get(remark.getCreatorId()));
                    remarkInfo.setRecipient(remark.getRecipient());
                    remarkInfo.setRecipientName(userNameMap.get(remark.getRecipient()));
                    remarkInfo.setContent(remark.getContent());
                    remarkInfo.setCreated(remark.getCreated());
                    if (remark.getCreated() != null) {
                        remarkInfo.setCreatedStr(remark.getCreated().format(formatter));
                    }
                    remarkInfo.setStatus(remark.getStatus());

                    remarkList.add(remarkInfo);
                }
            }
        } catch (Exception e) {
            log.error("查询工单备注失败, workOrderId: {}", workOrderId, e);
        }

        return remarkList;
    }



    /**
     * todo 转到enum维护
     * 获取事件类型描述
     */
    private String getEventTypeDesc(Integer eventType) {
        if (eventType == null || EventEnums.eventMap.get(eventType) == null) {
            return "未知事件";
        }

        return EventEnums.eventMap.get(eventType).getEventDesc();
    }

    /**
     * 获取拜访详情
     *
     * @param workOrderId 工单详情ID
     * @return 拜访详情响应
     */
    @Override
    public SingleResponse<SimpleDetailResponse> getVisitDetail(Long workOrderId) {
        try {
            if (workOrderId == null) {
                return SingleResponse.buildFailure("INVALID_PARAM", "工单详情ID不能为空");
            }

            // 查询工单详情
            LambdaQueryWrapper<WorkOrderDetail> lambdaQueryWrapper = new LambdaQueryWrapper();
            lambdaQueryWrapper.eq(WorkOrderDetail::getWorkOrderId, workOrderId);
            WorkOrderDetail workOrderDetail = iworkOrderDetailRepository.getOne(lambdaQueryWrapper);
            if (workOrderDetail == null) {
                return SingleResponse.buildFailure("WORK_ORDER_DETAIL_NOT_FOUND", "工单详情不存在");
            }

            // 查询主工单信息
            WorkOrder workOrder = iWorkOrderRepository.getById(workOrderDetail.getWorkOrderId());
            if (workOrder == null) {
                return SingleResponse.buildFailure("WORK_ORDER_NOT_FOUND", "关联工单不存在");
            }

            // 构建响应对象
            SimpleDetailResponse response = buildVisitDetailResponse(workOrder, workOrderDetail);

            return SingleResponse.buildSuccess(response);

        } catch (Exception e) {
            log.error("查询拜访详情失败, workOrderDetailId: {}", workOrderId, e);
            return SingleResponse.buildFailure("QUERY_ERROR", "查询拜访详情失败: " + e.getMessage());
        }
    }

    /**
     * 构建拜访详情响应对象
     */
    private SimpleDetailResponse buildVisitDetailResponse(WorkOrder workOrder, WorkOrderDetail workOrderDetail) {
        SimpleDetailResponse response = new SimpleDetailResponse();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 解析拜访详情数据
        Map<String, Object> actionDetailMap = new HashMap<>();
        if (StringUtils.hasText(workOrderDetail.getActionDetail())) {
            try {
                actionDetailMap = JSON.parseObject(workOrderDetail.getActionDetail(), Map.class);
            } catch (Exception e) {
                log.warn("解析拜访详情数据失败: {}", workOrderDetail.getActionDetail(), e);
            }
        }

        // 1. 构建基本信息
        SimpleDetailResponse.BasicInfo visitInfo = new SimpleDetailResponse.BasicInfo();
        visitInfo.setWorkOrderDetailId(workOrderDetail.getId());
        visitInfo.setTitle(workOrder.getTitle());
        visitInfo.setExecutorId(workOrderDetail.getExecutorId());

        visitInfo.setSubTypeDesc(ActionSubTypeEnums.map.get(workOrderDetail.getType()));

        // 获取执行人姓名
        if (workOrderDetail.getExecutorId() != null) {
            try {
                // 这里可以调用用户服务获取用户信息
                TenantUserInfoDTO tenantUserInfoDTO = departmentRemoteService.queryUserInfoByUserId(workOrderDetail.getTenantId(), workOrderDetail.getExecutorId());
                if (tenantUserInfoDTO != null) {
                    visitInfo.setExecutorName(tenantUserInfoDTO.getUnick());
                    visitInfo.setDeptName(tenantUserInfoDTO.getDeptName());

                }
            } catch (Exception e) {
                log.warn("获取执行人信息失败: {}", workOrderDetail.getExecutorId(), e);
                visitInfo.setExecutorName("未知");
            }
        }

        visitInfo.setCreated(workOrderDetail.getCreated());
        if (workOrderDetail.getCreated() != null) {
            visitInfo.setCreatedStr(workOrderDetail.getCreated().format(formatter));
        }
        visitInfo.setCompletedTime(workOrderDetail.getCompleteTime());
        if (workOrderDetail.getCompleteTime() != null) {
            visitInfo.setCompletedTimeStr(workOrderDetail.getCompleteTime().format(formatter));
        }

        response.setVisitInfo(visitInfo);

        //工单信息
        response.setWorkOrderRemarkInfos(buildWorkOrderRemarks(workOrderDetail.getWorkOrderId()));

        //详细的拜访信息
        response.setVisitExtraInfo(actionDetailMap);

        return response;
    }

    /**
     * 获取导购日常打卡详情
     *
     * @param workOrderId 工单ID
     * @return 打卡详情响应
     */
    @Override
    public SingleResponse<CheckInDetailResponse> getCheckInDetail(Long workOrderId) {
        try {
            if (workOrderId == null) {
                return SingleResponse.buildFailure("INVALID_PARAM", "工单ID不能为空");
            }

            // 查询工单基本信息
            WorkOrder workOrder = iWorkOrderRepository.getById(workOrderId);
            if (workOrder == null) {
                return SingleResponse.buildFailure("WORK_ORDER_NOT_FOUND", "工单不存在");
            }

            // 查询工单详情列表
            List<WorkOrderDetailDto> workOrderDetails = getWorkOrderDetailByWorkOrderId(workOrderId);
            if (CollectionUtils.isEmpty(workOrderDetails)) {
                return SingleResponse.buildFailure("WORK_ORDER_DETAILS_NOT_FOUND", "工单详情不存在");
            }

            // 构建响应对象
            CheckInDetailResponse response = buildCheckInDetailResponse(workOrder, workOrderDetails);

            return SingleResponse.buildSuccess(response);

        } catch (Exception e) {
            log.error("查询导购日常打卡详情失败, workOrderId: {}", workOrderId, e);
            return SingleResponse.buildFailure("QUERY_ERROR", "查询导购日常打卡详情失败: " + e.getMessage());
        }
    }

    /**
     * 构建导购日常打卡详情响应对象
     */
    private CheckInDetailResponse buildCheckInDetailResponse(WorkOrder workOrder, List<WorkOrderDetailDto> workOrderDetails) {
        CheckInDetailResponse response = new CheckInDetailResponse();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 2. 构建打卡任务列表
        List<CheckInDetailResponse.CheckInTaskItem> taskList = new ArrayList<>();
        for (WorkOrderDetailDto detail : workOrderDetails) {
            CheckInDetailResponse.CheckInTaskItem taskItem = buildCheckInTaskItem(detail, formatter);
            taskList.add(taskItem);
        }
        response.setTaskList(taskList);

        return response;
    }

    /**
     * 构建打卡任务项
     */
    private CheckInDetailResponse.CheckInTaskItem buildCheckInTaskItem(WorkOrderDetailDto detail, DateTimeFormatter formatter) {
        CheckInDetailResponse.CheckInTaskItem taskItem = new CheckInDetailResponse.CheckInTaskItem();

        taskItem.setWorkOrderDetailId(detail.getId());
        taskItem.setSort(detail.getSort());
        taskItem.setType(detail.getType());
        taskItem.setStatus(detail.getStatus());
        taskItem.setStatusDesc(WorkOrderDetailStatusEnums.MAP.get(detail.getStatus()).getDesc());
        taskItem.setCompleteTime(detail.getCompleteTime());
        if (detail.getCompleteTime() != null) {
            taskItem.setCompleteTimeStr(detail.getCompleteTime().format(formatter));
        }

        // 设置任务类型描述
        taskItem.setTypeDesc(getTaskTypeDesc( detail.getType()));

        // 解析任务详情数据
        if (StringUtils.hasText(detail.getActionDetail())) {
            taskItem.setTaskDetail(JSONObject.parseObject(detail.getActionDetail(), Map.class));
        }

        return taskItem;
    }


    /**
     * 获取任务类型描述
     */
    private String getTaskTypeDesc(Integer type) {
        if (type == null) {
            return "未知任务";
        }

        Map<Integer, ActionSubTypeEnums> actionTypeMap = ActionSubTypeEnums.getByActionTypeAndType(type);
        ActionSubTypeEnums actionSubType = actionTypeMap.get(type);

        return actionSubType != null ? actionSubType.getActionDesc() : "未知任务";
    }

    /**
     * 获取工单类型列表
     *
     * @return 工单类型响应
     */
    @Override
    public SingleResponse<WorkOrderTypeResponse> getWorkOrderTypes() {
        try {
            WorkOrderTypeResponse response = new WorkOrderTypeResponse();
            List<ChooseOption> workOrderTypes = new ArrayList<>();

            // 遍历所有的 ActionRelationEnums 枚举值
            for (ActionRelationEnums actionEnum : ActionRelationEnums.values()) {
                //todo 特别注意 7 以下的枚举值
                if(actionEnum.getActionType() < 7){
                    ChooseOption item = new ChooseOption(actionEnum.getActionDesc(), String.valueOf(actionEnum.getActionType()));
                    workOrderTypes.add(item);
                }

            }

            response.setWorkOrderTypes(workOrderTypes);
            return SingleResponse.buildSuccess(response);

        } catch (Exception e) {
            log.error("获取工单类型列表失败", e);
            return SingleResponse.buildFailure("QUERY_ERROR", "获取工单类型列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据工单类型获取子类型列表
     *
     * @param actionName 工单类型名称
     * @return 工单子类型响应
     */
    @Override
    public SingleResponse<WorkOrderSubTypeResponse> getWorkOrderSubTypes(Integer actionName) {
        try {
            if (actionName == null) {
                return SingleResponse.buildFailure("INVALID_PARAM", "工单类型名称不能为空");
            }

            // 根据actionName获取对应的子类型
            Map<Integer, ActionSubTypeEnums> subTypeMap = ActionSubTypeEnums.getByActionTypeAndType(actionName);

            if (subTypeMap.isEmpty()) {
                return SingleResponse.buildSuccess(null);
            }

            WorkOrderSubTypeResponse response = new WorkOrderSubTypeResponse();

            // 设置主类型描述
            ActionRelationEnums actionRelation = getActionRelationByName(actionName);
            if (actionRelation != null) {
                response.setActionDesc(actionRelation.getActionDesc());
            }

            // 构建子类型列表
            List<ChooseOption> subTypes = new ArrayList<>();
            for (Map.Entry<Integer, ActionSubTypeEnums> entry : subTypeMap.entrySet()) {
                ActionSubTypeEnums subTypeEnum = entry.getValue();
                ChooseOption item = new ChooseOption(subTypeEnum.getActionDesc(), String.valueOf(subTypeEnum.getType()));
                subTypes.add(item);
            }

            // 按key排序

            response.setSubTypes(subTypes);
            return SingleResponse.buildSuccess(response);

        } catch (Exception e) {
            log.error("获取工单子类型列表失败, actionName: {}", actionName, e);
            return SingleResponse.buildFailure("QUERY_ERROR", "获取工单子类型列表失败: " + e.getMessage());
        }
    }


    @Override
    public SingleResponse<WorkOrderSubTypeResponse> getEventBySubType(Integer subType) {
        try {

            // 根据actionName获取对应的子类型
            List<ChooseOption> subTypes = ActionSubTypeEnums.getEventTypeByType(subType);

            if (CollectionUtils.isEmpty(subTypes)) {
                return SingleResponse.buildSuccess(null);
            }

            WorkOrderSubTypeResponse response = new WorkOrderSubTypeResponse();

            // 按key排序

            response.setSubTypes(subTypes);
            return SingleResponse.buildSuccess(response);

        } catch (Exception e) {
            log.error("获取工单子类型列表失败, actionName: {}", subType, e);
            return SingleResponse.buildFailure("QUERY_ERROR", "获取工单子类型列表失败: " + e.getMessage());
        }
    }

    @Override
    public SingleResponse<WorkOrderSubTypeResponse> getWorkOrderStatus() {
        try {

            WorkOrderSubTypeResponse response = new WorkOrderSubTypeResponse();

            // 构建子类型列表
            List<ChooseOption> subTypes = new ArrayList<>();
            for (WorkOrderStatusEnums entry : WorkOrderStatusEnums.values()) {
                ChooseOption item = new ChooseOption(String.valueOf(entry.getDesc()), String.valueOf(entry.getCode()));
                subTypes.add(item);
            }

            // 按key排序

            response.setSubTypes(subTypes);
            return SingleResponse.buildSuccess(response);

        } catch (Exception e) {
            return SingleResponse.buildFailure("QUERY_ERROR", "获取工单子类型列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据actionName获取ActionRelationEnums
     */
    private ActionRelationEnums getActionRelationByName(Integer type) {
        for (ActionRelationEnums actionEnum : ActionRelationEnums.values()) {
            if (actionEnum.getActionType().equals(type)) {
                return actionEnum;
            }
        }
        return null;
    }

    @Override
    @Transactional
    public SingleResponse<Boolean> updateWorkOrderAndDetail(ActionTaskModel actionTaskModel) {
        log.info("updateWorkOrderAndDetail: {}", actionTaskModel);
        try {

            WorkOrder workOrder = convertToWorkORder(actionTaskModel);
            iWorkOrderRepository.updateById(workOrder);

            //可能的更新
//            LambdaUpdateWrapper<WorkOrderDetail> updateWrapper = new LambdaUpdateWrapper();
//            updateWrapper.eq(WorkOrderDetail::getWorkOrderId, actionTaskModel.getWorkOrderId());
//            updateWrapper.set(WorkOrderDetail::getAction, ActionRelationEnums.actionMap.get(actionTaskModel.getActionType()).getActionName());
//            updateWrapper.set(WorkOrderDetail::getType, actionTaskModel.getWorkOrderDetailType());
//            updateWrapper.set(WorkOrderDetail::getBizId, actionTaskModel.getCustomerId());
//            updateWrapper.set(WorkOrderDetail::getBizId, actionTaskModel.getCustomerId());
//            updateWrapper.set(WorkOrderDetail::getExecutorId, actionTaskModel.getUserId());
//            updateWrapper.set(WorkOrderDetail::getActionDetail, JSON.toJSONString(actionTaskModel.getActionDetail()));
//            iworkOrderDetailRepository.update(updateWrapper);


            //创建事件
            WorkOrderEvent workOrderEvent = WorkOrderUtil.convertToWorkOrderEvent(workOrder);
            EventContent eventContent = new EventContent();
            eventContent.setContent(ActionSubTypeEnums.UPDATE.getActionDesc());
            workOrderEvent.setDescription(JSON.toJSONString(eventContent));
            workOrderEvent.setType(ActionSubTypeEnums.UPDATE.getType());
            workOrderEvent.setCreatorId(SessionContextHolder.getUserId());
            workOrderEvent.setTenantId(SessionContextHolder.getTenantId());
            iWorkOrderEventRepository.save(workOrderEvent);

            if(workOrder.getPriority() == WorkOrderPriorityEnums.URGENT.getCode()){
                TicketNotifyParam ticketNotifyParam = new TicketNotifyParam();
                ticketNotifyParam.setTenantId(workOrder.getTenantId());
                ticketNotifyParam.setTitle("工单加急");
                ticketNotifyParam.setReceiverIds(Arrays.asList(workOrder.getExecutorId()));
                ticketNotifyParam.setTicketSerial(String.valueOf(workOrder.getId()));
                ticketNotifyParam.setUrl("/pages/order/detail/index?id="+workOrder.getId());
                ticketNotifyParam.setContent("工单："+workOrder.getTitle()+"加急，请及时处理。");
                notifyService.sendTicketMessage(ticketNotifyParam);
            }

        } catch (Exception e) {
            log.error("更新工单和工单详情失败", e);
            return SingleResponse.buildFailure("UPDATE_ERROR", "更新工单和工单详情失败: " + e.getMessage());
        }
        return SingleResponse.buildSuccess(true);
    }

    public static WorkOrder convertToWorkORder(ActionTaskModel actionTaskModel) {
        WorkOrder workOrder = new WorkOrder();
        //todo 选择创建人
        workOrder.setCreatorId(SessionContextHolder.getUserId());
        workOrder.setExecutorId(actionTaskModel.getUserId());
        workOrder.setUpdaterId(1L);
        workOrder.setWorkOrderStartTime(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));
        workOrder.setWorkOrderEndTime(LocalDateTimeUtil.endOfDay(LocalDateTime.now()));
        workOrder.setPriority(actionTaskModel.getPriority());
        workOrder.setTenantId(actionTaskModel.getTenantId());
        workOrder.setType(actionTaskModel.getActionType());
        workOrder.setId(actionTaskModel.getWorkOrderId());
        workOrder.setBizId(String.valueOf(actionTaskModel.getCustomerId()));
        workOrder.setSubType(actionTaskModel.getWorkOrderDetailType());
        if(actionTaskModel.getActionDetail() != null){
            workOrder.setDescription(JSON.toJSONString(actionTaskModel.getActionDetail()));
        }
        initTitle(actionTaskModel,workOrder);
        return workOrder;
    }

    private static void initTitle(ActionTaskModel actionTaskModel, WorkOrder workOrder) {
        workOrder.setTitle(ActionSubTypeEnums.map.get(actionTaskModel.getWorkOrderDetailType()));
        if(org.apache.commons.lang.StringUtils.isNotBlank(actionTaskModel.getCustomerName())){
            workOrder.setTitle(workOrder.getTitle() + "-" + actionTaskModel.getCustomerName());
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        //汇报加上时间
        if(actionTaskModel.getActionType() == ActionRelationEnums.REPORT.getActionType()
                || actionTaskModel.getActionType() == ActionRelationEnums.CHECK_IN.getActionType()){
            workOrder.setTitle(workOrder.getTitle()+" "+ LocalDateTime.now().format(formatter));
        }
    }

    @Override
    public void expiringWorkOrder(Long workOrderId) {

        WorkOrder workOrderExist  = iWorkOrderRepository.getById(workOrderId);

        if(workOrderExist == null){
            return;
        }

        if(workOrderExist.getStatus().equals(WorkOrderStatusEnums.UNDERWAY.getCode())){
            WorkOrder workOrder = new WorkOrder();
            workOrder.setId(workOrderId);
            workOrder.setStatus(WorkOrderStatusEnums.EXPIRING.getCode());
            iWorkOrderRepository.updateById(workOrder);

            TicketNotifyParam ticketNotifyParam = new TicketNotifyParam();
            ticketNotifyParam.setTenantId(workOrderExist.getTenantId());
            ticketNotifyParam.setTitle("工单快过期了");
            ticketNotifyParam.setReceiverIds(Arrays.asList(workOrderExist.getExecutorId()));
            ticketNotifyParam.setTicketSerial(String.valueOf(workOrderExist.getId()));
            ticketNotifyParam.setUrl("/pages/order/detail/index?id="+workOrderExist.getId());
            ticketNotifyParam.setContent("工单："+workOrderExist.getTitle()+"快过期了，请及时处理。");

            notifyService.sendTicketMessage(ticketNotifyParam);
        }
    }

    @Override
    public void expiredWorkOrder(Long workOrderId) {

        WorkOrder workOrderExist  = iWorkOrderRepository.getById(workOrderId);

        if(workOrderExist == null){
            return;
        }

        if(workOrderExist.getStatus().equals(WorkOrderStatusEnums.UNDERWAY.getCode())
            || workOrderExist.getStatus().equals(WorkOrderStatusEnums.EXPIRING.getCode())){
            WorkOrder workOrder = new WorkOrder();
            workOrder.setId(workOrderId);
            workOrder.setStatus(WorkOrderStatusEnums.OVERTIME.getCode());
            iWorkOrderRepository.updateById(workOrder);

            TicketNotifyParam ticketNotifyParam = new TicketNotifyParam();
            ticketNotifyParam.setTenantId(workOrderExist.getTenantId());
            ticketNotifyParam.setTitle("工单过期了");
            ticketNotifyParam.setReceiverIds(Arrays.asList(workOrderExist.getExecutorId()));
            ticketNotifyParam.setTicketSerial(String.valueOf(workOrderExist.getId()));
            ticketNotifyParam.setUrl("/pages/order/detail/index?id="+workOrderExist.getId());
            ticketNotifyParam.setContent("工单："+workOrderExist.getTitle()+"快过期了，请及时处理。");

            notifyService.sendTicketMessage(ticketNotifyParam);
        }
    }


    @Override
    public boolean read(Long workOrderId) {
        WorkOrder workOrderExist  = iWorkOrderRepository.getById(workOrderId);

        if(workOrderExist == null){
            return false;
        }
        if(workOrderExist.getRead() == 1){
            return true;
        }
        if(workOrderExist.getExecutorId() == SessionContextHolder.getUserId()){
            WorkOrder workOrder = new WorkOrder();
            workOrder.setRead(1);
            workOrder.setId(workOrderId);
            return iWorkOrderRepository.updateById(workOrder);
        }else{
            return false;
        }
    }
}
