package com.neo.nova.app.sync;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.app.action.enums.SqlServerSourceEnums;
import com.neo.nova.app.sync.impl.TagSyncService;
import com.neo.nova.domain.entity.GoodsInfo;
import com.neo.nova.domain.entity.sqlserver.SynIvPamMatetype;
import com.neo.tagcenter.app.constants.EnableStatus;
import com.neo.tagcenter.app.constants.TagDomainEnums;
import com.neo.tagcenter.client.param.TreeTagSaveParam;
import com.neo.tagcenter.client.rpc.TreeTagWriteService;
import com.neo.tagcenter.domain.entity.TagLeafInfo;
import com.neo.tagcenter.infrastructure.mapper.TagLeafInfoMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
public abstract class AbstractTagLeafInfoTableSyncConfig<T> extends AbstractTableSyncConfig<T, TagLeafInfo> {

    @Resource
    private TagLeafInfoMapper tagLeafInfoMapper;

    @Resource
    private TreeTagWriteService treeTagWriteService;

    @Resource
    private TagSyncService tagSyncService;

    /**
     * 获取标签类型
     */
    protected abstract TagDomainEnums getTagDomainEnums();

    @Override
    public Map<String, TagLeafInfo> queryExistingDataFromMysql(List<T> updatedData) {
        List<String> primaryKeys = updatedData.stream()
                .map(this::getSqlServerPrimaryKeyValue)
                .toList();

        if (primaryKeys.isEmpty()) {
            return new HashMap<>();
        }

        log.debug("从MySQL查询表{}的现有数据，主键数量：{}", getMysqlTableName(), primaryKeys.size());

        QueryWrapper<TagLeafInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tagDomain", getTagDomainEnums().getCode());
        switch (getSqlserverSource()) {
            case SQLSERVER1:
                queryWrapper.in("outId1", primaryKeys);
                break;
            case SQLSERVER2:
                queryWrapper.in("outId2", primaryKeys);
                break;
        }

        List<TagLeafInfo> existingList = getMysqlMapper().selectList(queryWrapper);

        Map<String, TagLeafInfo> resultMap = new HashMap<>();
        for (TagLeafInfo data : existingList) {
            String primaryKey = switch (getSqlserverSource()) {
                case LOCAL -> "";
                case SQLSERVER1 -> data.getOutId1();
                case SQLSERVER2 -> data.getOutId2();
            };
            resultMap.put(primaryKey, data);
        }

        return resultMap;
    }

    @Override
    public Map<String, TagLeafInfo> queryRepeatNameFromMysql(List<T> updatedData) {
        Set<String> names = updatedData.stream()
                .map(this::getSqlServerNameValue)
                .collect(Collectors.toSet());

        if (names.isEmpty()) {
            return new HashMap<>();
        }

        log.debug("从MySQL查询表{}的现有数据，名称数量：{}", getMysqlTableName(), names.size());

        QueryWrapper<TagLeafInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("name", names);
        queryWrapper.eq("tagDomain", getTagDomainEnums().getCode());
        queryWrapper.ne("outType", getSqlserverSource().getValue());

        List<TagLeafInfo> existingList = getMysqlMapper().selectList(queryWrapper);

        Map<String, TagLeafInfo> resultMap = new HashMap<>();
        for (TagLeafInfo data : existingList) {
            resultMap.put(data.getName(), data);
        }

        return resultMap;
    }

    @Override
    public void batchInsertToMysql(List<TagLeafInfo> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        log.debug("批量插入MySQL表{}，数据量：{}", getMysqlTableName(), dataList.size());

        // 分批插入，避免单次插入数据量过大
        int batchSize = 100;
        for (int i = 0; i < dataList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, dataList.size());
            List<TagLeafInfo> batchData = dataList.subList(i, endIndex);

            for (TagLeafInfo data : batchData) {
                QueryWrapper<TagLeafInfo> queryWrapper1 = new QueryWrapper<>();
                switch (getSqlserverSource()) {
                    case SQLSERVER1:
                        queryWrapper1.eq("outId1", data.getOutParentId1());
                        break;
                    case SQLSERVER2:
                        queryWrapper1.eq("outId2", data.getOutParentId2());
                        break;
                }

                TagLeafInfo tagLeafInfo = getMysqlMapper().selectOne(queryWrapper1);
                if (tagLeafInfo != null) {
                    data.setParentId(tagLeafInfo.getId());
                }

                TreeTagSaveParam treeTagSaveParam = tagSyncService.getTreeTagSaveParam(data, getTenantId(), getTagDomainEnums());

                treeTagWriteService.addTagLeafInfo(treeTagSaveParam);
            }
        }
    }


    @Override
    public boolean needUpdate(TagLeafInfo existData, TagLeafInfo updateData) {
        if (existData == null || updateData == null) {
            return false;
        }

        int enableToUpdate = enableToUpdate(getSqlserverSource().getValue(), existData.getOutType(),
                updateData.getIsEnabled(), existData.getIsEnabled(), EnableStatus.ENABLE.getValue());
        if (enableToUpdate == 1) {
            return true;
        } else if (enableToUpdate == -1) {
            if (getSqlserverSource() == SqlServerSourceEnums.SQLSERVER2 &&
                    StrUtil.isBlank(existData.getOutId2())) {
                String outId2 = updateData.getOutId2();
                BeanUtils.copyProperties(new GoodsInfo(), updateData);

                updateData.setOutId2(outId2);
                return true;
            }
            return false;
        }

        if (!Objects.equals(existData.getCode(), updateData.getCode())) {
            return true;
        }

        if (!Objects.equals(existData.getName(), updateData.getName())) {
            return true;
        }

        if (!Objects.equals(existData.getIsEnabled(), updateData.getIsEnabled())) {
            return true;
        }

        // 比较描述
        String updateDescription = updateData.getDescription() == null ? "" : updateData.getDescription();
        if (!Objects.equals(existData.getDescription(), updateDescription)) {
            return true;
        }

        // 比较层级深度
        if (!Objects.equals(existData.getLevel(), updateData.getLevel())) {
            return true;
        }

        // 比较父级ID
        if (SqlServerSourceEnums.SQLSERVER1.equals(getSqlserverSource())) {
            String updateOutParentId1 = updateData.getOutParentId1() == null ? "" : updateData.getOutParentId1();
            if (!Objects.equals(existData.getOutParentId1(), updateOutParentId1)) {
                return true;
            }
        } else {
            String updateOutParentId2 = updateData.getOutParentId2() == null ? "" : updateData.getOutParentId2();
            if (!Objects.equals(existData.getOutParentId2(), updateOutParentId2)) {
                return true;
            }
        }

        return false;
    }


    @Override
    public String getMysqlTableName() {
        return "TagLeafInfo";
    }

    @Override
    public BaseMapper<TagLeafInfo> getMysqlMapper() {
        return tagLeafInfoMapper;
    }

    @Override
    public String getMysqlPrimaryKeyValue(TagLeafInfo data) {
        throw new RuntimeException("not support");
    }

    @Override
    public String getMysqlNameValue(TagLeafInfo data) {
        return data.getName();
    }

    @Override
    public String getMysqlNameColumnName() {
        return "name";
    }

    @Override
    public void setId(TagLeafInfo existData, TagLeafInfo updateData) {
        updateData.setId(existData.getId());
    }

    /**
     * 批量转换为MySQL实体（按主键）
     * 所有继承自AbstractTagLeafInfoTableSyncConfig的类都使用这个统一实现
     * 子类只需要实现convertToMysqlEntity方法，该方法会调用对应的fromXXX方法
     */
    @Override
    public Map<String, TagLeafInfo> batchConvertToMysqlEntityWithPrimaryKey(List<T> updatedData) {
        return batchConvertToTagLeafInfoMap(updatedData, this::getMysqlPrimaryKeyValue);
    }

    /**
     * 批量转换为MySQL实体（按名称）
     * 所有继承自AbstractTagLeafInfoTableSyncConfig的类都使用这个统一实现
     * 子类只需要实现convertToMysqlEntity方法，该方法会调用对应的fromXXX方法
     */
    @Override
    public Map<String, TagLeafInfo> batchConvertToMysqlEntityWithName(List<T> updatedData) {
        return batchConvertToTagLeafInfoMap(updatedData, this::getMysqlNameValue);
    }

    /**
     * 统一的批量转换方法
     * 所有标签同步类都使用这个统一的实现，避免重复代码
     *
     * 实现原理：
     * 1. 遍历待转换的数据列表
     * 2. 对每个数据调用convertToMysqlEntity方法（子类实现）
     * 3. convertToMysqlEntity方法内部会调用对应的fromXXX方法（如fromCustarea、fromMateType等）
     * 4. 使用keyMapper函数提取键值（主键或名称）
     * 5. 构建并返回Map结果
     *
     * @param updatedData 待转换的数据列表
     * @param keyMapper 键映射函数（主键或名称）
     * @return 转换后的Map，key为映射键，value为TagLeafInfo
     */
    protected Map<String, TagLeafInfo> batchConvertToTagLeafInfoMap(List<T> updatedData, Function<TagLeafInfo, String> keyMapper) {
        Map<String, TagLeafInfo> tagLeafInfoMap = new HashMap<>();

        for (T data : updatedData) {
            // 调用子类实现的convertToMysqlEntity方法
            // 子类的convertToMysqlEntity方法会调用对应的fromXXX方法：
            // - AbstractSynIvPamMateTypeConfigImpl -> fromMateType()
            // - AbstractSynSlPamCustareaConfigImpl -> fromCustarea()
            // - AbstractSynSlPamCustdistrictConfigImpl -> fromMateType()
            // - AbstractSynSlPamCustlineConfigImpl -> fromCustline()
            // - AbstractSynSlPamCusttypeConfigImpl -> fromCusttype()
            // - AbstractSynSlPamPricetypeConfigImpl -> fromPricetype()
            // - 等等...
            TagLeafInfo tagLeafInfo = convertToMysqlEntity(data);

            if (tagLeafInfo != null) {
                String key = keyMapper.apply(tagLeafInfo);
                if (key != null && !key.isEmpty()) {
                    tagLeafInfoMap.put(key, tagLeafInfo);
                }
            }
        }

        return tagLeafInfoMap;
    }
}
