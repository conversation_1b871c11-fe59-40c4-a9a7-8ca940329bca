package com.neo.nova.app.sync.impl.sqlserver;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.app.action.enums.SqlServerSourceEnums;
import com.neo.nova.app.sync.impl.AbstractSynSlSrvSalesbilllistConfigImpl;
import com.neo.nova.domain.entity.OrderDetail;
import com.neo.nova.domain.entity.sqlserver.SynSlSrvSalesbilllist;
import com.neo.nova.infrastructure.mapper.sqlserver.SynSlSrvSalesbilllistMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SynSlSrvSalesbilllistConfigImpl extends AbstractSynSlSrvSalesbilllistConfigImpl {

    @Resource
    private SynSlSrvSalesbilllistMapper synSlSrvSalesbilllistMapper;

    @Override
    public BaseMapper<SynSlSrvSalesbilllist> getSqlServerMapper() {
        return synSlSrvSalesbilllistMapper;
    }

    @Override
    public SqlServerSourceEnums getSqlserverSource() {
        return SqlServerSourceEnums.SQLSERVER1;
    }
}
