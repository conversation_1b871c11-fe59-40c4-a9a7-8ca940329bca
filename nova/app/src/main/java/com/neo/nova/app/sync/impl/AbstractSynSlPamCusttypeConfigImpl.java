package com.neo.nova.app.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.nova.app.sync.AbstractTagLeafInfoTableSyncConfig;
import com.neo.nova.domain.entity.sqlserver.SynSlPamCustline;
import com.neo.nova.domain.entity.sqlserver.SynSlPamCusttype;
import com.neo.tagcenter.app.constants.EnableStatus;
import com.neo.tagcenter.app.constants.TagDomainEnums;
import com.neo.tagcenter.domain.entity.TagLeafInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

@Slf4j
public abstract class AbstractSynSlPamCusttypeConfigImpl extends AbstractTagLeafInfoTableSyncConfig<SynSlPamCusttype> {

    @Override
    protected TagDomainEnums getTagDomainEnums() {
        return TagDomainEnums.CUSTOMER_TYPE;
    }

    @Override
    public String getSqlServerTableName() {
        return "syn_sl_pam_custtype";
    }


    @Override
    public QueryWrapper<SynSlPamCusttype> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynSlPamCusttype> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("Depth").orderByAsc("ParentID");
        return queryWrapper;
    }

    @Override
    public String getSqlServerPrimaryKeyValue(SynSlPamCusttype data) {
        return String.valueOf(data.getId());
    }

    @Override
    public String getSqlServerNameValue(SynSlPamCusttype data) {
        return data.getCusttypename();
    }

    @Override
    public TagLeafInfo convertToMysqlEntity(SynSlPamCusttype data) {
        return fromCusttype(data);
    }

    /**
     * SynSlPamCusttype转TagLeafInfo
     *
     * @param custtype
     * @return
     */
    public TagLeafInfo fromCusttype(SynSlPamCusttype custtype) {
        if (custtype == null) {
            return null;
        }

        TagLeafInfo tagLeafInfo = new TagLeafInfo();

        Long parentId = custtype.getParentid() != null ? custtype.getParentid().longValue() : null;

        // 基本信息映射
        switch (getSqlserverSource()) {
            case SQLSERVER1:
                tagLeafInfo.setOutId1(String.valueOf(custtype.getId()));
                tagLeafInfo.setOutParentId1(String.valueOf(parentId));
                break;
            case SQLSERVER2:
                tagLeafInfo.setOutId2(String.valueOf(custtype.getId()));
                tagLeafInfo.setOutParentId2(String.valueOf(parentId));
                break;
        }
        tagLeafInfo.setOutType(getSqlserverSource().getValue());

        tagLeafInfo.setName(custtype.getCusttypename());
        tagLeafInfo.setCode(custtype.getCusttypeno());
        tagLeafInfo.setDescription(custtype.getDescription());

        // 层级信息映射
        tagLeafInfo.setLevel(custtype.getDepth());
//        tagLeafInfo.setPos(custtype.getOrdercode());

        // 状态映射
        tagLeafInfo.setIsEnabled("1".equals(custtype.getUstate()) ? EnableStatus.ENABLE.getValue() : EnableStatus.DISABLE.getValue());
        tagLeafInfo.setIsDeleted(0);

        return tagLeafInfo;
    }

}
