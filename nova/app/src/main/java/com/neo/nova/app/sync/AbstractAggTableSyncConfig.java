package com.neo.nova.app.sync;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2025/8/3
 **/
@Slf4j
public abstract class AbstractAggTableSyncConfig<T, R> extends AbstractTableSyncConfig<T, R> {


    @Override
    public Map<String, R> queryExistingDataFromMysql(List<T> updatedData) {
        List<String> primaryKeys = updatedData.stream()
                .map(this::getSqlServerPrimaryKeyValue)
                .toList();

        if (primaryKeys.isEmpty()) {
            return new HashMap<>();
        }

        log.debug("从MySQL查询表{}的现有数据，主键数量：{}", getMysqlTableName(), primaryKeys.size());

        List<R> existingList = new ArrayList<>();
        for (List<String> objects : Lists.partition(primaryKeys, 100)) {
            QueryWrapper<R> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("outId", objects);
            queryWrapper.eq("outType", getSqlserverSource().getValue());
            existingList.addAll(getMysqlMapper().selectList(queryWrapper));
        }

        Map<String, R> resultMap = new HashMap<>();
        for (R data : existingList) {
            String primaryKey = getMysqlPrimaryKeyValue(data);
            resultMap.put(primaryKey, data);
        }

        return resultMap;
    }

    /**
     * 批量转换为MySQL实体（按主键）
     * 所有继承自AbstractAggTableSyncConfig的类都可以使用这个默认实现
     */
    @Override
    public Map<String, R> batchConvertToMysqlEntityWithPrimaryKey(List<T> updatedData) {
        return convertToEntityMap(updatedData, this::getMysqlPrimaryKeyValue);
    }

    /**
     * 批量转换为MySQL实体（按名称）
     * 只有实现了getMysqlNameValue且返回值不为空的子类才需要重写此方法
     * 大部分AggTableSyncConfig的子类都不需要名称转换，所以这里提供默认的空实现
     */
    @Override
    public Map<String, R> batchConvertToMysqlEntityWithName(List<T> updatedData) {
        // 大部分聚合表不需要按名称转换，子类如果需要可以重写
        return new HashMap<>();
    }

    /**
     * 通用的批量转换方法
     */
    protected Map<String, R> convertToEntityMap(List<T> updatedData, Function<R, String> keyMapper) {
        Map<String, R> entityMap = new HashMap<>();
        for (T data : updatedData) {
            R entity = convertToMysqlEntity(data);
            if (entity != null) {
                String key = keyMapper.apply(entity);
                if (key != null && !key.isEmpty()) {
                    entityMap.put(key, entity);
                }
            }
        }
        return entityMap;
    }
}
