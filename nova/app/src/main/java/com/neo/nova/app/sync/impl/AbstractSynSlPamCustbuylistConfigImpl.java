package com.neo.nova.app.sync.impl;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.api.SingleResponse;
import com.neo.nova.app.action.enums.SqlServerSourceEnums;
import com.neo.nova.app.sync.AbstractInternalIdRelationTableSyncConfig;
import com.neo.nova.domain.entity.GoodsInfo;
import com.neo.nova.domain.entity.Priceschemepurchaselist;
import com.neo.nova.domain.entity.sqlserver.SynSlPamCustbuylist;
import com.neo.nova.domain.gateway.GoodsInfoRepository;
import com.neo.nova.infrastructure.mapper.PriceschemepurchaselistMapper;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;
import com.neo.tagcenter.client.param.BaseTagQueryOption;
import com.neo.tagcenter.client.param.BusinessTreeTagQueryParam;
import com.neo.tagcenter.client.rpc.BusinessTreeTagReadReadService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


public abstract class AbstractSynSlPamCustbuylistConfigImpl extends AbstractInternalIdRelationTableSyncConfig<SynSlPamCustbuylist, Priceschemepurchaselist> {

    private static final Logger log = LoggerFactory.getLogger(AbstractSynSlPamCustbuylistConfigImpl.class);

    @Resource
    private PriceschemepurchaselistMapper priceschemepurchaselistMapper;

    @Resource
    private BusinessTreeTagReadReadService businessTreeTagReadReadService;

    @Resource
    private GoodsInfoRepository goodsInfoRepository;

    @Override
    public BaseMapper<Priceschemepurchaselist> getMysqlMapper() {
        return priceschemepurchaselistMapper;
    }

    @Override
    public String getSqlServerTableName() {
        return "syn_sl_pam_custbuylist";
    }

    @Override
    public String getMysqlTableName() {
        return "Priceschemepurchaselist";
    }


    @Override
    public QueryWrapper<SynSlPamCustbuylist> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynSlPamCustbuylist> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("id");

        return queryWrapper;
    }

    @Override
    public boolean needUpdate(Priceschemepurchaselist existData, Priceschemepurchaselist updateData) {
        if (existData == null || updateData == null) {
            return false;
        }

        // 比较客户类型ID
        if (!Objects.equals(existData.getPriceTypeID(), updateData.getPriceTypeID())) {
            return true;
        }

        // 比较物料ID
        if (!Objects.equals(existData.getMatelID(), updateData.getMatelID())) {
            return true;
        }

        // 比较销售价格
        if (!bigDecimalCompare(existData.getSellPrice(), updateData.getSellPrice())) {
            return true;
        }

        // 比较打印价格
        if (!Objects.equals(existData.getPrintPrice(), updateData.getPrintPrice())) {
            return true;
        }

        return false;
    }

    @Override
    public String getSqlServerPrimaryKeyValue(SynSlPamCustbuylist data) {
        return String.valueOf(data.getId());
    }

    @Override
    public String getSqlServerNameValue(SynSlPamCustbuylist data) {
        return "";
    }

    @Override
    public Priceschemepurchaselist convertToMysqlEntity(SynSlPamCustbuylist data) {
        return null;
    }

    @Override
    public Map<String, Priceschemepurchaselist> batchConvertToMysqlEntityWithPrimaryKey(List<SynSlPamCustbuylist> updatedData) {
        return convertToPriceschemepurchaselistMap(updatedData, this::getMysqlPrimaryKeyValue);
    }

    private Map<String, Priceschemepurchaselist> convertToPriceschemepurchaselistMap(List<SynSlPamCustbuylist> updatedData, Function<Priceschemepurchaselist, String> keyMapper) {
        List<String> priceTypeIds = updatedData.stream()
                .map(v -> String.valueOf(v.getPriceTypeID()))
                .toList();

        List<String> goodsIds = updatedData.stream()
                .map(v -> String.valueOf(v.getMateid()))
                .toList();

        Map<String, TagLeafInfoDto> priceTypeMap = new HashMap<>();

        BusinessTreeTagQueryParam param = new BusinessTreeTagQueryParam();
        param.setBusinessDomain(getTenantId().intValue());
        switch (getSqlserverSource()) {
            case SQLSERVER1:
                param.setOutId1List(priceTypeIds);
                break;
            case SQLSERVER2:
                param.setOutId2List(priceTypeIds);
                break;
        }

        BaseTagQueryOption option = new BaseTagQueryOption();
        option.setIncludeDeleted(true);
        option.setIncludeDisable(true);
        SingleResponse<Map<String, TagLeafInfoDto>> response = businessTreeTagReadReadService.batchQueryPricePlanSetting(param, option);
        if (response.isSuccess() && MapUtil.isNotEmpty(response.getData())) {
            priceTypeMap = response.getData();
        }

        Map<String, GoodsInfo> goodsInfoMap = goodsInfoRepository.list(new LambdaQueryWrapper<GoodsInfo>()
                        .in(getSqlserverSource() == SqlServerSourceEnums.SQLSERVER1, GoodsInfo::getOutId1, goodsIds)
                        .in(getSqlserverSource() == SqlServerSourceEnums.SQLSERVER2, GoodsInfo::getOutId2, goodsIds)
                        .eq(GoodsInfo::getTenantId, getTenantId()))
                .stream().collect(Collectors.toMap(
                        r0 -> switch (getSqlserverSource()) {
                            case LOCAL -> "";
                            case SQLSERVER1 -> r0.getOutId1();
                            case SQLSERVER2 -> r0.getOutId2();
                        },
                        Function.identity(),
                        (exist1, exist2) -> exist1
                ));

        Map<String, Priceschemepurchaselist> priceschemepurchaselistMap = new HashMap<>();

        for (SynSlPamCustbuylist data : updatedData) {
            TagLeafInfoDto priceType = priceTypeMap.get(String.valueOf(data.getPriceTypeID()));
            GoodsInfo goodsInfo = goodsInfoMap.get(String.valueOf(data.getMateid()));
            Priceschemepurchaselist priceschemepurchaselist = fromCustbuylist(data, priceType, goodsInfo);

            priceschemepurchaselistMap.put(keyMapper.apply(priceschemepurchaselist), priceschemepurchaselist);
        }

        return priceschemepurchaselistMap;
    }


    /**
     * SynSlPamCustbuylist转OrderDetail
     *
     * @param custbuylist
     * @param priceType
     * @param goodsInfo
     * @return
     */
    public Priceschemepurchaselist fromCustbuylist(SynSlPamCustbuylist custbuylist, TagLeafInfoDto priceType, GoodsInfo goodsInfo) {
        if (custbuylist == null) {
            return null;
        }

        Priceschemepurchaselist priceschemepurchaselist = new Priceschemepurchaselist();
        if (priceType != null) {
            priceschemepurchaselist.setPriceTypeID(priceType.getId());
        }

        if (goodsInfo != null) {
            priceschemepurchaselist.setMatelID(goodsInfo.getId());
        }

        priceschemepurchaselist.setSellPrice(custbuylist.getSellprice());
        priceschemepurchaselist.setPrintPrice(custbuylist.getPrintprice());

        return priceschemepurchaselist;
    }

    @Override
    public String getMysqlPrimaryKeyValue(Priceschemepurchaselist data) {
        return String.valueOf(data.getId());
    }

    @Override
    public void setId(Priceschemepurchaselist existData, Priceschemepurchaselist updateData) {
        updateData.setId(existData.getId());
    }

    // ========== 内部ID关联表特有方法实现 ==========

    @Override
    public String getBusinessUniqueKey(Priceschemepurchaselist data) {
        // 业务唯一键：客户类型ID + 物料ID
        return data.getPriceTypeID() + "_" + data.getMatelID();
    }

    @Override
    public String getSqlServerBusinessUniqueKey(SynSlPamCustbuylist data) {
        // 需要先转换外键关联
        Long priceTypeId = convertPriceTypeId(data.getPriceTypeID());
        Long matelId = convertMatelId(data.getMateid());

        if (priceTypeId == null || matelId == null) {
            log.error("外键转换失败：data:{}, priceTypeId:{}, matelId:{}", data, priceTypeId, matelId);
            return null;
        }

        return priceTypeId + "_" + matelId;
    }

    @Override
    protected List<Priceschemepurchaselist> queryBatchByBusinessKey(List<SynSlPamCustbuylist> batch) {
        if (batch.isEmpty()) {
            return new ArrayList<>();
        }

        // 第一步：批量转换外键关联
        BatchConversionResult conversionResult = batchConvertForeignKeys(batch);

        if (conversionResult.businessKeys.isEmpty()) {
            return new ArrayList<>();
        }

        // 第二步：使用 IN 查询批量获取数据
        LambdaQueryWrapper<Priceschemepurchaselist> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Priceschemepurchaselist::getPriceTypeID, conversionResult.priceTypeIds)
                .in(Priceschemepurchaselist::getMatelID, conversionResult.matelIds);

        List<Priceschemepurchaselist> allResults = getMysqlMapper().selectList(queryWrapper);

        // 第三步：过滤出真正匹配的记录
        return allResults.stream()
                .filter(result -> conversionResult.businessKeys.contains(result.getPriceTypeID() + "_" + result.getMatelID()))
                .collect(Collectors.toList());
    }

    /**
     * 批量转换外键关联的结果容器
     */
    private static class BatchConversionResult {
        final Set<Long> priceTypeIds;
        final Set<Long> matelIds;
        final Set<String> businessKeys;

        BatchConversionResult(Set<Long> priceTypeIds, Set<Long> matelIds, Set<String> businessKeys) {
            this.priceTypeIds = priceTypeIds;
            this.matelIds = matelIds;
            this.businessKeys = businessKeys;
        }
    }

    /**
     * 批量转换外键关联
     */
    private BatchConversionResult batchConvertForeignKeys(List<SynSlPamCustbuylist> batch) {
        // 提取所有需要转换的ID
        Set<Integer> sqlServerPriceTypeIds = batch.stream()
                .map(SynSlPamCustbuylist::getPriceTypeID)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<Integer> sqlServerMateIds = batch.stream()
                .map(SynSlPamCustbuylist::getMateid)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 批量转换价格类型ID
        Map<Integer, Long> priceTypeIdMap = batchConvertPriceTypeIds(sqlServerPriceTypeIds);

        // 批量转换物料ID
        Map<Integer, Long> matelIdMap = batchConvertMatelIds(sqlServerMateIds);

        // 构建最终结果
        Set<Long> priceTypeIds = new HashSet<>();
        Set<Long> matelIds = new HashSet<>();
        Set<String> businessKeys = new HashSet<>();

        for (SynSlPamCustbuylist sqlServerData : batch) {
            Long priceTypeId = priceTypeIdMap.get(sqlServerData.getPriceTypeID());
            Long matelId = matelIdMap.get(sqlServerData.getMateid());

            if (priceTypeId != null && matelId != null) {
                priceTypeIds.add(priceTypeId);
                matelIds.add(matelId);
                businessKeys.add(priceTypeId + "_" + matelId);
            }
        }

        return new BatchConversionResult(priceTypeIds, matelIds, businessKeys);
    }

    // ========== 外键转换方法 ==========

    /**
     * 批量转换价格类型ID
     * 从SQL Server的PriceTypeID批量转换为MySQL的priceTypeID
     */
    private Map<Integer, Long> batchConvertPriceTypeIds(Set<Integer> sqlServerPriceTypeIds) {
        Map<Integer, Long> resultMap = new HashMap<>();

        if (sqlServerPriceTypeIds.isEmpty()) {
            return resultMap;
        }

        BusinessTreeTagQueryParam param = new BusinessTreeTagQueryParam();
        param.setBusinessDomain(getTenantId().intValue());

        List<String> outIds = sqlServerPriceTypeIds.stream()
                .map(String::valueOf)
                .collect(Collectors.toList());

        switch (getSqlserverSource()) {
            case SQLSERVER1:
                param.setOutId1List(outIds);
                break;
            case SQLSERVER2:
                param.setOutId2List(outIds);
                break;
        }

        BaseTagQueryOption option = new BaseTagQueryOption();
        option.setIncludeDeleted(true);
        option.setIncludeDisable(true);

        SingleResponse<Map<String, TagLeafInfoDto>> queryResult =
                businessTreeTagReadReadService.batchQueryPricePlanSetting(param, option);

        if (queryResult.isSuccess() && MapUtil.isNotEmpty(queryResult.getData())) {
            for (Map.Entry<String, TagLeafInfoDto> entry : queryResult.getData().entrySet()) {
                Integer sqlServerId = Integer.valueOf(entry.getKey());
                Long mysqlId = entry.getValue().getId();
                resultMap.put(sqlServerId, mysqlId);
            }
        }

        return resultMap;
    }

    /**
     * 转换客户类型ID（保留单个查询方法用于兼容性）
     * 从SQL Server的PriceTypeID转换为MySQL的priceTypeID
     */
    private Long convertPriceTypeId(Integer sqlServerPriceTypeId) {
        if (sqlServerPriceTypeId == null) {
            return null;
        }

        Map<Integer, Long> result = batchConvertPriceTypeIds(Set.of(sqlServerPriceTypeId));
        return result.get(sqlServerPriceTypeId);
    }

    /**
     * 批量转换物料ID
     * 从SQL Server的MateID批量转换为MySQL的matelID
     */
    private Map<Integer, Long> batchConvertMatelIds(Set<Integer> sqlServerMateIds) {
        Map<Integer, Long> resultMap = new HashMap<>();

        if (sqlServerMateIds.isEmpty()) {
            return resultMap;
        }

        LambdaQueryWrapper<GoodsInfo> queryWrapper = new LambdaQueryWrapper<>();

        switch (getSqlserverSource()) {
            case SQLSERVER1:
                queryWrapper.in(GoodsInfo::getOutId1, sqlServerMateIds.stream().map(String::valueOf).collect(Collectors.toList()));
                break;
            case SQLSERVER2:
                queryWrapper.in(GoodsInfo::getOutId2, sqlServerMateIds.stream().map(String::valueOf).collect(Collectors.toList()));
                break;
        }

        List<GoodsInfo> goodsInfoList = goodsInfoRepository.list(queryWrapper);

        for (GoodsInfo goodsInfo : goodsInfoList) {
            Integer sqlServerId = null;
            switch (getSqlserverSource()) {
                case SQLSERVER1:
                    if (goodsInfo.getOutId1() != null) {
                        sqlServerId = Integer.valueOf(goodsInfo.getOutId1());
                    }
                    break;
                case SQLSERVER2:
                    if (goodsInfo.getOutId2() != null) {
                        sqlServerId = Integer.valueOf(goodsInfo.getOutId2());
                    }
                    break;
            }

            if (sqlServerId != null) {
                resultMap.put(sqlServerId, goodsInfo.getId());
            }
        }

        return resultMap;
    }

    /**
     * 转换物料ID（保留单个查询方法用于兼容性）
     * 从SQL Server的MateID转换为MySQL的matelID
     */
    private Long convertMatelId(Integer sqlServerMateId) {
        if (sqlServerMateId == null) {
            return null;
        }

        Map<Integer, Long> result = batchConvertMatelIds(Set.of(sqlServerMateId));
        return result.get(sqlServerMateId);
    }
}
