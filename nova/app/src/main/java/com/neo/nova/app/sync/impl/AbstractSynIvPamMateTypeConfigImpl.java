package com.neo.nova.app.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.nova.app.sync.AbstractTagLeafInfoTableSyncConfig;
import com.neo.nova.domain.entity.sqlserver.SynIvPamMatetype;
import com.neo.tagcenter.app.constants.EnableStatus;
import com.neo.tagcenter.app.constants.TagDomainEnums;
import com.neo.tagcenter.domain.entity.TagLeafInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public abstract class AbstractSynIvPamMateTypeConfigImpl extends AbstractTagLeafInfoTableSyncConfig<SynIvPamMatetype> {


    @Override
    protected TagDomainEnums getTagDomainEnums() {
        return TagDomainEnums.GOODS_CATEGORY;
    }

    @Override
    public String getSqlServerTableName() {
        return "syn_iv_pam_matetype";
    }

    @Override
    public QueryWrapper<SynIvPamMatetype> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynIvPamMatetype> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("Depth").orderByAsc("ParentID");

        return queryWrapper;
    }


    @Override
    public String getSqlServerPrimaryKeyValue(SynIvPamMatetype data) {
        return String.valueOf(data.getId());
    }

    @Override
    public String getSqlServerNameValue(SynIvPamMatetype data) {
        return data.getMatetypename();
    }

    @Override
    public TagLeafInfo convertToMysqlEntity(SynIvPamMatetype data) {
        return fromMateType(data);
    }

    /**
     * SynIvPamMatetype转TagLeafInfo
     *
     * @param matetype
     * @return
     */
    private TagLeafInfo fromMateType(SynIvPamMatetype matetype) {
        if (matetype == null) {
            return null;
        }

        TagLeafInfo tagLeafInfo = new TagLeafInfo();

        Long parentId = matetype.getParentid() != null ? matetype.getParentid().longValue() : null;

        // 基本信息映射
        switch (getSqlserverSource()) {
            case SQLSERVER1:
                tagLeafInfo.setOutId1(String.valueOf(matetype.getId()));
                tagLeafInfo.setOutParentId1(String.valueOf(parentId));
                break;
            case SQLSERVER2:
                tagLeafInfo.setOutId2(String.valueOf(matetype.getId()));
                tagLeafInfo.setOutParentId2(String.valueOf(parentId));
                break;
        }
        tagLeafInfo.setOutType(getSqlserverSource().getValue());

        tagLeafInfo.setName(matetype.getMatetypename());
        tagLeafInfo.setCode(matetype.getMatetypeno());
        tagLeafInfo.setDescription(matetype.getDescription());

        // 层级信息映射
        tagLeafInfo.setLevel(matetype.getDepth());

        tagLeafInfo.setCode(matetype.getOrdercode());
        // 状态映射
        tagLeafInfo.setIsEnabled("1".equals(matetype.getUstate()) ? EnableStatus.ENABLE.getValue() : EnableStatus.DISABLE.getValue());
        tagLeafInfo.setIsDeleted(0);

        return tagLeafInfo;
    }

}
