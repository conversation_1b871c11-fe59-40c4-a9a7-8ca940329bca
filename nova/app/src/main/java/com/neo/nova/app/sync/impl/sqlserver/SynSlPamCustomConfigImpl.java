package com.neo.nova.app.sync.impl.sqlserver;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.app.action.enums.SqlServerSourceEnums;
import com.neo.nova.app.sync.impl.AbstractSynSlPamCustomConfigImpl;
import com.neo.nova.domain.entity.CustomerInfo;
import com.neo.nova.domain.entity.sqlserver.SynSlPamCustom;
import com.neo.nova.infrastructure.mapper.sqlserver.SynSlPamCustomMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SynSlPamCustomConfigImpl extends AbstractSynSlPamCustomConfigImpl {

    @Resource
    private SynSlPamCustomMapper synSlPamCustomMapper;

    @Override
    public BaseMapper<SynSlPamCustom> getSqlServerMapper() {
        return synSlPamCustomMapper;
    }

    @Override
    public SqlServerSourceEnums getSqlserverSource() {
        return SqlServerSourceEnums.SQLSERVER1;
    }
}
