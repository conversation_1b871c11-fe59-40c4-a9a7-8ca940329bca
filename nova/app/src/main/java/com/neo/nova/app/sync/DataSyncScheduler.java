package com.neo.nova.app.sync;

import com.neo.xxljob.client.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 数据同步定时任务
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
@Slf4j
@Component("dataSyncScheduler")
public class DataSyncScheduler {

    @Autowired
    private DataSyncService dataSyncService;

    /**
     * 每分钟执行一次数据同步
     * cron表达式：0 * * * * ? 表示每分钟的第0秒执行
     */
    @XxlJob("dataSyncScheduler")
    public void scheduledSync() {
        log.info("定时数据同步任务开始执行");
        try {
            Long tenantId = 21L;
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusMonths(1);
            dataSyncService.syncAllTables(tenantId, startTime, endTime);
        } catch (Exception e) {
            log.error("定时数据同步任务执行失败", e);
        }
    }

    /**
     * 手动触发同步（用于测试）
     */
    public void manualSync(Long tenantId) {
        log.info("手动触发数据同步任务");
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusMonths(1);
        dataSyncService.syncAllTables(tenantId, startTime, endTime);
    }
}
