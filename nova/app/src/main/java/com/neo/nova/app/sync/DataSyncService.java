package com.neo.nova.app.sync;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.neo.nova.app.action.enums.SqlServerSourceEnums;
import com.neo.nova.app.service.MetricService;
import com.neo.nova.app.service.SalesDataUpdateService;
import com.neo.nova.app.sync.config.DataSyncConfig;
import com.neo.nova.app.sync.config.TableSyncOrderConfig;
import com.neo.nova.app.sync.executor.PageSyncResult;
import com.neo.nova.app.sync.executor.PageSyncTask;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * 数据同步服务
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
@Slf4j
@Service
public class DataSyncService {

    @Autowired
    private List<TableSyncConfig<?, ?>> tableSyncConfigs;

    @Resource
    private SalesDataUpdateService salesDataUpdateService;

    @Resource
    private MetricService metricService;

    @Resource
    private TableSyncOrderConfig tableSyncOrderConfig;

    @Resource
    private DataSyncConfig dataSyncConfig;

    private ExecutorService executorService;

    /**
     * 初始化线程池
     */
    private synchronized ExecutorService getExecutorService() {
        if (executorService == null || executorService.isShutdown()) {
            executorService = new ThreadPoolExecutor(
                    dataSyncConfig.getCorePoolSize(),
                    dataSyncConfig.getMaxPoolSize(),
                    dataSyncConfig.getKeepAliveSeconds(),
                    TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>(dataSyncConfig.getQueueCapacity()),
                    new ThreadFactory() {
                        private int counter = 0;

                        @Override
                        public Thread newThread(Runnable r) {
                            return new Thread(r, "DataSync-" + (++counter));
                        }
                    },
                    new ThreadPoolExecutor.CallerRunsPolicy()
            );
        }
        return executorService;
    }

    /**
     * 执行所有表的数据同步
     *
     * @param tenantId 租户ID，用于设置下游的businessDomain
     */
    public void syncAllTables(Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        log.info("开始执行数据同步任务，租户ID: {}", tenantId);
        if (tenantId == null) {
            log.error("租户ID不能为空");
            return;
        }

        // 获取按顺序排列的配置列表
        List<TableSyncConfig<?, ?>> orderedConfigs = tableSyncOrderConfig.getOrderedConfigs(tableSyncConfigs);
        log.info("共{}个表配置，按顺序执行同步", orderedConfigs.size());

        // 分离标签任务和其他任务
        List<TableSyncConfig<?, ?>> tagConfigs = new ArrayList<>();
        List<TableSyncConfig<?, ?>> otherConfigs = new ArrayList<>();

        for (TableSyncConfig<?, ?> config : orderedConfigs) {
            if (isTagConfig(config)) {
                tagConfigs.add(config);
            } else {
                otherConfigs.add(config);
            }
        }

        log.info("标签任务{}个，其他任务{}个", tagConfigs.size(), otherConfigs.size());

        boolean tagSyncSuccess = true;
        boolean allSyncSuccess = true;

        // 1. 先执行标签任务
        log.info("开始执行标签同步任务");
        for (TableSyncConfig<?, ?> config : tagConfigs) {
            try {
                if (tableSyncOrderConfig.isConfigEnabled(config)) {
                    syncSingleTable(config, startTime, endTime, tenantId);
                    log.info("标签任务{}同步完成", config.getClass().getSimpleName());
                } else {
                    log.debug("跳过已禁用的标签配置: {}", config.getClass().getSimpleName());
                }
            } catch (Exception e) {
                log.error("同步标签表{}失败: {}", config.getSqlServerTableName(), e.getMessage(), e);
                tagSyncSuccess = false;
                allSyncSuccess = false;
                if (!tableSyncOrderConfig.isContinueOnError()) {
                    log.error("遇到错误，停止后续同步");
                    return;
                }
            }
        }

        // 2. 标签任务完成后，调用metricService.init
        if (tagSyncSuccess && !tagConfigs.isEmpty()) {
            try {
                log.info("标签同步任务完成，开始初始化指标服务，租户ID: {}", tenantId);
                metricService.init(tenantId);
                log.info("指标服务初始化完成");
            } catch (Exception e) {
                log.error("指标服务初始化失败", e);
                allSyncSuccess = false;
                if (!tableSyncOrderConfig.isContinueOnError()) {
                    log.error("遇到错误，停止后续同步");
                    return;
                }
            }
        }

        // 3. 执行其他任务
        log.info("开始执行其他同步任务");
        for (TableSyncConfig<?, ?> config : otherConfigs) {
            try {
                if (tableSyncOrderConfig.isConfigEnabled(config)) {
                    syncSingleTable(config, startTime, endTime, tenantId);
                    log.info("任务{}同步完成", config.getClass().getSimpleName());
                } else {
                    log.debug("跳过已禁用的配置: {}", config.getClass().getSimpleName());
                }
            } catch (Exception e) {
                log.error("同步表{}失败: {}", config.getSqlServerTableName(), e.getMessage(), e);
                allSyncSuccess = false;
                if (!tableSyncOrderConfig.isContinueOnError()) {
                    log.error("遇到错误，停止后续同步");
                    break;
                }
            }
        }

        // 4. 全部任务完成后，调用updateData
        if (allSyncSuccess) {
            try {
                log.info("所有同步任务完成，开始更新销售数据统计");
                boolean updateResult = salesDataUpdateService.updateSalesStatistics();
                if (updateResult) {
                    log.info("销售数据统计更新完成");
                } else {
                    log.warn("销售数据统计更新失败");
                }
            } catch (Exception e) {
                log.error("销售数据统计更新异常", e);
            }
        } else {
            log.warn("由于同步过程中存在失败，跳过销售数据统计更新");
        }

        log.info("数据同步任务完成，总耗时:{}秒", Duration.between(endTime, LocalDateTime.now()).getSeconds());
    }

    /**
     * 判断是否为标签配置
     * 根据配置类名判断是否为标签同步任务
     */
    private boolean isTagConfig(TableSyncConfig<?, ?> config) {
        String className = config.getClass().getSimpleName();

        // 标签相关的配置类名模式
        return className.contains("MateType") ||      // 物料类型标签
                className.contains("Custtype") ||      // 客户类型标签
                className.contains("Custarea") ||      // 客户区域标签
                className.contains("Custline") ||      // 客户线路标签
                className.contains("Custdistrict") ||  // 客户区域标签
                className.contains("Pricetype") ||     // 价格类型标签
                className.contains("Saleser");         // 销售员标签
    }

    /**
     * 同步单个表
     *
     * @param config    同步配置
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param tenantId  租户ID，用于设置businessDomain
     */
    @SuppressWarnings("unchecked")
    private <T, R> void syncSingleTable(TableSyncConfig<T, R> config, LocalDateTime startTime, LocalDateTime endTime, Long tenantId) {
        String tableName = config.getSqlServerTableName();
        SqlServerSourceEnums sqlserverSource = config.getSqlserverSource();
        log.info("开始同步表: {}:{} -> {}，租户ID: {}", tableName, sqlserverSource.getDescription(), config.getMysqlTableName(), tenantId);

        // 检查是否启用分页同步
        syncSingleTableWithPagination(config, startTime, endTime, tenantId);
    }


    /**
     * 分页方式同步单个表
     *
     * @param config    同步配置
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param tenantId  租户ID，用于设置businessDomain
     */
    @SuppressWarnings("unchecked")
    private <T, R> void syncSingleTableWithPagination(TableSyncConfig<T, R> config, LocalDateTime startTime, LocalDateTime endTime, Long tenantId) {
        String tableName = config.getSqlServerTableName();

        try {
            // 1. 获取总记录数
            long totalCount = config.countUpdatedDataFromSqlServer(startTime, endTime);
            if (totalCount == 0) {
                log.info("表{}没有更新数据", tableName);
                return;
            }

            log.info("表{}共有{}条更新数据，开始分页同步", tableName, totalCount);

            // 2. 计算分页参数
            int pageSize = dataSyncConfig.getPageSize();
            int totalPages = (int) Math.ceil((double) totalCount / pageSize);

            log.info("表{}分页参数：总记录数={}，页大小={}，总页数={}", tableName, totalCount, pageSize, totalPages);

            // 3. 创建同步任务
            List<Future<PageSyncResult>> futures = new ArrayList<>();
            ExecutorService executor = getExecutorService();

            for (int i = 0; i < totalPages; i++) {
                int offset = i * pageSize;
                int pageNum = i + 1;

                PageSyncTask<T, R> task = new PageSyncTask<>(config, startTime, endTime, offset, pageSize, pageNum, tenantId);

                if (dataSyncConfig.isEnableMultiThread()) {
                    // 多线程执行
                    futures.add(executor.submit(task));
                } else {
                    // 单线程执行
                    try {
                        PageSyncResult result = task.call();
                        logPageResult(tableName, result);
                    } catch (Exception e) {
                        log.error("表{}第{}页同步失败: {}", tableName, pageNum, e.getMessage(), e);
                    }
                }
            }

            // 4. 等待多线程任务完成并收集结果
            if (dataSyncConfig.isEnableMultiThread()) {
                waitForTasksCompletion(tableName, futures);
            }

            log.info("表{}分页同步完成", tableName);

        } catch (Exception e) {
            log.error("表{}分页同步失败: {}", tableName, e.getMessage(), e);
            throw new RuntimeException("表" + tableName + "分页同步失败", e);
        }
    }

    /**
     * 等待任务完成并处理结果
     */
    private void waitForTasksCompletion(String tableName, List<Future<PageSyncResult>> futures) {
        int totalProcessed = 0;
        int totalInserted = 0;
        int totalUpdated = 0;
        int successCount = 0;
        int failureCount = 0;

        for (Future<PageSyncResult> future : futures) {
            try {
                PageSyncResult result = future.get(dataSyncConfig.getSyncTimeoutMinutes(), TimeUnit.MINUTES);

                if (result.isSuccess()) {
                    successCount++;
                    totalProcessed += result.getProcessedCount();
                    totalInserted += result.getInsertCount();
                    totalUpdated += result.getUpdateCount();
                } else {
                    failureCount++;
                    log.error("表{}第{}页同步失败: {}", tableName, result.getPageNum(), result.getErrorMessage());
                }

                logPageResult(tableName, result);

            } catch (TimeoutException e) {
                failureCount++;
                log.error("表{}页面同步超时", tableName, e);
            } catch (Exception e) {
                failureCount++;
                log.error("表{}页面同步异常", tableName, e);
            }
        }

        log.info("表{}多线程同步完成 - 成功页数: {}, 失败页数: {}, 总处理: {}条, 总插入: {}条, 总更新: {}条",
                tableName, successCount, failureCount, totalProcessed, totalInserted, totalUpdated);

        if (failureCount > 0) {
            log.warn("表{}有{}个页面同步失败，请检查日志", tableName, failureCount);
        }
    }

    /**
     * 记录页面同步结果
     */
    private void logPageResult(String tableName, PageSyncResult result) {
        if (result.isSuccess()) {
            log.debug("表{}第{}页同步成功 - 处理: {}条, 插入: {}条, 更新: {}条",
                    tableName, result.getPageNum(), result.getProcessedCount(),
                    result.getInsertCount(), result.getUpdateCount());
        } else {
            log.error("表{}第{}页同步失败: {}", tableName, result.getPageNum(), result.getErrorMessage());
        }
    }

    /**
     * 应用关闭时清理资源
     */
    @PreDestroy
    public void destroy() {
        if (executorService != null && !executorService.isShutdown()) {
            log.info("正在关闭数据同步线程池...");
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.warn("线程池未能在30秒内正常关闭，强制关闭");
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                log.warn("等待线程池关闭时被中断", e);
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
            log.info("数据同步线程池已关闭");
        }
    }
}
