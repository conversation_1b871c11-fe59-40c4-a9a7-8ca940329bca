package com.neo.nova.app.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.app.action.enums.SqlServerSourceEnums;
import com.neo.nova.app.sync.AbstractTagLeafInfoTableSyncConfig;
import com.neo.nova.domain.entity.sqlserver.SynIvPamMatetype;
import com.neo.nova.domain.entity.sqlserver.SynSlPamCustarea;
import com.neo.tagcenter.app.constants.EnableStatus;
import com.neo.tagcenter.app.constants.TagDomainEnums;
import com.neo.tagcenter.client.param.TreeTagSaveParam;
import com.neo.tagcenter.client.rpc.TreeTagWriteService;
import com.neo.tagcenter.domain.entity.TagLeafInfo;
import com.neo.tagcenter.infrastructure.mapper.TagLeafInfoMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public abstract class AbstractSynSlPamCustareaConfigImpl extends AbstractTagLeafInfoTableSyncConfig<SynSlPamCustarea> {

    @Override
    protected TagDomainEnums getTagDomainEnums() {
        return TagDomainEnums.SALES_REGION;
    }

    @Override
    public String getSqlServerTableName() {
        return "syn_sl_pam_custarea";
    }

    @Override
    public QueryWrapper<SynSlPamCustarea> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynSlPamCustarea> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("Depth").orderByAsc("ParentID");

        return queryWrapper;
    }


    @Override
    public String getSqlServerPrimaryKeyValue(SynSlPamCustarea data) {
        return String.valueOf(data.getId());
    }

    @Override
    public String getMysqlPrimaryKeyValue(TagLeafInfo data) {
        SqlServerSourceEnums sqlserverSource = getSqlserverSource();
        return switch (sqlserverSource) {
            case LOCAL -> "";
            case SQLSERVER1 -> data.getOutId1();
            case SQLSERVER2 -> data.getOutId2();
        };
    }

    public String getSqlServerNameValue(SynSlPamCustarea data) {
        return data.getCustareaname();
    }

    @Override
    public TagLeafInfo convertToMysqlEntity(SynSlPamCustarea data) {
        return fromCustarea(data);
    }


    /**
     * SynSlPamCustarea转TagLeafInfo
     *
     * @param custarea
     * @return
     */
    private TagLeafInfo fromCustarea(SynSlPamCustarea custarea) {
        if (custarea == null) {
            return null;
        }

        TagLeafInfo tagLeafInfo = new TagLeafInfo();

        Long parentId = custarea.getParentid() != null ? custarea.getParentid().longValue() : null;

        // 基本信息映射
        switch (getSqlserverSource()) {
            case SQLSERVER1:
                tagLeafInfo.setOutId1(String.valueOf(custarea.getId()));
                tagLeafInfo.setOutParentId1(String.valueOf(parentId));
                break;
            case SQLSERVER2:
                tagLeafInfo.setOutId2(String.valueOf(custarea.getId()));
                tagLeafInfo.setOutParentId2(String.valueOf(parentId));
                break;
        }
        tagLeafInfo.setOutType(getSqlserverSource().getValue());

        tagLeafInfo.setName(custarea.getCustareaname());
        tagLeafInfo.setCode(custarea.getCustareano());
        tagLeafInfo.setDescription(custarea.getDescription());

        // 层级信息映射
        tagLeafInfo.setLevel(custarea.getDepth());
//        tagLeafInfo.setPos(custarea.getOrdercode());

        // 状态映射
        tagLeafInfo.setIsEnabled("1".equals(custarea.getUstate()) ? EnableStatus.ENABLE.getValue() : EnableStatus.DISABLE.getValue());
        tagLeafInfo.setIsDeleted(0);

        return tagLeafInfo;
    }

}
