package com.neo.nova.app.action.actionType;/*
 *
 *          ┌─┐       ┌─┐
 *       ┌──┘ ┴───────┘ ┴──┐
 *       │                 │
 *       │       ───       │
 *       │  ─┬┘       └┬─  │
 *       │                 │
 *       │       ─┴─       │
 *       │                 │
 *       └───┐         ┌───┘
 *           │         │
 *           │         │
 *           │         │
 *           │         └──────────────┐
 *           │                        │
 *           │                        ├─┐
 *           │                        ┌─┘
 *           │                        │
 *           └─┐  ┐  ┌───────┬──┐  ┌──┘
 *             │ ─┤ ─┤       │ ─┤ ─┤
 *             └──┴──┘       └──┴──┘
 *                   Code is far away from bug with the animal protecting
 *                   神兽保佑,代码无bug
 *
 *
 * 用途说明:
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/24 21:04
 */

import com.neo.nova.app.action.enums.ActionRelationEnums;
import com.neo.nova.app.vo.ChooseOption;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
public enum ActionSubTypeEnums {
    //    CHECK_FIX(1, "巡检");
    CREATE(1, "创建工单", "事件"),
    UPDATE(2, "更改工单", "事件"),
    CANCEL(3, "关闭工单", "事件"),

    //    CLOTHES_PHOTO(1, "着装拍照"),
    //    GOODS_PHOTO(2, "理货拍照");
    COMMON(4, "普通", "工单"),
    GUIDE_COMMON(5, "导购日常打卡", "导购日常打卡"),
    CLOTHES_PHOTO(6, "着装拍照", "着装拍照"),
    GOODS_PHOTO(7, "理货拍照", "理货拍照"),
    //    DAY_REPORT(1, "日报"),
    //    WEEK_REPORT(2, "周报"),
    //    MONTH_REPORT(3, "月报"),
    //    YEAR_REPORT(4, "年报");
    DAY_REPORT(8, "日报", "日报"),
    WEEK_REPORT(9, "周报", "周报"),
    MONTH_REPORT(10, "月报", "月报"),
    YEAR_REPORT(11, "年报", "年报"),
    FIRST(12, "陌拜", "拜访"),
    SECOND(13, "回访", "拜访"),
    //只在event里面用
    SIGN_IN(14, "签到", "签到"),
    SIGN_OUT(15, "签退", "签退"),
    VISIT_RECORD(20, "拜访结论", "拜访结论"),
    COMMENT(16, "评论", "评论"),
    CHECK_FIX(17, "巡检", "巡检"),

    CHECK_IN_AM(18, "上班打卡", "打卡"),
    CHECK_OUT_AM(19, "下班打卡", "打卡"),


    NOT_ORDER_BEFORE_DEADLINE(21, "截止下单时间未下单","预警"),
    ORDER_EXCEED_DAILY_MAX(22, "下单超出日常量","预警"),
    TEMP_ADD_ORDER(23, "临时加单","预警"),
    OVERDUE_PAYMENT(24, "未及时回款","预警"),
    CUSTOMER_WITHOUT_SALESMAN(25, "客户无人管理","预警"),
    BIG_CUSTOMER_NO_ORDER_7D(26, "大客一周未下单","预警"),
    PRODUCT_TYPE_EXCEPTION(27, "大客单产品15天未下单","预警");
//    CHECK_IN_PM(20, "下午上班打卡", "checkIn"),
//    CHECK_OUT_PM(21, "下午下班打卡", "checkIn"),
    ;

    public static final Map<Integer, String> map = new HashMap<>();
    public static final Map<Integer, String> mapActionName = new HashMap<>();
    static {
        for (ActionSubTypeEnums value : values()) {
            map.put(value.getType(), value.getActionDesc());
            mapActionName.put(value.getType(), value.getActionName());
        }
    }
    

    private Integer type;
    private String actionDesc;
    private String actionName;

    ActionSubTypeEnums(Integer type, String actionDesc, String actionName) {
        this.type = type;
        this.actionDesc = actionDesc;
        this.actionName = actionName;
    }

    /**
     * 通过action分类 然后通过type分类
     *
     * @return
     */
    public static Map<Integer, ActionSubTypeEnums> getByActionTypeAndType(Integer type) {
        Map<Integer, ActionSubTypeEnums> result = new HashMap<>();
        //ActionTypeEnum
        switch (type) {
            case 0:
                result.put(COMMON.getType(), COMMON);
//                result.put(CHECK_OUT_AM.getType(), CHECK_OUT_AM);
//                result.put(CHECK_OUT_PM.getType(), CHECK_OUT_PM);
                break;
            case 3:
                result.put(CHECK_FIX.getType(), CHECK_FIX);
                break;
            case 1:
                result.put(GUIDE_COMMON.getType(), GUIDE_COMMON);
//                result.put(CHECK_IN_PM.getType(), CHECK_IN_PM);
//                result.put(CHECK_OUT_AM.getType(), CHECK_OUT_AM);
//                result.put(CHECK_OUT_PM.getType(), CHECK_OUT_PM);
                break;
            case 4:
                result.put(CLOTHES_PHOTO.getType(), CLOTHES_PHOTO);
                result.put(GOODS_PHOTO.getType(), GOODS_PHOTO);
                break;

            //拜访
            case 5:
                result.put(FIRST.getType(), FIRST);
                result.put(SECOND.getType(), SECOND);
                break;
            case 2:
                result.put(DAY_REPORT.getType(), DAY_REPORT);
                break;

                //预警
            case 7:
                result.put(CUSTOMER_WITHOUT_SALESMAN.getType(), CUSTOMER_WITHOUT_SALESMAN);
                result.put(BIG_CUSTOMER_NO_ORDER_7D.getType(), BIG_CUSTOMER_NO_ORDER_7D);
                result.put(PRODUCT_TYPE_EXCEPTION.getType(), PRODUCT_TYPE_EXCEPTION);
//                result.put(WEEK_REPORT.getType(), WEEK_REPORT);
//                result.put(MONTH_REPORT.getType(), MONTH_REPORT);
//                result.put(YEAR_REPORT.getType(), YEAR_REPORT);
        }
        return result;
    }

    public static List<ChooseOption> getEventTypeByType(Integer subType) {
        List<ChooseOption> result = new ArrayList<>();
        //ActionTypeEnum
        switch (subType) {
            case 0:
                ActionSubTypeEnums subTypeEnum = ActionSubTypeEnums.COMMON;
                ChooseOption item = new ChooseOption(subTypeEnum.getActionDesc(), String.valueOf(subTypeEnum.getType()));
                result.add(item);
                break;

                //导购日常打卡
            case 5:
//                result.put(CHECK_IN_AM.getType(), CHECK_IN_AM);
//                result.put(CHECK_OUT_AM.getType(), CHECK_OUT_AM);
                subTypeEnum = CLOTHES_PHOTO;
                item = new ChooseOption(subTypeEnum.getActionDesc(), String.valueOf(subTypeEnum.getType()));
                result.add(item);

                subTypeEnum = GOODS_PHOTO;
                item = new ChooseOption(subTypeEnum.getActionDesc(), String.valueOf(subTypeEnum.getType()));
                result.add(item);
                break;

            case 4:

//                subTypeEnum = CLOTHES_PHOTO;
//                item = new ChooseOption(subTypeEnum.getActionDesc(), String.valueOf(subTypeEnum.getType()));
//                result.add(item);
//
//                subTypeEnum = GOODS_PHOTO;
//                item = new ChooseOption(subTypeEnum.getActionDesc(), String.valueOf(subTypeEnum.getType()));
//                result.add(item);

                break;

            //拜访
            case 12:
            case 13:

                subTypeEnum = SIGN_IN;
                item = new ChooseOption(subTypeEnum.getActionDesc(), String.valueOf(subTypeEnum.getType()));
                result.add(item);

                subTypeEnum = SIGN_OUT;
                item = new ChooseOption(subTypeEnum.getActionDesc(), String.valueOf(subTypeEnum.getType()));
                result.add(item);

                subTypeEnum = VISIT_RECORD;
                item = new ChooseOption(subTypeEnum.getActionDesc(), String.valueOf(subTypeEnum.getType()));
                result.add(item);

                break;
                //日报
        }
        return result;
    }

}
