package com.neo.nova.app.sync.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.app.action.enums.SqlServerSourceEnums;
import com.neo.nova.app.sync.AbstractAggTableSyncConfig;
import com.neo.nova.domain.entity.CustomerInfo;
import com.neo.nova.domain.entity.Order;
import com.neo.nova.domain.entity.sqlserver.SynSlSrvSalesBill;
import com.neo.nova.domain.gateway.CustomerInfoRepository;
import com.neo.nova.infrastructure.mapper.OrderMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * SQL Server 2 数据源的销售单据同步配置实现
 */
@Slf4j
public abstract class AbstractSynSlSrvSalesBillConfigImpl extends AbstractAggTableSyncConfig<SynSlSrvSalesBill, Order> {

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private CustomerInfoRepository customerInfoRepository;

    @Override
    public BaseMapper<Order> getMysqlMapper() {
        return orderMapper;
    }

    @Override
    public String getSqlServerTableName() {
        return "syn_sl_srv_SalesBill";
    }

    @Override
    public String getMysqlTableName() {
        return "Order";
    }


    @Override
    public QueryWrapper<SynSlSrvSalesBill> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynSlSrvSalesBill> queryWrapper = new QueryWrapper<SynSlSrvSalesBill>();
        queryWrapper.orderByAsc("id");

        return queryWrapper;
    }


    @Override
    public boolean needUpdate(Order existData, Order updateData) {
        // 如果任一数据为空，不需要更新
        if (existData == null || updateData == null) {
            return false;
        }

        if (!Objects.equals(existData.getCode(), updateData.getCode())) {
            return true;
        }
        if (!Objects.equals(existData.getOrderType(), updateData.getOrderType())) {
            return true;
        }
        if (!Objects.equals(existData.getCustomerId(), updateData.getCustomerId())) {
            return true;
        }
        if (!Objects.equals(existData.getDelivyDate(), updateData.getDelivyDate())) {
            return true;
        }
        if (!Objects.equals(existData.getStatus(), updateData.getStatus())) {
            return true;
        }
        if (!Objects.equals(existData.getSynId(), updateData.getSynId())) {
            return true;
        }
        if (!Objects.equals(existData.getOrderTime(), updateData.getOrderTime())) {
            return true;
        }
        if (!bigDecimalCompare(existData.getAmount(), updateData.getAmount())) {
            return true;
        }
        if (!Objects.equals(existData.getReturnProcess(), updateData.getReturnProcess())) {
            return true;
        }
        if (!Objects.equals(existData.getReturnReason(), updateData.getReturnReason())) {
            return true;
        }
        if (!Objects.equals(existData.getReturnNote(), updateData.getReturnNote())) {
            return true;
        }

        return false;
    }

    @Override
    public String getSqlServerPrimaryKeyValue(SynSlSrvSalesBill data) {
        return String.valueOf(data.getId());
    }

    @Override
    public String getMysqlPrimaryKeyValue(Order data) {
        return data.getOutId();
    }

    @Override
    public String getMysqlNameColumnName() {
        throw new RuntimeException("do not need this method");
    }

    @Override
    public String getMysqlNameValue(Order data) {
        throw new RuntimeException("do not need this method");
    }

    @Override
    public String getSqlServerNameValue(SynSlSrvSalesBill data) {
        return "";
    }

    @Override
    public Order convertToMysqlEntity(SynSlSrvSalesBill data) {
        return null;
    }

    @Override
    public Map<String, Order> batchConvertToMysqlEntityWithPrimaryKey(List<SynSlSrvSalesBill> updatedData) {
        return convertToOrderMap(updatedData, this::getMysqlPrimaryKeyValue);
    }

    private Map<String, Order> convertToOrderMap(List<SynSlSrvSalesBill> updatedData, Function<Order, String> keyMapper) {
        List<String> customerIds = updatedData.stream()
                .map(v -> String.valueOf(v.getCustid()))
                .toList();


        Map<String, CustomerInfo> customerInfoMap = new HashMap<>();

        List<CustomerInfo> customerInfos = customerInfoRepository.list(
                new LambdaQueryWrapper<CustomerInfo>()
                        .in(getSqlserverSource() == SqlServerSourceEnums.SQLSERVER1, CustomerInfo::getOutId1, customerIds)
                        .in(getSqlserverSource() == SqlServerSourceEnums.SQLSERVER2, CustomerInfo::getOutId2, customerIds)
        );
        if (CollUtil.isNotEmpty(customerInfos)) {
            switch (getSqlserverSource()) {
                case SQLSERVER1 -> customerInfoMap = customerInfos.stream()
                        .collect(Collectors.toMap(CustomerInfo::getOutId1, Function.identity()));
                case SQLSERVER2 -> customerInfoMap = customerInfos.stream()
                        .collect(Collectors.toMap(CustomerInfo::getOutId2, Function.identity()));
            }
        }

        Map<String, Order> orderMap = new HashMap<>();
        for (SynSlSrvSalesBill data : updatedData) {
            Order order = fromOrder(data, customerInfoMap);
            orderMap.put(keyMapper.apply(order), order);
        }

        return orderMap;
    }


    private Order fromOrder(SynSlSrvSalesBill data, Map<String, CustomerInfo> customerInfoMap) {
        Order order = new Order();

        // 设置主键映射
        order.setOutId(String.valueOf(data.getId()));

        // 设置基本信息
        order.setTenantId(getTenantId()); // 默认租户ID
        order.setCode(data.getBillno()); // 订单编号
        order.setOrderType(Integer.valueOf(data.getBilltype()));

        // 设置数据源类型为sqlserver2（关键区别）
        order.setOutType(getSqlserverSource().getValue());

        // 客户信息 - 这里需要根据custid查询客户信息，暂时设置默认值
        if (data.getCustid() != null) {
            CustomerInfo customerInfo = customerInfoMap.get(String.valueOf(data.getCustid()));
            if (customerInfo != null) {
                order.setCustomerId(customerInfo.getId());
            } else {
                order.setCustomerId(-1L); // 默认客户ID
            }
        }

        // 业务员信息 - 暂时设置默认值
        order.setVerifyPerson(data.getVerifyperson());

        // 订单时间信息
        if (data.getBilldate() != null) {
            order.setOrderTime(data.getBilldate().getTime() / 1000); // 转换为时间戳（秒）
        }

        if (data.getDelivdate() != null) {
            order.setDelivyDate(data.getDelivdate().getTime() / 1000); // 转换为时间戳（秒）
        }

        // 订单金额
        order.setAmount(data.getBillmoney() == null ? BigDecimal.ZERO : data.getBillmoney().setScale(5, RoundingMode.HALF_UP));

        // 订单状态映射
        if (data.getBillstate() != null) {
            order.setStatus(mapBillStateToOrderStatus(data.getBillstate()));
        }

        // 订单来源映射
        if (data.getBillfrom() != null) {
            order.setOrderFrom(mapBillFromToOrderFrom(data.getBillfrom()));
        }

        // 设置同步相关字段
        if (data.getSynid() != null) {
            order.setSynId(data.getSynid().longValue());
        }

        if (data.getPrinttime() != null) {
            order.setPrintTime(data.getPrinttime().longValue());
        }

        // 设置审核信息
        if (data.getVerifydate() != null) {
            order.setVerifyDate(String.valueOf(data.getVerifydate().getTime() / 1000));
        }

        order.setReturnProcess(data.getReturnProcess());
        order.setReturnReason(data.getReturnReason());
        order.setReturnNote(data.getReturnNote());

        // 设置创建和更新时间
        long currentTime = System.currentTimeMillis() / 1000;
        order.setCreated(currentTime);
        order.setUpdated(currentTime);
        order.setCreatedBy(-1L); // 默认创建人
        order.setUpdatedBy(-1L); // 默认更新人

        return order;
    }

    /**
     * 比较Date和时间戳
     */
    private boolean compareDateWithTimestamp(Date date, Long timestamp) {
        if (date == null && timestamp == null) {
            return true;
        }
        if (date == null || timestamp == null) {
            return false;
        }
        return (date.getTime() / 1000) == timestamp;
    }

    /**
     * 比较BigDecimal和Long金额
     */
    private boolean compareBillMoneyWithAmount(BigDecimal billMoney, BigDecimal amount) {
        if (billMoney == null && amount == null) {
            return true;
        }
        if (billMoney == null || amount == null) {
            return false;
        }
        return billMoney.compareTo(amount) == 0;
    }

    /**
     * 映射单据状态到订单状态
     */
    private Integer mapBillStateToOrderStatus(String billState) {
        // 根据业务规则映射状态
        switch (billState) {
            case "0":
                return 0; // 未发货
            case "1":
                return 1; // 已发货
            case "2":
                return 2; // 结账中
            case "3":
                return 3; // 已结账
            case "9":
                return 9; // 已删除
            default:
                return 0; // 默认未发货
        }
    }

    /**
     * 映射单据来源到订单来源
     */
    private Integer mapBillFromToOrderFrom(String billFrom) {
        // 根据业务规则映射来源
        switch (billFrom) {
            case "0":
                return 0; // 电话
            case "1":
                return 1; // 手机
            case "2":
                return 2; // 网络
            default:
                return 0; // 默认电话
        }
    }


    @Override
    public void setId(Order existData, Order updateData) {
        updateData.setId(existData.getId());
    }
}
