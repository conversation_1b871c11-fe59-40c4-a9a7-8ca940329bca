package com.neo.nova.app.sync.impl.sqlserver;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.app.action.enums.SqlServerSourceEnums;
import com.neo.nova.app.sync.impl.AbstractSynSlSrvSalesBillConfigImpl;
import com.neo.nova.domain.entity.Order;
import com.neo.nova.domain.entity.sqlserver.SynSlSrvSalesBill;
import com.neo.nova.infrastructure.mapper.sqlserver.SynSlSrvSalesBillMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SynSlSrvSalesBillConfigImpl extends AbstractSynSlSrvSalesBillConfigImpl {

    @Resource
    private SynSlSrvSalesBillMapper synSlSrvSalesBillMapper;

    @Override
    public BaseMapper<SynSlSrvSalesBill> getSqlServerMapper() {
        return synSlSrvSalesBillMapper;
    }


    @Override
    public SqlServerSourceEnums getSqlserverSource() {
        return SqlServerSourceEnums.SQLSERVER1;
    }
}
