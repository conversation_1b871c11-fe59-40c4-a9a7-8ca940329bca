package com.neo.nova.app.sync.executor;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.neo.nova.app.sync.TableSyncConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

/**
 * 分页同步任务
 *
 * <AUTHOR>
 * @since 2025/7/22
 */
@Slf4j
//@Component
public class PageSyncTask<T, R> implements Callable<PageSyncResult> {


    private final TableSyncConfig<T, R> config;
    private final LocalDateTime startTime;
    private final LocalDateTime endTime;
    private final int offset;
    private final int limit;
    private final int pageNum;
    private final Long tenantId;


    public PageSyncTask(TableSyncConfig<T, R> config, LocalDateTime startTime, LocalDateTime endTime,
                        int offset, int limit, int pageNum, Long tenantId) {
        this.config = config;
        this.startTime = startTime;
        this.endTime = endTime;
        this.offset = offset;
        this.limit = limit;
        this.pageNum = pageNum;
        this.tenantId = tenantId;
    }

    @Override
    public PageSyncResult call() throws Exception {
        String tableName = config.getSqlServerTableName();
        log.info("开始同步表{}第{}页数据，偏移量：{}，限制：{}，租户ID：{}", tableName, pageNum, offset, limit, tenantId);

        // 设置租户ID到配置对象中
        config.setTenantId(tenantId);

        // 记录当前数据源上下文，用于最后清理
        String originalDataSource = DynamicDataSourceContextHolder.peek();
        log.debug("任务开始时数据源上下文: {}", originalDataSource);

        try {
            // 1. 分页查询SQL Server数据
            List<T> updatedData = config.queryUpdatedDataFromSqlServerWithPagination(startTime, endTime, offset, limit);

            if (CollectionUtils.isEmpty(updatedData)) {
                log.debug("表{}第{}页没有数据", tableName, pageNum);
                return new PageSyncResult(pageNum, 0, 0, 0, true, null);
            }

            log.debug("表{}第{}页查询到{}条数据", tableName, pageNum, updatedData.size());

            // 2. 从MySQL查询现有数据
            Map<String, R> existingDataMap = config.queryExistingDataFromMysql(updatedData);

            // 3. 从MySQL查询重复名称数据
            Map<String, R> repeatNameMap = config.queryRepeatNameFromMysql(updatedData);

            // 4. 分类数据：新增和更新
            List<R> insertList = new ArrayList<>();
            List<R> updateList = new ArrayList<>();

            Map<String, R> convertPrimaryKeyUpdateDataMap = config.batchConvertToMysqlEntityWithPrimaryKey(updatedData);

            Map<String, R> convertNameUpdateDataMap = config.batchConvertToMysqlEntityWithName(updatedData);

            for (T data : updatedData) {

                String primaryKey = config.getSqlServerPrimaryKeyValue(data);
                R existingData = existingDataMap.get(primaryKey);
                if (existingData == null) {
                    String name = config.getSqlServerNameValue(data);
                    //防止空字符串脏数据
                    if (StringUtils.isNotBlank(name)) {
                        existingData = repeatNameMap.get(name);
                    }
                }

                /*R updateData = config.convertToMysqlEntity(data);*/
                R updateData = convertPrimaryKeyUpdateDataMap.get(primaryKey);
                if (updateData == null) {
                    String name = config.getSqlServerNameValue(data);
                    updateData = convertNameUpdateDataMap.get(name);
                }

                if (existingData == null) {
                    // 新数据，需要插入
                    insertList.add(updateData);
                } else if (config.needUpdate(existingData, updateData)) {
                    // 现有数据，需要更新
                    config.setId(existingData, updateData);
                    updateList.add(updateData);
                }
            }

            // 5. 执行批量操作
            int insertCount = 0;
            int updateCount = 0;

            if (!insertList.isEmpty()) {
                log.debug("表{}第{}页批量插入{}条数据", tableName, pageNum, insertList.size());
                config.batchInsertToMysql(insertList);
                insertCount = insertList.size();
            }

            if (!updateList.isEmpty()) {
                log.debug("表{}第{}页批量更新{}条数据", tableName, pageNum, updateList.size());
                config.batchUpdateToMysql(updateList);
                updateCount = updateList.size();


            }

            log.info("表{}第{}页同步完成，处理{}条，插入{}条，更新{}条",
                    tableName, pageNum, updatedData.size(), insertCount, updateCount);

            return new PageSyncResult(pageNum, updatedData.size(), insertCount, updateCount, true, null);

        } catch (Exception e) {
            log.error("表{}第{}页同步失败: {}", tableName, pageNum, e.getMessage(), e);
            return new PageSyncResult(pageNum, 0, 0, 0, false, e.getMessage());
        }
    }
}
