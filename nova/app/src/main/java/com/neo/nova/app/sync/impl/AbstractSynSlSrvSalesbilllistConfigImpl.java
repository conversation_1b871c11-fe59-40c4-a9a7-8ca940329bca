package com.neo.nova.app.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.app.action.enums.SqlServerSourceEnums;
import com.neo.nova.app.sync.AbstractAggTableSyncConfig;
import com.neo.nova.domain.entity.GoodsInfo;
import com.neo.nova.domain.entity.Order;
import com.neo.nova.domain.entity.OrderDetail;
import com.neo.nova.domain.entity.sqlserver.SynSlSrvSalesbilllist;
import com.neo.nova.domain.gateway.GoodsInfoRepository;
import com.neo.nova.domain.gateway.OrderRepository;
import com.neo.nova.infrastructure.mapper.OrderDetailMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractSynSlSrvSalesbilllistConfigImpl extends AbstractAggTableSyncConfig<SynSlSrvSalesbilllist, OrderDetail> {

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private OrderRepository ordersRepository;

    @Resource
    private GoodsInfoRepository goodsInfoRepository;

    @Override
    public BaseMapper<OrderDetail> getMysqlMapper() {
        return orderDetailMapper;
    }

    @Override
    public String getSqlServerTableName() {
        return "syn_sl_srv_SalesBillList";
    }

    @Override
    public String getMysqlTableName() {
        return "OrderDetail";
    }

    @Override
    public QueryWrapper<SynSlSrvSalesbilllist> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynSlSrvSalesbilllist> queryWrapper = new QueryWrapper<SynSlSrvSalesbilllist>();
        queryWrapper.orderByAsc("id");
        return queryWrapper;
    }

    @Override
    public boolean needUpdate(OrderDetail existData, OrderDetail updateData) {
        if (existData == null || updateData == null) {
            return false;
        }

        if (!Objects.equals(existData.getOrderId(), updateData.getOrderId())) {
            return true;
        }

        if (!Objects.equals(existData.getCustomerId(), updateData.getCustomerId())) {
            return true;
        }

        // 比较物料ID
        if (!Objects.equals(existData.getGoodsId(), updateData.getGoodsId())) {
            return true;
        }

        // 比较销售价格
        if (!bigDecimalCompare(existData.getSalePrice(), updateData.getSalePrice())) {
            return true;
        }

        // 比较数量
        if (!bigDecimalCompare(existData.getSalesQty(), updateData.getSalesQty())) {
            return true;
        }

        // 比较发货数量
        if (!bigDecimalCompare(existData.getDelivyQty(), updateData.getDelivyQty())) {
            return true;
        }

        // 比较单据金额
        if (!bigDecimalCompare(existData.getBillMoney(), updateData.getBillMoney())) {
            return true;
        }

        // 比较发货金额
        if (!bigDecimalCompare(existData.getDelivMoney(), updateData.getDelivMoney())) {
            return true;
        }

        if (!bigDecimalCompare(existData.getRspPrice(), updateData.getRspPrice())) {
            return true;
        }
        if (!bigDecimalCompare(existData.getWsPrice(), updateData.getWsPrice())) {
            return true;
        }
        if (!bigDecimalCompare(existData.getGbPrice(), updateData.getGbPrice())) {
            return true;
        }
        if (!bigDecimalCompare(existData.getSbPrice(), updateData.getSbPrice())) {
            return true;
        }

        return false;
    }

    @Override
    public String getMysqlPrimaryKeyValue(OrderDetail data) {
        return data.getOutId();
    }

    @Override
    public String getSqlServerPrimaryKeyValue(SynSlSrvSalesbilllist data) {
        return String.valueOf(data.getId());
    }

    @Override
    public String getMysqlNameColumnName() {
        throw new RuntimeException("do not need this method");
    }

    @Override
    public String getMysqlNameValue(OrderDetail data) {
        throw new RuntimeException("do not need this method");
    }

    @Override
    public String getSqlServerNameValue(SynSlSrvSalesbilllist data) {
        return "";
    }

    @Override
    public OrderDetail convertToMysqlEntity(SynSlSrvSalesbilllist data) {
        return null;
    }

    @Override
    public Map<String, OrderDetail> batchConvertToMysqlEntityWithPrimaryKey(List<SynSlSrvSalesbilllist> updatedData) {
        return convertToOrderDetailMap(updatedData, this::getMysqlPrimaryKeyValue);
    }

    private Map<String, OrderDetail> convertToOrderDetailMap(List<SynSlSrvSalesbilllist> updatedData, Function<OrderDetail, String> keyMapper) {
        List<String> billIds = updatedData.stream()
                .map(SynSlSrvSalesbilllist::getSalesbillid).distinct().map(String::valueOf).toList();

        List<String> goodsIds = updatedData.stream()
                .map(SynSlSrvSalesbilllist::getMateid).distinct().map(String::valueOf).toList();

        Map<String, Order> orderMap = ordersRepository.list(new LambdaQueryWrapper<Order>()
                        .in(Order::getOutId, billIds)
                        .eq(Order::getOutType, getSqlserverSource().getValue()))
                .stream().collect(Collectors.toMap(
                        Order::getOutId,
                        Function.identity(),
                        (exist1, exist2) -> exist1
                ));

        Map<String, GoodsInfo> goodsInfoMap = goodsInfoRepository.list(new LambdaQueryWrapper<GoodsInfo>()
                        .in(getSqlserverSource() == SqlServerSourceEnums.SQLSERVER1, GoodsInfo::getOutId1, goodsIds)
                        .in(getSqlserverSource() == SqlServerSourceEnums.SQLSERVER2, GoodsInfo::getOutId2, goodsIds)
                        .eq(GoodsInfo::getOutType, getSqlserverSource().getValue()))
                .stream().collect(Collectors.toMap(
                        r0 -> switch (getSqlserverSource()) {
                            case LOCAL -> "";
                            case SQLSERVER1 -> r0.getOutId1();
                            case SQLSERVER2 -> r0.getOutId2();
                        },
                        Function.identity(),
                        (exist1, exist2) -> exist1
                ));

        Map<String, OrderDetail> orderDetailMap = new HashMap<>();

        for (SynSlSrvSalesbilllist salesbilllist : updatedData) {
            Order order = orderMap.get(String.valueOf(salesbilllist.getSalesbillid()));
            GoodsInfo goodsInfo = goodsInfoMap.get(String.valueOf(salesbilllist.getMateid()));
            OrderDetail orderDetail = fromSalesbilllist(salesbilllist, order, goodsInfo);

            orderDetailMap.put(keyMapper.apply(orderDetail), orderDetail);
        }

        return orderDetailMap;
    }

    /**
     * SynSlSrvSalesbilllist转OrderDetail
     *
     * @param salesbilllist
     * @param order
     * @param goodsInfo
     * @return
     */
    public OrderDetail fromSalesbilllist(SynSlSrvSalesbilllist salesbilllist, Order order, GoodsInfo goodsInfo) {
        if (salesbilllist == null) {
            return null;
        }

        OrderDetail orderDetail = new OrderDetail();

        orderDetail.setOutId(String.valueOf(salesbilllist.getId()));
        orderDetail.setOutType(getSqlserverSource().getValue());

        long currentTime = System.currentTimeMillis() / 1000;

        if (order != null) {
            orderDetail.setOrderId(order.getId());
            orderDetail.setCustomerId(order.getCustomerId());
        } else {
            orderDetail.setOrderId(-1L);
        }

        if (goodsInfo != null) {
            orderDetail.setGoodsId(goodsInfo.getId());
        } else {
            orderDetail.setGoodsId(-1L);
        }

        orderDetail.setSalePrice(salesbilllist.getSellprice() == null ? BigDecimal.ZERO : salesbilllist.getSellprice().setScale(5, RoundingMode.HALF_UP));
        orderDetail.setSalesQty(salesbilllist.getQty() == null ? BigDecimal.ZERO : salesbilllist.getQty().setScale(3, RoundingMode.HALF_UP));
        orderDetail.setDelivyQty(salesbilllist.getDelivqty() == null ? BigDecimal.ZERO : salesbilllist.getDelivqty().setScale(3, RoundingMode.HALF_UP));
        orderDetail.setBillMoney(salesbilllist.getBillmoney() == null ? BigDecimal.ZERO : salesbilllist.getBillmoney().setScale(5, RoundingMode.HALF_UP));
        orderDetail.setDelivMoney(salesbilllist.getDelivmoney() == null ? BigDecimal.ZERO : salesbilllist.getDelivmoney().setScale(5, RoundingMode.HALF_UP));
        orderDetail.setRspPrice(salesbilllist.getRspPrice() == null ? BigDecimal.ZERO : salesbilllist.getRspPrice().setScale(5, RoundingMode.HALF_UP));
        orderDetail.setWsPrice(salesbilllist.getWsPrice() == null ? BigDecimal.ZERO : salesbilllist.getWsPrice().setScale(5, RoundingMode.HALF_UP));
        orderDetail.setGbPrice(salesbilllist.getGbPrice() == null ? BigDecimal.ZERO : salesbilllist.getGbPrice().setScale(5, RoundingMode.HALF_UP));
        orderDetail.setSbPrice(salesbilllist.getSbPrice() == null ? BigDecimal.ZERO : salesbilllist.getGbPrice().setScale(5, RoundingMode.HALF_UP));

        orderDetail.setCreated(currentTime);
        orderDetail.setUpdated(currentTime);

        return orderDetail;
    }

    @Override
    public void setId(OrderDetail existData, OrderDetail updateData) {
        updateData.setId(existData.getId());
    }
}
