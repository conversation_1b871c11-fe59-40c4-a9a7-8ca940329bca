package com.neo.nova.app.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.nova.app.sync.AbstractTagLeafInfoTableSyncConfig;
import com.neo.nova.domain.entity.sqlserver.SynSlPamCustdistrict;
import com.neo.nova.domain.entity.sqlserver.SynSlPamCustline;
import com.neo.tagcenter.app.constants.EnableStatus;
import com.neo.tagcenter.app.constants.TagDomainEnums;
import com.neo.tagcenter.domain.entity.TagLeafInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

@Slf4j
public abstract class AbstractSynSlPamCustlineConfigImpl extends AbstractTagLeafInfoTableSyncConfig<SynSlPamCustline> {

    @Override
    protected TagDomainEnums getTagDomainEnums() {
        return TagDomainEnums.DELIVERY_ROUTE;
    }

    @Override
    public String getSqlServerTableName() {
        return "syn_sl_pam_custline";
    }


    @Override
    public QueryWrapper<SynSlPamCustline> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynSlPamCustline> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("ID");

        return queryWrapper;
    }

    @Override
    public String getSqlServerPrimaryKeyValue(SynSlPamCustline data) {
        return String.valueOf(data.getId());
    }

    @Override
    public String getSqlServerNameValue(SynSlPamCustline data) {
        return data.getCustlinename();
    }

    @Override
    public TagLeafInfo convertToMysqlEntity(SynSlPamCustline data) {
        return fromCustline(data);
    }

    /**
     * SynSlPamCustline转TagLeafInfo
     *
     * @param custline
     * @return
     */
    public TagLeafInfo fromCustline(SynSlPamCustline custline) {
        if (custline == null) {
            return null;
        }

        TagLeafInfo tagLeafInfo = new TagLeafInfo();

        // 基本信息映射
        switch (getSqlserverSource()) {
            case SQLSERVER1:
                tagLeafInfo.setOutId1(String.valueOf(custline.getId()));
                break;
            case SQLSERVER2:
                tagLeafInfo.setOutId2(String.valueOf(custline.getId()));
                break;
        }
        tagLeafInfo.setOutType(getSqlserverSource().getValue());

        tagLeafInfo.setName(custline.getCustlinename());
        tagLeafInfo.setCode(custline.getCustlineno());
        tagLeafInfo.setLevel(1);

        // 状态映射
        tagLeafInfo.setIsEnabled("1".equals(custline.getUstate()) ? EnableStatus.ENABLE.getValue() : EnableStatus.DISABLE.getValue());
        tagLeafInfo.setIsDeleted(0);

        return tagLeafInfo;
    }

}
