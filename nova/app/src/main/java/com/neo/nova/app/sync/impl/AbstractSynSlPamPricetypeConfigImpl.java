package com.neo.nova.app.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.nova.app.sync.AbstractTagLeafInfoTableSyncConfig;
import com.neo.nova.domain.entity.sqlserver.SynSlPamPricetype;
import com.neo.tagcenter.app.constants.EnableStatus;
import com.neo.tagcenter.app.constants.TagDomainEnums;
import com.neo.tagcenter.domain.entity.TagLeafInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
public abstract class AbstractSynSlPamPricetypeConfigImpl extends AbstractTagLeafInfoTableSyncConfig<SynSlPamPricetype> {

    @Override
    protected TagDomainEnums getTagDomainEnums() {
        return TagDomainEnums.PRICE_PLAN;
    }

    @Override
    public String getSqlServerTableName() {
        return "syn_sl_pam_pricetype";
    }


    @Override
    public QueryWrapper<SynSlPamPricetype> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynSlPamPricetype> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("ID");
        return queryWrapper;
    }

    @Override
    public String getSqlServerPrimaryKeyValue(SynSlPamPricetype data) {
        return String.valueOf(data.getId());
    }

    @Override
    public String getSqlServerNameValue(SynSlPamPricetype data) {
        return data.getPricetypename();
    }

    @Override
    public TagLeafInfo convertToMysqlEntity(SynSlPamPricetype data) {
        return fromPricetype(data);
    }

    /**
     * SynSlPamPricetype转TagLeafInfo
     *
     * @param pricetype
     * @return
     */
    private TagLeafInfo fromPricetype(SynSlPamPricetype pricetype) {
        if (pricetype == null) {
            return null;
        }

        TagLeafInfo tagLeafInfo = new TagLeafInfo();

        // 基本信息映射
        switch (getSqlserverSource()) {
            case SQLSERVER1:
                tagLeafInfo.setOutId1(String.valueOf(pricetype.getId()));
                break;
            case SQLSERVER2:
                tagLeafInfo.setOutId2(String.valueOf(pricetype.getId()));
                break;
        }
        tagLeafInfo.setOutType(getSqlserverSource().getValue());

        tagLeafInfo.setName(pricetype.getPricetypename());
        tagLeafInfo.setCode(pricetype.getPricetypeno());
        tagLeafInfo.setLevel(1);

        // 状态映射
        tagLeafInfo.setIsEnabled("1".equals(pricetype.getUstate()) ? EnableStatus.ENABLE.getValue() : EnableStatus.DISABLE.getValue());
        tagLeafInfo.setIsDeleted(0);

        return tagLeafInfo;
    }

}
