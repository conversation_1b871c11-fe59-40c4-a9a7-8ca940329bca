package com.neo.nova.app.sync.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.api.SingleResponse;
import com.neo.nova.app.action.enums.SqlServerSourceEnums;
import com.neo.nova.app.service.CustomerMetricService;
import com.neo.nova.app.service.SalesDataUpdateService;
import com.neo.nova.app.sync.AbstractTableSyncConfig;
import com.neo.nova.domain.entity.CustomerInfo;
import com.neo.nova.domain.entity.GoodsInfo;
import com.neo.nova.domain.entity.Metric;
import com.neo.nova.domain.entity.sqlserver.SynSlPamCustom;
import com.neo.nova.domain.entity.sqlserver.SynSlPamSaleser;
import com.neo.nova.domain.enums.CustomerStatusEnum;
import com.neo.nova.domain.enums.MetricCodeEnum;
import com.neo.nova.domain.gateway.sqlserver.SynSlPamSaleserRepository;
import com.neo.nova.infrastructure.mapper.CustomerInfoMapper;
import com.neo.nova.infrastructure.mapper.sqlserver2.SynSlPamSaleser2Mapper;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;
import com.neo.tagcenter.client.param.BaseTagQueryOption;
import com.neo.tagcenter.client.param.BusinessTreeTagQueryParam;
import com.neo.tagcenter.client.rpc.BusinessTreeTagReadReadService;
import com.neo.tagcenter.infrastructure.mapper.TagLeafInfoMapper;
import com.neo.user.client.tenant.api.MobileTenantService;
import com.neo.user.client.userinfo.api.UserService;
import com.neo.user.client.userinfo.dto.UserMobileInfoDTO;
import jakarta.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.function.Function;

public abstract class AbstractSynSlPamCustomConfigImpl extends AbstractTableSyncConfig<SynSlPamCustom, CustomerInfo> {

    @Resource
    private SalesDataUpdateService salesDataUpdateService;
    @Resource
    private CustomerInfoMapper customerInfoMapper;

    @Resource
    private BusinessTreeTagReadReadService businessTreeTagReadReadService;

    @Resource
    private UserService userService;
    @Resource
    private MobileTenantService mobileTenantService;
    @Resource
    private SynSlPamSaleserRepository synSlPamSaleserRepository;
    @Resource
    private SynSlPamSaleser2Mapper synSlPamSaleser2Mapper;
    @Resource
    private TagLeafInfoMapper tagLeafInfoMapper;
    @Resource
    private CustomerMetricService customerMetricService;

    @Override
    public BaseMapper<CustomerInfo> getMysqlMapper() {
        return customerInfoMapper;
    }


    @Override
    public String getSqlServerTableName() {
        return "syn_sl_pam_Custom";
    }

    @Override
    public String getMysqlTableName() {
        return "CustomerInfo";
    }


    @Override
    public QueryWrapper<SynSlPamCustom> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynSlPamCustom> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("id");

        return queryWrapper;
    }

    @Override
    public boolean needUpdate(CustomerInfo existData, CustomerInfo updateData) {
        if (existData == null || updateData == null) {
            return false;
        }

        int enableToUpdate = enableToUpdate(getSqlserverSource().getValue(), existData.getOutType(),
                updateData.getStatus(), existData.getStatus(), CustomerStatusEnum.NORMAL.getCode());
        if (enableToUpdate == 1) {
            updateData.setId(existData.getId());
            return true;
        } else if (enableToUpdate == -1) {
            if (getSqlserverSource() == SqlServerSourceEnums.SQLSERVER2 &&
                    StrUtil.isBlank(existData.getOutId2())) {
                String outId2 = updateData.getOutId2();
                BeanUtils.copyProperties(new GoodsInfo(), updateData);

                updateData.setOutId2(outId2);
                return true;
            }
            return false;
        }

        // 比较客户编号
        if (!stringCompare(existData.getCode(), updateData.getCode())) {
            return true;
        }
        if (!Objects.equals(existData.getPriceTypeId(), updateData.getPriceTypeId())) {
            return true;
        }
        // 比较客户名称
        if (!stringCompare(existData.getName(), updateData.getName())) {
            return true;
        }
        if (!stringCompare(existData.getChannel(), updateData.getChannel())) {
            return true;
        }
        if (!stringCompare(existData.getSupermarketAreaId(), updateData.getSupermarketAreaId())) {
            return true;
        }
        if (!stringCompare(existData.getMarketId(), updateData.getMarketId())) {
            return true;
        }
        // 比较助记码
        if (!stringCompare(existData.getMnemoCode(), updateData.getMnemoCode())) {
            return true;
        }
        // 比较联系人
        if (!stringCompare(existData.getLinkMan(), updateData.getLinkMan())) {
            return true;
        }
        // 比较联系电话
        if (!stringCompare(existData.getContractNumber(), updateData.getContractNumber())) {
            return true;
        }
        // 比较送货地址
        if (!stringCompare(existData.getDeliveryAddress(), updateData.getDeliveryAddress())) {
            return true;
        }
        if (!Objects.equals(existData.getCustomerTypeId(), updateData.getCustomerTypeId())) {
            return true;
        }
        if (!Objects.equals(existData.getCustomerLineId(), updateData.getCustomerLineId())) {
            return true;
        }
        if (!Objects.equals(existData.getAdminRegionId(), updateData.getAdminRegionId())) {
            return true;
        }
        if (!Objects.equals(existData.getCustomerAreaId(), updateData.getCustomerAreaId())) {
            return true;
        }
        if (!Objects.equals(existData.getOpenDate(), updateData.getOpenDate())) {
            return true;
        }
        if (!Objects.equals(existData.getAuditFlag(), updateData.getAuditFlag())) {
            return true;
        }
        if (!Objects.equals(existData.getSalesId(), updateData.getSalesId())) {
            return true;
        }
        if (!Objects.equals(existData.getDispPrice(), updateData.getDispPrice())) {
            return true;
        }

        return false;
    }

    @Override
    public String getMysqlPrimaryKeyValue(CustomerInfo data) {
        return switch (getSqlserverSource()) {
            case LOCAL -> "";
            case SQLSERVER1 -> data.getOutId1();
            case SQLSERVER2 -> data.getOutId2();
        };
    }

    @Override
    public String getSqlServerPrimaryKeyValue(SynSlPamCustom data) {
        return String.valueOf(data.getId());
    }

    @Override
    public String getSqlServerNameValue(SynSlPamCustom data) {
        return data.getCustname();
    }

    @Override
    public String getMysqlNameColumnName() {
        return "name";
    }

    @Override
    public String getMysqlNameValue(CustomerInfo data) {
        return data.getName();
    }

    @Override
    public CustomerInfo convertToMysqlEntity(SynSlPamCustom data) {
        return null;
    }

    @Override
    public Map<String, CustomerInfo> batchConvertToMysqlEntityWithPrimaryKey(List<SynSlPamCustom> updatedData) {
        return convertToCustomerInfoMap(updatedData, this::getMysqlPrimaryKeyValue);
    }

    @Override
    public Map<String, CustomerInfo> batchConvertToMysqlEntityWithName(List<SynSlPamCustom> updatedData) {
        return convertToCustomerInfoMap(updatedData, this::getMysqlNameValue);
    }

    private Map<String, CustomerInfo> convertToCustomerInfoMap(List<SynSlPamCustom> updatedData, Function<CustomerInfo, String> keyMapper) {
        if (CollUtil.isEmpty(updatedData)) {
            return new HashMap<>();
        }

        // 批量查询所有相关标签信息
        CustomTagMap customTagMap = batchQueryAllTagMaps(updatedData);

        // 批量转换
        Map<String, CustomerInfo> result = new HashMap<>();
        for (SynSlPamCustom data : updatedData) {
            TagLeafInfoDto customerTypeTagLeafInfoDto = customTagMap.customerTypeMap.get(String.valueOf(data.getCusttypeid()));
            TagLeafInfoDto deliveryRouteTagLeafInfoDto = customTagMap.deliveryRouteMap.get(String.valueOf(data.getCustlineid()));
            TagLeafInfoDto administrativeRegionTagLeafInfoDto = customTagMap.administrativeRegionMap.get(String.valueOf(data.getCustdistrictid()));
            TagLeafInfoDto customerAreaTagLeafInfoDto = customTagMap.customerAreaMap.get(String.valueOf(data.getCustareaid()));
            TagLeafInfoDto priceTypeTagLeafInfoDto = customTagMap.priceTypeMap.get(String.valueOf(data.getPricetypeid()));

            CustomerInfo customerInfo = fromCustom(data,
                    customerTypeTagLeafInfoDto, deliveryRouteTagLeafInfoDto,
                    administrativeRegionTagLeafInfoDto, customerAreaTagLeafInfoDto,
                    priceTypeTagLeafInfoDto);

            result.put(keyMapper.apply(customerInfo), customerInfo);
        }

        return result;
    }

    /**
     * 批量查询所有标签映射
     */
    private CustomTagMap batchQueryAllTagMaps(List<SynSlPamCustom> updatedData) {
        // 提取所有ID列表
        List<String> cusTypeIds = extractIds(updatedData, SynSlPamCustom::getCusttypeid);
        List<String> cusLineIds = extractIds(updatedData, SynSlPamCustom::getCustlineid);
        List<String> adminRegionIds = extractIds(updatedData, SynSlPamCustom::getCustdistrictid);
        List<String> cusAreaIds = extractIds(updatedData, SynSlPamCustom::getCustareaid);
        List<String> priceTypeIds = extractIds(updatedData, SynSlPamCustom::getPricetypeid);

        // 批量查询所有标签
        return new CustomTagMap(
                batchQueryTags(cusTypeIds, businessTreeTagReadReadService::batchQueryCustomerTypeSetting),
                batchQueryTags(cusLineIds, businessTreeTagReadReadService::batchQueryDeliveryRouteSetting),
                batchQueryTags(adminRegionIds, businessTreeTagReadReadService::batchQueryAdministrativeRegionSetting),
                batchQueryTags(cusAreaIds, businessTreeTagReadReadService::batchQuerySalesRegionSetting),
                batchQueryTags(priceTypeIds, businessTreeTagReadReadService::batchQueryPricePlanSetting)
        );
    }

    /**
     * 提取ID列表的通用方法
     */
    private List<String> extractIds(List<SynSlPamCustom> updatedData, Function<SynSlPamCustom, Integer> idExtractor) {
        return updatedData.stream()
                .map(idExtractor)
                .filter(Objects::nonNull)
                .map(String::valueOf)
                .distinct()
                .toList();
    }

    /**
     * 批量查询标签的通用方法
     */
    private Map<String, TagLeafInfoDto> batchQueryTags(List<String> ids,
                                                       BiFunction<BusinessTreeTagQueryParam, BaseTagQueryOption, SingleResponse<Map<String, TagLeafInfoDto>>> queryFunction) {
        if (CollUtil.isEmpty(ids)) {
            return new HashMap<>();
        }

        BusinessTreeTagQueryParam param = createBaseQueryParam();
        setOutIdList(param, ids);

        BaseTagQueryOption option = new BaseTagQueryOption();
        option.setIncludeDeleted(true);
        option.setIncludeDisable(true);

        SingleResponse<Map<String, TagLeafInfoDto>> response = queryFunction.apply(param, option);
        if (response.isSuccess() && MapUtil.isNotEmpty(response.getData())) {
            return response.getData();
        }

        return new HashMap<>();
    }

    /**
     * 创建基础查询参数
     */
    private BusinessTreeTagQueryParam createBaseQueryParam() {
        BusinessTreeTagQueryParam param = new BusinessTreeTagQueryParam();
        param.setBusinessDomain(getTenantId().intValue());
        return param;
    }

    /**
     * 根据数据源设置outId列表
     */
    private void setOutIdList(BusinessTreeTagQueryParam param, List<String> ids) {
        switch (getSqlserverSource()) {
            case SQLSERVER1:
                param.setOutId1List(ids);
                break;
            case SQLSERVER2:
                param.setOutId2List(ids);
                break;
        }
    }

    /**
     * 标签映射容器类
     */
    private static class CustomTagMap {
        final Map<String, TagLeafInfoDto> customerTypeMap;
        final Map<String, TagLeafInfoDto> deliveryRouteMap;
        final Map<String, TagLeafInfoDto> administrativeRegionMap;
        final Map<String, TagLeafInfoDto> customerAreaMap;
        final Map<String, TagLeafInfoDto> priceTypeMap;

        CustomTagMap(Map<String, TagLeafInfoDto> customerTypeMap,
                     Map<String, TagLeafInfoDto> deliveryRouteMap,
                     Map<String, TagLeafInfoDto> administrativeRegionMap,
                     Map<String, TagLeafInfoDto> customerAreaMap,
                     Map<String, TagLeafInfoDto> priceTypeMap) {
            this.customerTypeMap = customerTypeMap;
            this.deliveryRouteMap = deliveryRouteMap;
            this.administrativeRegionMap = administrativeRegionMap;
            this.customerAreaMap = customerAreaMap;
            this.priceTypeMap = priceTypeMap;
        }
    }


    /**
     * SynSlPamCustom转CustomerInfo
     *
     * @param custom
     * @return
     */
    public CustomerInfo fromCustom(SynSlPamCustom custom,
                                   TagLeafInfoDto customerTypeTagLeafInfoDto,
                                   TagLeafInfoDto deliveryRouteTagLeafInfoDto,
                                   TagLeafInfoDto administrativeRegionTagLeafInfoDto,
                                   TagLeafInfoDto customerAreaTagLeafInfoDto,
                                   TagLeafInfoDto priceTypeTagLeafInfoDto) {
        if (custom == null) {
            return null;
        }


        long now = System.currentTimeMillis() / 1000;

        CustomerInfo customerInfo = new CustomerInfo();

        customerInfo.setSalesId(mappingSalesId(custom));

        customerInfo.setCode(custom.getCustno());
        customerInfo.setName(custom.getCustname());
        customerInfo.setMnemoCode(custom.getMnemocode());
        customerInfo.setLinkMan(custom.getLinkman());
        customerInfo.setContractNumber(custom.getContactnumber());
        customerInfo.setDeliveryAddress(custom.getTdelivyaddress());

        // 安全地设置标签ID，避免空指针异常
        customerInfo.setCustomerTypeId(customerTypeTagLeafInfoDto != null ? customerTypeTagLeafInfoDto.getId() : -1);
        customerInfo.setCustomerLineId(deliveryRouteTagLeafInfoDto != null ? deliveryRouteTagLeafInfoDto.getId() : -1);
        customerInfo.setAdminRegionId(administrativeRegionTagLeafInfoDto != null ? administrativeRegionTagLeafInfoDto.getId() : -1);
        customerInfo.setCustomerAreaId(customerAreaTagLeafInfoDto != null ? customerAreaTagLeafInfoDto.getId() : -1);
        customerInfo.setPriceTypeId(priceTypeTagLeafInfoDto != null ? priceTypeTagLeafInfoDto.getId() : -1);


        customerInfo.setOpenDate(custom.getOpendate().getTime() / 1000);
        customerInfo.setAuditFlag(Integer.valueOf(custom.getAuditflag()));
        customerInfo.setStatus(Integer.valueOf(custom.getUstate()));
        customerInfo.setDispPrice(Integer.valueOf(custom.getDispprice()));
        customerInfo.setCreatedBy(-1L);
        customerInfo.setCreated(now);
        customerInfo.setUpdatedBy(-1L);
        customerInfo.setUpdated(now);
        customerInfo.setTenantId(getTenantId());
        switch (getSqlserverSource()) {
            case SQLSERVER1:
                customerInfo.setOutId1(String.valueOf(custom.getId()));
                break;
            case SQLSERVER2:
                customerInfo.setOutId2(String.valueOf(custom.getId()));
                break;
        }
        customerInfo.setOutType(getSqlserverSource().getValue());

        Metric channle = customerMetricService.queryCustomerMetricCodeId(getTenantId(), MetricCodeEnum.CHANNEL.getCode(), (customerInfo));
        Metric supermaker = customerMetricService.queryCustomerMetricCodeId(getTenantId(), MetricCodeEnum.AREA.getCode(), (customerInfo));

        customerInfo.setChannel(channle == null ? "" : channle.getMetricCodeId());
        customerInfo.setSupermarketAreaId(supermaker == null ? "" : supermaker.getMetricCodeId());

        return customerInfo;
    }


    private Long mappingSalesId(SynSlPamCustom custom) {
        if (custom.getSaleserid() == null) {
            return 0L;
        }
        //查询业务员的信息
        SynSlPamSaleser saleser = switch (getSqlserverSource()) {
            case LOCAL -> null;
            case SQLSERVER1 -> synSlPamSaleserRepository.getById(custom.getSaleserid());
            case SQLSERVER2 -> synSlPamSaleser2Mapper.selectById(custom.getSaleserid());
        };

        if (saleser != null && StringUtils.isNotBlank(saleser.getPhoneNo())) {
            SingleResponse<UserMobileInfoDTO> userMobileInfoDTOSingleResponse = mobileTenantService.queryMobileInfoByMobile(saleser.getPhoneNo(), "86", getTenantId());
            if (userMobileInfoDTOSingleResponse.isSuccess()) {
                UserMobileInfoDTO userMobileInfoDTO = userMobileInfoDTOSingleResponse.getData();
                return userMobileInfoDTO.getUserId();
            }
        }
        return 0L;
    }


    @Override
    public void setId(CustomerInfo existData, CustomerInfo updateData) {
        updateData.setId(existData.getId());
    }
}
