package com.neo.nova.app.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.nova.app.sync.AbstractTagLeafInfoTableSyncConfig;
import com.neo.nova.domain.entity.sqlserver.SynSlPamSaleser;
import com.neo.tagcenter.app.constants.EnableStatus;
import com.neo.tagcenter.app.constants.TagDomainEnums;
import com.neo.tagcenter.domain.entity.TagLeafInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public abstract class AbstractSynSlPamSaleserConfigImpl extends AbstractTagLeafInfoTableSyncConfig<SynSlPamSaleser> {

    @Override
    protected TagDomainEnums getTagDomainEnums() {
        return null;
    }

    @Override
    public String getSqlServerTableName() {
        return "syn_sl_pam_saleser";
    }


    @Override
    public QueryWrapper<SynSlPamSaleser> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynSlPamSaleser> queryWrapper = new QueryWrapper<>();
        return queryWrapper;
    }



    @Override
    public String getSqlServerPrimaryKeyValue(SynSlPamSaleser data) {
        return String.valueOf(data.getId());
    }

    @Override
    public String getSqlServerNameValue(SynSlPamSaleser data) {
        return data.getSalesername();
    }

    @Override
    public TagLeafInfo convertToMysqlEntity(SynSlPamSaleser data) {
        return fromSaleser(data);
    }

    /**
     * SynSlPamSaleser转TagLeafInfo
     *
     * @param saleser
     * @return
     */
    private TagLeafInfo fromSaleser(SynSlPamSaleser saleser) {
        if (saleser == null) {
            return null;
        }

        TagLeafInfo tagLeafInfo = new TagLeafInfo();

        // 基本信息映射
        switch (getSqlserverSource()) {
            case SQLSERVER1:
                tagLeafInfo.setOutId1(String.valueOf(saleser.getId()));
                break;
            case SQLSERVER2:
                tagLeafInfo.setOutId2(String.valueOf(saleser.getId()));
                break;
        }

        tagLeafInfo.setName(saleser.getSalesername());
        tagLeafInfo.setCode(saleser.getSaleserno());

        // 状态映射
        tagLeafInfo.setIsEnabled("1".equals(saleser.getUstate()) ? EnableStatus.ENABLE.getValue() : EnableStatus.DISABLE.getValue());
        tagLeafInfo.setIsDeleted(0);

        return tagLeafInfo;
    }
}
