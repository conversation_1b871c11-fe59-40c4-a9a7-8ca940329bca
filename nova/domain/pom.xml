<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.neo</groupId>
        <artifactId>nova</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>nova-domain</artifactId>
    <name>nova-domain</name>

    <!-- notify 不允许单独指定版本-->
    <!-- notify 不允许引用环境jar包-->
    <dependencies>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-common</artifactId>
        </dependency>

        <!--  notify 仅允许引同项目client，项目中为开发方便也可以引其他模型client，但模型中不允许    -->
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>nova-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-json</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel-core</artifactId>
            <version>4.0.3</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-mcp-server-webmvc</artifactId>
        </dependency>
    </dependencies>
</project>
