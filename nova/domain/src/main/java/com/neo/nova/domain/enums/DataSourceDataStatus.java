package com.neo.nova.domain.enums;

import lombok.Getter;

/**
 * 0 处理中 1 待确认 2 已生效
 * <AUTHOR>
 * @since 2025/7/14
 **/
@Getter
public enum DataSourceDataStatus {

    PROCESS(0, "处理中"),
    WAIT_CONFIRM(1, "待确认"),
    FAILED(3, "处理失败"),
    CONFIRMED(2, "已生效"),
    ;
    final int code;
    final String desc;

    DataSourceDataStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
