package com.neo.nova.domain.dto;



import lombok.Data;

import java.time.LocalDateTime;
@Data
public class WorkOrderRemarkDTO {
    /**
     * 主键
     */

    private Long id;

    /**
     * 创建人id
     */

    private Long creatorId;

    /**
     * 工单id
     */

    private Long workOrderId;

    /**
     * 收件人
     */

    private Long recipient;

    /**
     * 内容
     */

    private String content;

    /**
     * 状态
     */

    private Integer status;

    /**
     * 创建时间
     */

    private LocalDateTime created;

    /**
     * 更新时间
     */

    private LocalDateTime updated;

    /**
     * 是否删除 0 否 1 是
     */
    private Integer deleted;

}
