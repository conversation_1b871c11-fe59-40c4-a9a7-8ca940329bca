package com.neo.nova.domain.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/8/15
 **/
@Getter
public enum GoodsSourceEnum {


    SELF(0, "自产"),
    EXTERNAL(1, "外采"),
    ;
    final int code;
    final String desc;

    GoodsSourceEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static GoodsSourceEnum getByCode(int code) {
        for (GoodsSourceEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
