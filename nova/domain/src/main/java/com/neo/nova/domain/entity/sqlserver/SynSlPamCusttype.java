package com.neo.nova.domain.entity.sqlserver;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @TableName syn_sl_pam_custtype
 */
@TableName(value = "syn_sl_pam_custtype")
@Data
public class SynSlPamCusttype {
    /**
     *
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    /**
     * 客户类型编号
     */
    @TableField(value = "CustTypeNo")
    private String custtypeno;

    /**
     * 客户类型名称
     */
    @TableField(value = "CustTypeName")
    private String custtypename;

    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 状态
     */
    @TableField(value = "UState")
    private String ustate;

    /**
     * 层级深度
     */
    @TableField(value = "Depth")
    private Integer depth;

    /**
     * 父级ID
     */
    @TableField(value = "ParentID")
    private Integer parentid;

    /**
     * 排序码
     */
    @TableField(value = "OrderCode")
    private String ordercode;
}