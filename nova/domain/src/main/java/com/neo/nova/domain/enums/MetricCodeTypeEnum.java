package com.neo.nova.domain.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/7/3
 **/
@Getter
public enum MetricCodeTypeEnum {

    DIRECT_METRIC(0, "直接指标", "D_"),
    COMPLEX_METRIC(1, "聚合指标", "C_"),
    SPECIAL_METRIC(2, "特殊指标", "S_"),
    ;
    final int code;
    final String desc;
    final String prefix;

    MetricCodeTypeEnum(int code, String desc, String prefix) {
        this.code = code;
        this.desc = desc;
        this.prefix = prefix;
    }
}
