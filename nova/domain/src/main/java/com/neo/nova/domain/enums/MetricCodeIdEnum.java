package com.neo.nova.domain.enums;

import lombok.Getter;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/7/21
 **/
@Getter
public enum MetricCodeIdEnum {

    // 渠道类型
    CHANNEL_JOINT_SUPERMARKET("JOINT_SUPERMARKET", "联营商超", MetricCodeEnum.CHANNEL),
    CHANNEL_PURCHASE_SUPERMARKET("PURCHASE_SUPERMARKET", "购销商超", MetricCodeEnum.CHANNEL),
    CHANNEL_VIP("VIP", "大客", MetricCodeEnum.CHANNEL),
    CHANNEL_DEALER("DEALER", "经销商", MetricCodeEnum.CHANNEL),
    CHANNEL_DIRECT("DIRECT", "直营店", MetricCodeEnum.CHANNEL),

    // 区域
    AREA_EAST("EAST", "东部", MetricCodeEnum.AREA),
    AREA_WEST("WEST", "西部", MetricCodeEnum.AREA),
    AREA_SOUTH("SOUTH", "南部", MetricCodeEnum.AREA),

    // 市场
    MARKET_GANZHOU("GANZHOU", "赣州", MetricCodeEnum.MARKET),
    MARKET_GUANGDONG("GUANGDONG", "广东", MetricCodeEnum.MARKET),


    ;
    final String code;
    final String name;
    final MetricCodeEnum parentCode;

    MetricCodeIdEnum(String code, String name, MetricCodeEnum parentCode) {
        this.code = code;
        this.name = name;
        this.parentCode = parentCode;
    }

    public static MetricCodeIdEnum getByCode(String code) {
        for (MetricCodeIdEnum value : MetricCodeIdEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

}
