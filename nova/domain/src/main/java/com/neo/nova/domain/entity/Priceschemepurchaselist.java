package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 价格方案对应的购货清单
 *
 * @TableName PriceSchemePurchaseList
 */
@TableName(value = "PriceSchemePurchaseList")
@Data
public class Priceschemepurchaselist {
    /**
     * 主键ID，自增
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 客户类型ID
     */
    @TableField(value = "priceTypeID")
    private Long priceTypeID;

    /**
     * 材料ID
     */
    @TableField(value = "matelID")
    private Long matelID;

    /**
     * 销售价格
     */
    @TableField(value = "sellPrice")
    private BigDecimal sellPrice;

    /**
     * 打印价格
     */
    @TableField(value = "printPrice")
    private String printPrice;
}