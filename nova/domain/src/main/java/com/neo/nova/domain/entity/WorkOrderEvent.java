package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 工单事件表
 * @TableName WorkOrderEvent
 */
@TableName(value ="WorkOrderEvent")
@Data
public class WorkOrderEvent implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField(value = "tenantId")
    private Long tenantId;

    /**
     * 工单id
     */
    @TableField(value = "workOrderId")
    private Long workOrderId;

    /**
     * 工单明细id
     */
    @TableField(value = "workOrderDetailId")
    private Long workOrderDetailId;

    /**
     * 事件发生人
     */
    @TableField(value = "creatorId")
    private Long creatorId;

    /**
     * 事件类型
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 任务描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 状态
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private LocalDateTime created;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private LocalDateTime updated;

    /**
     * 是否删除 0 否 1 是
     */
    @TableField(value = "deleted")
    private Integer deleted;


    /**
     * 开始时间
     */
    @TableField(value = "workOrderStartTime")
    private LocalDateTime workOrderStartTime;

    /**
     * 结束时间
     */
    @TableField(value = "workOrderEndTime")
    private LocalDateTime workOrderEndTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}