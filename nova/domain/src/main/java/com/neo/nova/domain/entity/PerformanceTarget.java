package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 业绩目标表
 * @TableName PerformanceTarget
 */
@TableName(value ="PerformanceTarget")
@Data
public class PerformanceTarget {
    /**
     * 目标ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenantId")
    private Long tenantId;

    /**
     * 计划ID
     */
    @TableField(value = "planId")
    private Long planId;

    /**
     * 目标名称
     */
    @TableField(value = "targetName")
    private String targetName;

    /**
     * 指标标识
     */
    @TableField(value = "metricCode")
    private String metricCode;

    /**
     * 指标标识 0自定义1销售额
     */
    @TableField(value = "metricDataType")
    private Integer metricDataType;

    /**
     * 单位 0元
     */
    @TableField(value = "unit")
    private String unit;

    /**
     * 目标id
     */
    @TableField(value = "targetId")
    private String targetId;

    /**
     * 目标值
     */
    @TableField(value = "targetValue")
    private BigDecimal targetValue;

    /**
     * 父目标ID
     */
    @TableField(value = "parentId")
    private Long parentId;

    /**
     * 子目标树 逗号分隔
     */
    @TableField(value = "children")
    private String children;

    /**
     * 子目标树 逗号分隔
     */
    @TableField(value = "rootId")
    private Long rootId;

    /**
     * 负责人ID
     */
    @TableField(value = "ownerId")
    private Long ownerId;

    /**
     * 创建人
     */
    @TableField(value = "createdBy")
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private Long created;

    /**
     * 更新人
     */
    @TableField(value = "updatedBy")
    private Long updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private Long updated;

    /**
     * 0正常1已删除
     */
    @TableField(value = "isDeleted")
    private Integer isDeleted;
}
