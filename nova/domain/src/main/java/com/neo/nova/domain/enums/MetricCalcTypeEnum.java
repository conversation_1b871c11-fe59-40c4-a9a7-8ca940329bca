package com.neo.nova.domain.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/7/21
 **/
@Getter
public enum MetricCalcTypeEnum {


    METRIC_PARTICIPATE_IN(0, "{metricCode}{participates} in (A,B,C)"),
    METRIC_PARTICIPATE_NOT_IN(1, "{metricCode}{participates} in (A,B,C)"),
    ;
    final int code;
    final String desc;

    MetricCalcTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
