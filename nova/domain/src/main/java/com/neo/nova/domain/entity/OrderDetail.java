package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;

import lombok.Data;

/**
 * 订单明细表
 *
 * @TableName OrderDetail
 */
@TableName(value = "OrderDetail")
@Data
public class OrderDetail {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单id
     */
    @TableField(value = "orderId")
    private Long orderId;

    /**
     * 三方订单ID
     */
    @TableField(value = "outId")
    private String outId;

    /**
     * 三方类型
     */
    @TableField(value = "outType")
    private String outType;

    /**
     * 客户id
     */
    @TableField(value = "customerId")
    private Long customerId;

    /**
     * 货品ID
     */
    @TableField(value = "goodsId")
    private Long goodsId;

    /**
     * 销售单价 元
     * 勤润：SellPrice
     */
    @TableField(value = "salePrice")
    private BigDecimal salePrice;

    /**
     * 销售数量
     * 勤润：Qty
     */
    @TableField(value = "salesQty")
    private BigDecimal salesQty;

    /**
     * 发货数量
     * 勤润：delivQty
     */
    @TableField(value = "delivyQty")
    private BigDecimal delivyQty;

    /**
     * 订货金额
     * 勤润：billMoney
     */
    @TableField(value = "billMoney")
    private BigDecimal billMoney;

    /**
     * 发货金额
     * 勤润：delivMoney
     */
    @TableField(value = "delivMoney")
    private BigDecimal delivMoney;

    /**
     * 零售价
     * 勤润：rspPrice
     */
    @TableField(value = "rspPrice")
    private BigDecimal rspPrice;

    /**
     * 批发价
     * 勤润：wsPrice
     */
    @TableField(value = "wsPrice")
    private BigDecimal wsPrice;

    /**
     * 团购价
     * 勤润：gbPrice
     */
    @TableField(value = "gbPrice")
    private BigDecimal gbPrice;

    /**
     * 备用价
     * 勤润：sbPrice
     */
    @TableField(value = "sbPrice")
    private BigDecimal sbPrice;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private Long created;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private Long updated;

    /**
     *
     */
    @TableField(value = "deleted")
    private Integer deleted;
}
