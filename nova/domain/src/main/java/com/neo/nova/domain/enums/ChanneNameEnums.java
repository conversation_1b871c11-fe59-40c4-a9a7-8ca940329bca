package com.neo.nova.domain.enums;

import lombok.Getter;

@Getter
public enum ChanneNameEnums {

    VIP("大客","VIP"),
    MARKET("商超","MARKET"),
    DIRECT("直营","DIRECT");

    final String name;
    final String code;

    ChanneNameEnums(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public static ChanneNameEnums getByCode(String code) {
        for (ChanneNameEnums value : ChanneNameEnums.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

}
