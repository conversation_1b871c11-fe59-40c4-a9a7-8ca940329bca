package com.neo.nova.domain.enums;

import com.google.common.collect.Maps;
import com.neo.nova.domain.entity.CustomerRemark;
import lombok.Getter;

import java.util.Map;
import java.util.Set;
@Getter
public enum CustomerRemarkTypeEnum {
    CHICKANDFIX("chickAndFix",  2),
    VISITRECORD("visitingRecord", 1),
    FOLLOWCONTENT("followContent",  0),
    STATION("station",  3);

    ;
    final String code;
    final Integer id;

    CustomerRemarkTypeEnum(String code,int id) {
        this.code = code;
        this.id = id;
    }

    public static CustomerRemarkTypeEnum getByCode(String code) {
        for (CustomerRemarkTypeEnum value : CustomerRemarkTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static CustomerRemarkTypeEnum getById(Integer id) {
        for (CustomerRemarkTypeEnum value :CustomerRemarkTypeEnum.values()) {
            if (value.id == id) {
                return value;
            }
        }
        return null;
    }




}
