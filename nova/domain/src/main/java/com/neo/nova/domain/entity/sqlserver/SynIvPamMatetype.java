package com.neo.nova.domain.entity.sqlserver;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @TableName syn_iv_pam_matetype
 */
@TableName(value = "syn_iv_pam_matetype")
@Data
public class SynIvPamMatetype {
    /**
     *
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    /**
     *
     */
    @TableField(value = "MateTypeNo")
    private String matetypeno;

    /**
     *
     */
    @TableField(value = "MateTypeName")
    private String matetypename;

    /**
     *
     */
    @TableField(value = "description")
    private String description;

    /**
     *
     */
    @TableField(value = "Ustate")
    private String ustate;

    /**
     *
     */
    @TableField(value = "Depth")
    private Integer depth;

    /**
     *
     */
    @TableField(value = "ParentID")
    private Integer parentid;

    /**
     *
     */
    @TableField(value = "OrderCode")
    private String ordercode;
}