package com.neo.nova.domain.constants;


import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/24
 **/
public class DataConstants {

    /**
     * 全店汇总货品ID常量
     */
    public static final Long SUMMARY_GOODS_ID = 0L;

    /**
     * 全店汇总货品名称常量
     */
    public static final String SUMMARY_GOODS_NAME = "合计";


    /**
     * 该部分客户在勤润系统1的订单仅为调拨使用，需要剔除统计。
     */
    public static final List<String> filterCustomerNamesFromServer1 = Lists.newArrayList(
            "赣州华东城门店"
    );

    /**
     * 该部分客户在勤润的订单仅为调拨使用，需要剔除统计。
     */
    public static final List<String> filterCustomerNames = Lists.newArrayList(
            "隔壁家", "隔壁阿姨卫府里门店", "隔壁家-赣州仓"
    );

    /**
     * 该部分类型的商品需要被统计进入汇总表
     */
    public static final List<String> includeItemTypeTagNames = Lists.newArrayList("成品");

    /**
     * 该部分类型的商品需要被过滤
     */
    public static final List<String> filterItemTypeTagNames = Lists.newArrayList("其他类", "箱筐类");
}
