package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 指标参与者表
 * @TableName MetricParticipate
 */
@TableName(value ="MetricParticipate")
@Data
public class MetricParticipate {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenantId")
    private Long tenantId;

    /**
     * 指标id
     */
    @TableField(value = "metricId")
    private Long metricId;

    /**
     * 指标标识
     */
    @TableField(value = "metricCode")
    private String metricCode;

    /**
     * 指标唯一id ,用于业务写死调用
     */
    @TableField(value = "metricCodeId")
    private String metricCodeId;

    /**
     * 对象id
     */
    @TableField(value = "participateId")
    private Long participateId;

    /**
     * 参与者指标标识
     */
    @TableField(value = "participateMetricCode")
    private String participateMetricCode;

    /**
     * 参与者指标标识
     */
    @TableField(value = "calcType")
    private Integer calcType;

    /**
     * 创建人
     */
    @TableField(value = "createdBy")
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private Long created;

    /**
     * 更新人
     */
    @TableField(value = "updatedBy")
    private Long updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private Long updated;

    /**
     * 0正常1已删除
     */
    @TableField(value = "isDeleted")
    private Integer isDeleted;
}
