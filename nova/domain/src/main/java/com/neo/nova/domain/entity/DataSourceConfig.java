package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 数据源配置表
 * @TableName DataSourceConfig
 */
@TableName(value ="DataSourceConfig")
@Data
public class DataSourceConfig {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenantId")
    private Long tenantId;

    /**
     * 用户ID
     */
    @TableField(value = "userId")
    private Long userId;

    /**
     * 会话ID
     */
    @TableField(value = "conversationId")
    private String conversationId;

    /**
     * 数据来源名称
     */
    @TableField(value = "dataSource")
    private String dataSource;

    /**
     * 文件名称
     */
    @TableField(value = "fileName")
    private String fileName;

    /**
     * 文件格式
     */
    @TableField(value = "fileType")
    private String fileType;

    /**
     * 文件存储路径
     */
    @TableField(value = "originFilePath")
    private String originFilePath;

    /**
     * 预处理的文件存储路径
     */
    @TableField(value = "preProcessFilePath")
    private String preProcessFilePath;

    /**
     * 处理完成的json对象
     */
    @TableField(value = "formatedInfo")
    private String formatedInfo;

    /**
     * 时间间隔类型
     */
    @TableField(value = "periodType")
    private Integer periodType;

    /**
     * 数据开始时间 2025-01-01
     */
    @TableField(value = "startDate")
    private String startDate;

    /**
     * 数据结束时间 2025-01-01
     */
    @TableField(value = "endDate")
    private String endDate;

    /**
     * 数据状态 0 处理中 1 待确认 2 已生效
     */
    @TableField(value = "dataStatus")
    private Integer dataStatus;

    /**
     * 创建人
     */
    @TableField(value = "createdBy")
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private Long created;

    /**
     * 更新人
     */
    @TableField(value = "updatedBy")
    private Long updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private Long updated;

    /**
     * 备注信息
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 0正常1已删除
     */
    @TableField(value = "isDeleted")
    private Integer isDeleted;
}
