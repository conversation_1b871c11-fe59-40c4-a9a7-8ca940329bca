package com.neo.nova.domain.entity.sqlserver;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @TableName syn_sl_pam_custdistrict
 */
@TableName(value = "syn_sl_pam_custdistrict")
@Data
public class SynSlPamCustdistrict {
    /**
     * 主键ID
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    /**
     *
     */
    @TableField(value = "CustDistrictNo")
    private String custdistrictno;

    /**
     *
     */
    @TableField(value = "CustDistrictName")
    private String custdistrictname;

    /**
     *
     */
    @TableField(value = "description")
    private String description;

    /**
     *
     */
    @TableField(value = "Ustate")
    private String ustate;

    /**
     *
     */
    @TableField(value = "Depth")
    private Integer depth;

    /**
     *
     */
    @TableField(value = "ParentID")
    private Integer parentid;

    /**
     *
     */
    @TableField(value = "OrderCode")
    private String ordercode;
}