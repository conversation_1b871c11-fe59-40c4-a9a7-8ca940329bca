package com.neo.nova.domain.enums;

import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/7/22
 **/
@Getter
public enum CustomerLevelEnum {
    LEVEL_ZERO("-", "-", 0),
    LEVEL_A("A", "A类", 1),
    LEVEL_B("B", "B类", 2),
    LEVEL_C("C", "C类", 3),
    LEVEL_D("D", "D类", 4),
    ;
    final String code;
    final String desc;
    final long id;

    CustomerLevelEnum(String code, String desc, int id) {
        this.code = code;
        this.desc = desc;
        this.id = id;
    }

    public static CustomerLevelEnum getByCode(String code) {
        for (CustomerLevelEnum value : CustomerLevelEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static CustomerLevelEnum getById(long id) {
        for (CustomerLevelEnum value : CustomerLevelEnum.values()) {
            if (value.id == id) {
                return value;
            }
        }
        return null;
    }


    public static Map<Long, String> mappingName(Set<Long> ids) {
        Map<Long, String> result = Maps.newHashMap();
        for (Long id : ids) {
            CustomerLevelEnum customerLevelEnum = getById(id);
            if (customerLevelEnum != null) {
                result.put(id, customerLevelEnum.desc);
            } else {
                result.put(id, "未知");
            }
        }
        return result;
    }

}
