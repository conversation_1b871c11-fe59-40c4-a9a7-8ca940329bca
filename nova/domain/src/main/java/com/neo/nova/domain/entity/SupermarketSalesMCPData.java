package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 商超数据收集表
 * @TableName SupermarketSalesMCPData
 */
@TableName(value ="SupermarketSalesMCPData")
@Data
public class SupermarketSalesMCPData {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 租户id
     */
    @TableField(value = "tenantId")
    private Long tenantId;

    /**
     * 用户id
     */
    @TableField(value = "userIdentity")
    private String userIdentity;

    /**
     * 会话id
     */
    @TableField(value = "conversationId")
    private String conversationId;

    /**
     * 商超名称
     */
    @TableField(value = "supermarketName")
    private String supermarketName;

    /**
     * 货品名称或填写合计
     */
    @TableField(value = "itemName")
    private String itemName;

    /**
     * 数据开始时间 2025-01-01
     */
    @TableField(value = "startDate")
    private String startDate;

    /**
     * 数据结束时间 2025-01-01
     */
    @TableField(value = "endDate")
    private String endDate;

    /**
     * 销售数量
     */
    @TableField(value = "count")
    private BigDecimal count;

    /**
     * 单位
     */
    @TableField(value = "unit")
    private String unit;

    /**
     * 销售金额（分）
     */
    @TableField(value = "amount")
    private Long amount;

    /**
     * 数据状态 0 处理中 1 已提交
     */
    @TableField(value = "dataStatus")
    private Integer dataStatus;

    /**
     * 创建人
     */
    @TableField(value = "createdBy")
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private Long created;

    /**
     * 更新人
     */
    @TableField(value = "updatedBy")
    private Long updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private Long updated;

    /**
     * 0正常1已删除
     */
    @TableField(value = "isDeleted")
    private Integer isDeleted;
}
