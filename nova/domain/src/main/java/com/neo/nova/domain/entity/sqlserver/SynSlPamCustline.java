package com.neo.nova.domain.entity.sqlserver;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @TableName syn_sl_pam_custline
 */
@TableName(value = "syn_sl_pam_custline")
@Data
public class SynSlPamCustline {
    /**
     *
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    /**
     *
     */
    @TableField(value = "CustLineNo")
    private String custlineno;

    /**
     *
     */
    @TableField(value = "CustLineName")
    private String custlinename;

    /**
     *
     */
    @TableField(value = "Ustate")
    private String ustate;
}