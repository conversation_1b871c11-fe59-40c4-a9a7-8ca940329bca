package com.neo.nova.domain.dto;

import com.neo.nova.domain.entity.WorkOrderDetail;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/*
 *             ,%%%%%%%%,
 *           ,%%/\%%%%/\%%
 *          ,%%%\c "" J/%%%
 * %.       %%%%/ o  o \%%%
 * `%%.     %%%%    _  |%%%
 *  `%%     `%%%%(__Y__)%%'
 *  //       ;%%%%`\-/%%%'
 * ((       /  `%%%%%%%'
 *  \\    .'          |
 *   \\  /       \  | |
 *    \\/         ) | |
 *     \         /_ | |__
 *     (___________))))))) 攻城狮
 *
 *
 * 用途说明:
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/10 15:25
 */
@Data
@Builder
public class WorkOrderDetailDto {
    /**
     * 主键
     */
 
    private Long id;

    /**
     * 租户id
     */

    private Long tenantId;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 执行人id
     */

    private Long executorId;

    /**
     * 动作 签到等
     */

    private String action;

    /**
     * 状态
     */

    private Integer status;

    /**
     * 子工单类型 业务字段
     */

    private Integer type;

    /**
     * 任务顺序
     */

    private Integer sort;

    /**
     * 完成时间
     */

    private LocalDateTime completeTime;

    /**
     * 主工单id
     */

    private Long workOrderId;

    /**
     * 开始时间
     */

    private LocalDateTime workOrderStartTime;

    /**
     * 工单截止时间
     */

    private LocalDateTime workOrderEndTime;

    /**
     * 动作的extra明细
     */

    private String actionDetail;

    /**
     * 创建时间
     */

    private LocalDateTime created;

    /**
     * 更新时间
     */

    private LocalDateTime updated;

    /**
     * 是否删除 0 否 1 是
     */

    private Integer deleted;


    public static WorkOrderDetailDto covertDto(WorkOrderDetail workOrderDetail) {
        if (workOrderDetail == null) {
            return null;
        }
        return WorkOrderDetailDto.builder()
                .id(workOrderDetail.getId())
                .creatorId(workOrderDetail.getCreatorId())
                .executorId(workOrderDetail.getExecutorId())
                .action(workOrderDetail.getAction())
                .status(workOrderDetail.getStatus())
                .type(workOrderDetail.getType())
                .completeTime(workOrderDetail.getCompleteTime())
                .workOrderStartTime(workOrderDetail.getWorkOrderStartTime())
                .workOrderEndTime(workOrderDetail.getWorkOrderEndTime())
                .actionDetail(workOrderDetail.getActionDetail())
                .workOrderId(workOrderDetail.getWorkOrderId())
                .sort(workOrderDetail.getSort())
                .created(workOrderDetail.getCreated())
                .updated(workOrderDetail.getUpdated()).build();
    }

}
