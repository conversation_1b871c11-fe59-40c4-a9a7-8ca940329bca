package com.neo.nova.domain.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.*;
import javax.validation.Valid;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.time.LocalDateTime;

/**
 * 订单数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderDTO implements Serializable {

    /**
     * 表ID
     */
    private Long id;

    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    /**
     * 订单编号
     */
    @NotBlank(message = "订单编号不能为空")
    @Size(max = 50, message = "订单编号长度不能超过50个字符")
    private String orderCode;

    /**
     * 客户ID
     */
    @NotNull(message = "客户ID不能为空")
    private Long customerId;

    /**
     * 客户编号
     */
    @Size(max = 50, message = "客户编号长度不能超过50个字符")
    private String customerCode;

    /**
     * 客户名称
     */
    @NotBlank(message = "客户名称不能为空")
    @Size(max = 100, message = "客户名称长度不能超过100个字符")
    private String customerName;

    /**
     * 客户类型
     */
    private Long customerCid;

    /**
     * 客户类型名称
     */
    @Size(max = 100, message = "客户类型名称长度不能超过100个字符")
    private String customerCidName;

    /**
     * 业务员ID
     */
    @NotNull(message = "业务员ID不能为空")
    private Long salesId;

    /**
     * 业务员编号
     */
    @Size(max = 50, message = "业务员编号长度不能超过50个字符")
    private String salesCode;

    /**
     * 业务员姓名
     */
    @NotBlank(message = "业务员姓名不能为空")
    @Size(max = 100, message = "业务员姓名长度不能超过100个字符")
    private String salesName;

    /**
     * 下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Long orderTime;

    /**
     * 订单金额（分）
     */
    @NotNull(message = "订单金额不能为空")
    @Min(value = 0, message = "订单金额不能为负数")
    private Long amount;

    /**
     * 订单状态（例如：0-未处理、1-已发货、2-已完成、3-已取消）
     */
    @NotNull(message = "订单状态不能为空")
    @Min(value = 0, message = "订单状态值不能为负数")
    @Max(value = 10, message = "订单状态值不能超过10")
    private Integer status;

    /**
     * 订单状态描述
     */
    private String statusDesc;

    /**
     * 付款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Integer payTime;

    /**
     * 发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Integer shipTime;

    /**
     * 取消订单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Integer cancelTime;

    /**
     * 创建人
     */
    private Integer createdBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Integer created;

    /**
     * 更新人
     */
    private Integer updatedBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Integer updated;

    /**
     * 是否删除（0-正常，1-已删除）
     */
    private Integer isDeleted;

    /**
     * 订单明细列表
     */
    @Valid
    private List<OrderDetailDTO> orderDetails;

    /**
     * 订单金额（元，用于显示）
     */
    public Double getAmountInYuan() {
        return amount != null ? amount / 100.0 : 0.0;
    }

    /**
     * 设置订单金额（元）
     */
    public void setAmountInYuan(Double amountInYuan) {
        this.amount = amountInYuan != null ? Math.round(amountInYuan * 100) : 0L;
    }

    /**
     * 计算订单总金额（基于明细的单价和数量）
     */
    public void calculateTotalAmount() {
        if (orderDetails != null && !orderDetails.isEmpty()) {
            BigDecimal total = orderDetails.stream()
                    .filter(detail -> detail.getSalePrice() != null && detail.getSalesQty() != null)
                    .map(detail -> detail.getSalePrice().multiply(detail.getSalesQty()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            this.amount = total.multiply(new BigDecimal("100")).longValue(); // 转换为分
        }
    }

    /**
     * 获取订单状态描述
     */
    public String getStatusDesc() {
        if (status == null) {
            return "未知";
        }
        return switch (status) {
            case 0 -> "未处理";
            case 1 -> "已发货";
            case 2 -> "已完成";
            case 3 -> "已取消";
            case 4 -> "已付款";
            case 5 -> "退款中";
            case 6 -> "已退款";
            default -> "未知状态";
        };
    }

    /**
     * 检查订单是否可以编辑
     */
    public boolean isEditable() {
        return status != null && (status == 0 || status == 4); // 未处理或已付款状态可编辑
    }

    /**
     * 检查订单是否可以取消
     */
    public boolean isCancelable() {
        return status != null && (status == 0 || status == 4); // 未处理或已付款状态可取消
    }

    /**
     * 检查订单是否已完成
     */
    public boolean isCompleted() {
        return status != null && status == 2;
    }

    /**
     * 检查订单是否已取消
     */
    public boolean isCanceled() {
        return status != null && status == 3;
    }
}
