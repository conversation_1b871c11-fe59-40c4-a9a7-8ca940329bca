package com.neo.nova.domain.dto;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by juan<PERSON>o
 * 复杂列
 *
 * @since 2019-06-04
 */
@Data
@Builder
public class Column {
    /**
     * 列类型
     *
     */
    private Integer columnType = 0;
    /**
     * 唯一标识
     */
    private String key;
    /**
     * 标题
     */
    private String title;
    /**
     * 是否支持排序
     */
    private boolean isSort = false;

    /**
     * 表头是否固定
     */
    private boolean isFixed = false;

    /**
     * 复杂表头
     */
    private List<Column> children;

    public Column addChild(Column column){
        if (children == null) {
            children = new ArrayList<>();
        }
        children.add(column);
        return this;
    }

}
