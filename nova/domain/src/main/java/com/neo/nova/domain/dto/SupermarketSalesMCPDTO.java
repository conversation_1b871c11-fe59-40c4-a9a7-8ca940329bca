package com.neo.nova.domain.dto;

import lombok.Data;
import org.springframework.ai.tool.annotation.ToolParam;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @since 2025/7/17
 **/
@Data
public class SupermarketSalesMCPDTO {

    /**
     * 商超名称
     */
    @ToolParam(description = "商超名称")
    private String supermarketName;

    /**
     * 货品名称或合计
     */
    @ToolParam(description = "货品名称或合计")
    private String itemName;

    /**
     * 数据开始时间 2025-01-01
     */
    @ToolParam(description = "数据开始时间")
    private String startDate;

    /**
     * 数据结束时间 2025-01-01
     */
    @ToolParam(description = "数据结束时间")
    private String endDate;

    /**
     * 销售数量
     */
    @ToolParam(description = "销售数量")
    private BigDecimal count;

    /**
     * 单位
     */
    @ToolParam(description = "单位")
    private String unit;

    /**
     * 销售金额（分）
     */
    @ToolParam(description = "销售金额（分）")
    private Long amount;

}
