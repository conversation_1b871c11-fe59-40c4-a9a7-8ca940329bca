package com.neo.nova.domain.entity.sqlserver;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @TableName syn_sl_pam_Custom
 */
@TableName(value = "syn_sl_pam_Custom")
@Data
public class SynSlPamCustom {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     *
     */
    @TableField(value = "CustNo")
    private String custno;

    /**
     *
     */
    @TableField(value = "CustName")
    private String custname;

    /**
     *
     */
    @TableField(value = "Mnemocode")
    private String mnemocode;

    /**
     *
     */
    @TableField(value = "LinkMan")
    private String linkman;

    /**
     *
     */
    @TableField(value = "ContactNumber")
    private String contactnumber;

    /**
     *
     */
    @TableField(value = "TdelivyAddress")
    private String tdelivyaddress;

    /**
     *
     */
    @TableField(value = "CustTypeID")
    private Integer custtypeid;

    /**
     *
     */
    @TableField(value = "CustLineID")
    private Integer custlineid;

    /**
     *
     */
    @TableField(value = "CustDistrictID")
    private Integer custdistrictid;

    /**
     *
     */
    @TableField(value = "saleserid")
    private Integer saleserid;

    /**
     *
     */
    @TableField(value = "UState")
    private String ustate;

    /**
     *
     */
    @TableField(value = "DispPrice")
    private String dispprice;

    /**
     *
     */
    @TableField(value = "PadPsw")
    private String padpsw;

    /**
     *
     */
    @TableField(value = "hand_sel_ids")
    private String handSelIds;

    /**
     *
     */
    @TableField(value = "CustAreaID")
    private Integer custareaid;

    /**
     *
     */
    @TableField(value = "PriceTypeID")
    private Integer pricetypeid;

    /**
     *
     */
    @TableField(value = "OpenDate")
    private Date opendate;

    /**
     *
     */
    @TableField(value = "AuditFlag")
    private String auditflag;
}