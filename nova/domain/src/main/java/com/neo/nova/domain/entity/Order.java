package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 订单基本信息表
 * @TableName `Order`
 */
@TableName(value ="`Order`")
@Data
public class Order {
    /**
     * 表ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenantId")
    private Long tenantId;

    /**
     * 订单编码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 三方订单ID
     */
    @TableField(value = "outId")
    private String outId;

    /**
     * 三方类型
     */
    @TableField(value = "outType")
    private String outType;

    /**
     * 1订单 2加单 3退货 4差错
     */
    @TableField(value = "orderType")
    private Integer orderType;

    /**
     * 客户ID
     */
    @TableField(value = "customerId")
    private Long customerId;

    /**
     * 送货日期
     */
    @TableField(value = "delivyDate")
    private Long delivyDate;

    /**
     * 0未发货/部分发货 1已发货 2结帐中 3已结账 9已删除
     */
    @TableField(value = "status")
    private Integer status;

    /**
     *
     */
    @TableField(value = "synId")
    private Long synId;

    /**
     * 打印次数
     */
    @TableField(value = "printTime")
    private Long printTime;

    /**
     * 0电话 1手机 2网络
     */
    @TableField(value = "orderFrom")
    private Integer orderFrom;

    /**
     * 下单时间
     */
    @TableField(value = "orderTime")
    private Long orderTime;

    /**
     * 订单金额 元
     */
    @TableField(value = "amount")
    private BigDecimal amount;

    /**
     * 开单人
     */
    @TableField(value = "verifyPerson")
    private String verifyPerson;

    /**
     * 开单时间
     */
    @TableField(value = "verifyDate")
    private String verifyDate;

    /**
     * 处理方式
     */
    @TableField(value = "returnProcess")
    private String returnProcess;

    /**
     * 退货原因
     */
    @TableField(value = "returnReason")
    private String returnReason;

    /**
     * 退货说明
     */
    @TableField(value = "returnNote")
    private String returnNote;

    /**
     * 创建人
     */
    @TableField(value = "createdBy")
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private Long created;

    /**
     * 更新人
     */
    @TableField(value = "updatedBy")
    private Long updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private Long updated;

    /**
     * 0正常1已删除
     */
    @TableField(value = "isDeleted")
    private Integer isDeleted;
}
