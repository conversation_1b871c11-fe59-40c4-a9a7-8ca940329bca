package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 客户别名映射表
 * @TableName CustomerAliasMapping
 */
@TableName(value = "CustomerAliasMapping")
@Data
public class CustomerAliasMapping {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenantId")
    private Long tenantId;

    /**
     * 用户ID
     */
    @TableField(value = "userId")
    private Long userId;

    /**
     * 数据源类型：通用、宏昌、国光
     */
    @TableField(value = "dataSourceType")
    private String dataSourceType;

    /**
     * 客户别名（Excel中的原始名称）
     */
    @TableField(value = "customerAlias")
    private String customerAlias;

    /**
     * 系统客户ID
     */
    @TableField(value = "customerId")
    private Long customerId;

    /**
     * 系统客户名称
     */
    @TableField(value = "customerName")
    private String customerName;

    /**
     * 创建人
     */
    @TableField(value = "createdBy")
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private Long created;

    /**
     * 更新人
     */
    @TableField(value = "updatedBy")
    private Long updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private Long updated;

    /**
     * 0正常1已删除
     */
    @TableField(value = "isDeleted")
    private Integer isDeleted;
}
