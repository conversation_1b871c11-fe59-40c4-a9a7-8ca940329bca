package com.neo.nova.domain.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/7/8
 **/
@Data
public class CustomerDTO {


    /**
     * 主键ID
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

/**
     * 三方类型
     */
    private String outType;

    /**
     * 客户区域ID
     */
    private Long customerAreaId;

    /**
     * 线路位置
     */
    private String linkPosition;


    /**
     * 客户区域名称
     */
    private String customerAreaName;
    /**
     * 内容
     */
    private String content;

    /**
     * 渠道名称
     */
    private String channelName;
    /**
     * 开通日期
     */
    private String openDate;
    /**
     * 合同编号
     */
    private String contractNumber;
    /**
     * 销售编号
     */
    private String saleNumber;
    /**
     * 客户编码
     */
    private String code;

    /**
     * 三方客户ID
     */
    private String outId;
    /**
     * 客户渠道
     */

    private String channel;

    /**
     * 客户名称
     */
    private String name;

    /**
     * 客户名称缩写
     */
    private String mnemoCode;

    /**
     * 客户联系人
     */
    private String linkMan;

    /**
     * 客户收货地址
     */
    private String deliveryAddress;

    /**
     * 客户类型id
     */
    private Long customerTypeId;

    /**
     * 客户类型名称
     */
    private String customerTypeName;

    /**
     * 客户线路id
     */
    private Long customerLineId;

    /**
     * 客户线路名称*
     */
    private String customerLineName;

    /**
     * 价格类型id
     */
    private Long priceTypeId;

    /**
     * 行政区域id
     */
    private Long adminRegionId;

    /**
     * 行政区域名称*
     */
    private String adminRegionName;

    /**
     * 客户等级
     */
    private Integer level;


    /**
     * 客户等级名称*
     */
    private String levelName;

    /**
     * 业务员Id
     */
    private Long salesId;


    /**
     * 业务员名称*
     */
    private String salesName;

    /**
     * 状态  0休眠 1使用 2停用
     */
    private Integer status;

}
