package com.neo.nova.domain.dto;


import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SalesUserDetailDTO {
    /**
     * 自增ID
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;
    /**
     * 手机号
     */
    private String userPhone;

    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 部门名称
     */

    private String deptName;
    /**
     * 领导名称
     */

    private String leaderName;

    /**
     * 客户id
     */
    private Long customerId;


    /**
     * 月完成度
     */
    private BigDecimal monthRate;

    /**
     * 年完成度
     */
    private BigDecimal yearRate;


    private List<String> customerNames;

    private Integer isDeleted;



}
