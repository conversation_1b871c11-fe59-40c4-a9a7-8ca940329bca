package com.neo.nova.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 明细数据
 *
 * <AUTHOR>
 * @since 2025/8/7
 **/
@Data
public class CustomerStatisticsDetailDTO {

    private Long tenantId;
    private Long customerId;
    private Long goodsId;
    private BigDecimal billQty;
    private BigDecimal billAmount;
    private BigDecimal deliveryQty;
    private BigDecimal deliveryAmount;
    private BigDecimal costAmount;
    private BigDecimal backAmount; // 外采商品取备用金计入成本价
    private BigDecimal salesQty; //非联营商超，发货额就是销售额
    private BigDecimal salesAmount; //非联营商超，发货额就是销售额
    private String startDate; //yyyy-MM-dd 必须同月或则同日
    private String endDate; //yyyy-MM-dd 必须同月或则同日
    private String outType; //1隔壁阿姨 2隔壁家 ，其他系统填0
    private Integer orderType; //1 订单 2加单 3退货 4差错  ，非订单明细填1
    private String returnProcess; //退货处理方式 ：联营商超&退货&就地销毁计入损耗

}
