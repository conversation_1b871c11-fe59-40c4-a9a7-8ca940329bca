package com.neo.nova.domain.enums;

import lombok.Getter;

/**
 * billstate 0未发货/部分发货 1已发货 2结帐中 3已结账 9已删除
 *
 * <AUTHOR>
 * @since 2025/7/29
 **/
@Getter
public enum OrderStatusEnum {


    CREATED(0, "未发货/部分发货"),
    SHIPPED(1, "已发货"),
    RECEIVED(2, "结帐中"),
    SETTLED(3, "已结账"),
    CLOSED(9, "已删除"),
    ;
    final int code;
    final String desc;

    OrderStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
