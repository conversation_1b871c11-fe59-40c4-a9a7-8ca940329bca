package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Excel加工后数据表 - 存储匹配后的数据
 * @TableName ExcelDataProcessed
 */
@TableName(value = "ExcelDataProcessed")
@Data
public class ExcelDataProcessed {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenantId")
    private Long tenantId;

    /**
     * 数据源配置ID
     */
    @TableField(value = "dataSourceConfigId")
    private Long dataSourceConfigId;


    /**
     * 数据开始时间 2025-01-01
     */
    @TableField(value = "startDate")
    private String startDate;

    /**
     * 数据结束时间 2025-01-01
     */
    @TableField(value = "endDate")
    private String endDate;

    /**
     * 原始客户名称
     */
    @TableField(value = "customerNameOriginal")
    private String customerNameOriginal;

    /**
     * 匹配的客户ID
     */
    @TableField(value = "customerId")
    private Long customerId;

    /**
     * 匹配的客户名称
     */
    @TableField(value = "customerName")
    private String customerName;

    /**
     * 客户编号
     */
    @TableField(value = "customerCode")
    private String customerCode;

    /**
     * 原始货品名称
     */
    @TableField(value = "itemNameOriginal")
    private String itemNameOriginal;

    /**
     * 匹配的货品ID
     */
    @TableField(value = "goodsId")
    private Long goodsId;

    /**
     * 匹配的货品名称
     */
    @TableField(value = "goodsName")
    private String goodsName;

    /**
     * 规格
     */
    @TableField(value = "specification")
    private String specification;

    /**
     * 单位
     */
    @TableField(value = "unit")
    private String unit;

    /**
     * 单价 元
     */
    @TableField(value = "price")
    private BigDecimal price;

    /**
     * 订货数量
     */
    @TableField(value = "billQty")
    private BigDecimal billQty;

    /**
     * 订货金额 元
     */
    @TableField(value = "billAmount")
    private BigDecimal billAmount;

    /**
     * 发货数量
     */
    @TableField(value = "deliveryQty")
    private BigDecimal deliveryQty;

    /**
     * 发货金额 元
     */
    @TableField(value = "deliveryAmount")
    private BigDecimal deliveryAmount;

    /**
     * 成本单价 元
     */
    @TableField(value = "costPrice")
    private BigDecimal costPrice;

    /**
     * 成本金额 元
     */
    @TableField(value = "costAmount")
    private BigDecimal costAmount;


    /**
     * 备用金额 元
     */
    @TableField(value = "backPrice")
    private BigDecimal backPrice;

    /**
     * 备用金额 元
     */
    @TableField(value = "backAmount")
    private BigDecimal backAmount;

    /**
     * 销售规格
     */
    @TableField(value = "salesSpecification")
    private String salesSpecification;

    /**
     * 销售单位
     */
    @TableField(value = "salesUnit")
    private String salesUnit;

    /**
     * 销售数量
     */
    @TableField(value = "salesQty")
    private BigDecimal salesQty;

    /**
     * 销售单价 元
     */
    @TableField(value = "salesPrice")
    private BigDecimal salesPrice;

    /**
     * 销售金额 元
     */
    @TableField(value = "salesAmount")
    private BigDecimal salesAmount;

    /**
     * 确认状态 0-待确认 1-已确认
     */
    @TableField(value = "confirmStatus")
    private Integer confirmStatus;

    /**
     * 匹配状态 0-未匹配 1-匹配成功 2-匹配失败
     */
    @TableField(value = "matchStatus")
    private Integer matchStatus;

    /**
     * 额外
     */
    @TableField(value = "extendField1")
    private String extendField1;

    /**
     * 额外
     */
    @TableField(value = "extendField2")
    private String extendField2;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private Long created;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private Long updated;

    /**
     * 0正常1已删除
     */
    @TableField(value = "isDeleted")
    private Integer isDeleted;
}
