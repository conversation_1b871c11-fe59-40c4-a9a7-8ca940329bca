package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 业绩计划表
 * @TableName PerformancePlan
 */
@TableName(value ="PerformancePlan")
@Data
public class PerformancePlan {
    /**
     * 目标ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenantId")
    private Long tenantId;

    /**
     * 周期类型 0年度1半年2季度3双月4月9自定义
     */
    @TableField(value = "periodType")
    private Integer periodType;

    /**
     * 目标开始日期
     */
    @TableField(value = "periodStart")
    private String periodStart;

    /**
     * 目标结束日期
     */
    @TableField(value = "periodEnd")
    private String periodEnd;

    /**
     * 创建人
     */
    @TableField(value = "createdBy")
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private Long created;

    /**
     * 更新人
     */
    @TableField(value = "updatedBy")
    private Long updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private Long updated;

    /**
     * 0正常1已删除
     */
    @TableField(value = "isDeleted")
    private Integer isDeleted;
}
