package com.neo.nova.domain.dto;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.neo.nova.domain.enums.PeriodTypeEnum;
import lombok.Builder;
import lombok.Data;
import org.apache.poi.ss.usermodel.DataFormatter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * Created by juanmao
 */
@Builder
@Data
public class TimeCondition {
    /**
     * com.neo.nova.domain.enums.PeriodTypeEnum
     */
    private Integer periodType;
    /**
     * 开始时间 like 2025-06-12
     */
    private String startDate;
    /**
     * 结束时间 like 2025-06-12
     */
    private String endDate;

    public TimeCondition() {
    }

    public TimeCondition(Integer periodType, String startDate, String endDate) {
        this.periodType = periodType;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    public int tableType() {
        if (periodType == null) {
            return -1;
        }
        if (periodType == PeriodTypeEnum.MONTH.getCode()
                || periodType == PeriodTypeEnum.DOUBLE_MONTH.getCode()
                || periodType == PeriodTypeEnum.QUARTER.getCode()
                || periodType == PeriodTypeEnum.HALF_YEAR.getCode()
                || periodType == PeriodTypeEnum.YEAR.getCode()) { // 月度数据
            return 1;
        } else if (periodType == PeriodTypeEnum.CUSTOM_DAY.getCode()) { // 日度数据
            return 2;
        }
        return -1;
    }

    public TimeCondition initCurrent(Integer periodType) {
        if (periodType == null) {
            return this;
        }
        this.periodType = periodType;
        LocalDateTime now = LocalDateTimeUtil.now();
        if (periodType == PeriodTypeEnum.YEAR.getCode()) {
            this.startDate = now.getYear() + "-01";
            this.endDate = now.getYear() + "-12";
        } else if (periodType == PeriodTypeEnum.HALF_YEAR.getCode()) {
            if (now.getMonthValue() < 7) {
                this.startDate = now.getYear() + "-01";
                this.endDate = now.getYear() + "-06";
            } else {
                this.startDate = now.getYear() + "-07";
                this.endDate = now.getYear() + "-12";
            }
        } else if (periodType == PeriodTypeEnum.QUARTER.getCode()) {
            if (now.getMonthValue() < 4) {
                this.startDate = now.getYear() + "-01";
                this.endDate = now.getYear() + "-03";
            } else if (now.getMonthValue() < 7) {
                this.startDate = now.getYear() + "-04";
                this.endDate = now.getYear() + "-06";
            } else if (now.getMonthValue() < 10) {
                this.startDate = now.getYear() + "-07";
                this.endDate = now.getYear() + "-09";
            } else {
                this.startDate = now.getYear() + "-10";
                this.endDate = now.getYear() + "-12";
            }
        } else if (periodType == PeriodTypeEnum.DOUBLE_MONTH.getCode()) {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
            if (now.getMonthValue() % 2 == 0) {
                this.startDate = now.minusMonths(1).format(dateTimeFormatter);
                this.endDate = now.format(dateTimeFormatter);
            } else {
                this.startDate = now.format(dateTimeFormatter);
                this.endDate = now.plusMonths(1).format(dateTimeFormatter);
            }
        } else if (periodType == PeriodTypeEnum.MONTH.getCode()) {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
            this.startDate = now.format(dateTimeFormatter);
            this.endDate = now.format(dateTimeFormatter);
        } else if (periodType == PeriodTypeEnum.CUSTOM_DAY.getCode()) {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            this.startDate = now.format(dateTimeFormatter);
            this.endDate = now.format(dateTimeFormatter);
        }
        return this;
    }


    public TimeCondition initMom() {
        if (periodType == null || startDate == null || endDate == null) {
            return this;
        }
        int tableType = tableType();
        long interval = _getInterval();
        if (tableType == 1) {
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(startDate, "yyyy-MM");
            LocalDateTime endDateTime = LocalDateTimeUtil.parse(endDate, "yyyy-MM");
            this.startDate = LocalDateTimeUtil.format(startDateTime.minusMonths(interval), "yyyy-MM");
            this.endDate = LocalDateTimeUtil.format(endDateTime.minusMonths(interval), "yyyy-MM");
        } else if (tableType == 2) {
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(startDate, "yyyy-MM-dd");
            LocalDateTime endDateTime = LocalDateTimeUtil.parse(endDate, "yyyy-MM-dd");
            this.startDate = LocalDateTimeUtil.format(startDateTime.minusDays(interval), "yyyy-MM-dd");
            this.endDate = LocalDateTimeUtil.format(endDateTime.minusDays(interval), "yyyy-MM-dd");
        }
        return this;
    }

    public TimeCondition copy() {
        TimeCondition timeCondition = new TimeCondition();
        timeCondition.setPeriodType(this.periodType);
        timeCondition.setStartDate(this.startDate);
        timeCondition.setEndDate(this.endDate);
        return timeCondition;
    }

    public TimeCondition initMomTrend() {
        if (periodType == null || startDate == null || endDate == null) {
            return this;
        }
        int tableType = tableType();
        if (tableType == 1) {
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(startDate, "yyyy-MM");
            this.startDate = LocalDateTimeUtil.format(startDateTime.minusMonths(1), "yyyy-MM");
            this.endDate = LocalDateTimeUtil.format(startDateTime.minusMonths(1), "yyyy-MM");
        } else if (tableType == 2) {
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(startDate, "yyyy-MM-dd");
            this.startDate = LocalDateTimeUtil.format(startDateTime.minusDays(1), "yyyy-MM-dd");
            this.endDate = LocalDateTimeUtil.format(startDateTime.minusDays(1), "yyyy-MM-dd");
        }
        return this;
    }


    public TimeCondition initYoy() {
        if (periodType == null || startDate == null || endDate == null) {
            return this;
        }
        int tableType = tableType();
        if (tableType == 1) {
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(startDate, "yyyy-MM");
            LocalDateTime endDateTime = LocalDateTimeUtil.parse(endDate, "yyyy-MM");
            this.startDate = LocalDateTimeUtil.format(startDateTime.minusYears(1), "yyyy-MM");
            this.endDate = LocalDateTimeUtil.format(endDateTime.minusYears(1), "yyyy-MM");
        } else if (tableType == 2) {
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(startDate, "yyyy-MM-dd");
            LocalDateTime endDateTime = LocalDateTimeUtil.parse(endDate, "yyyy-MM-dd");
            this.startDate = LocalDateTimeUtil.format(startDateTime.minusYears(1), "yyyy-MM-dd");
            this.endDate = LocalDateTimeUtil.format(endDateTime.minusYears(1), "yyyy-MM-dd");
        }
        return this;
    }

    // 根据当前时间和timeCondition获取环比时间
    public static String getMomData(String current, Integer periodType) {
        if (PeriodTypeEnum.CUSTOM_DAY.getCode() == periodType) {
            LocalDateTime currentDateTime = LocalDateTimeUtil.parse(current, "yyyy-MM-dd");
            return LocalDateTimeUtil.format(currentDateTime.minusDays(1), "yyyy-MM-dd");
        } else {
            LocalDateTime currentDateTime = LocalDateTimeUtil.parse(current, "yyyy-MM");
            return LocalDateTimeUtil.format(currentDateTime.minusMonths(1), "yyyy-MM");
        }
    }

    // 根据当前时间和timeCondition获取同比时间
    public static String getYoyData(String current, Integer periodType) {
        if (PeriodTypeEnum.CUSTOM_DAY.getCode() == periodType) {
            LocalDateTime currentDateTime = LocalDateTimeUtil.parse(current, "yyyy-MM-dd");
            return LocalDateTimeUtil.format(currentDateTime.minusYears(1), "yyyy-MM-dd");
        } else {
            LocalDateTime currentDateTime = LocalDateTimeUtil.parse(current, "yyyy-MM");
            return LocalDateTimeUtil.format(currentDateTime.minusYears(1), "yyyy-MM");
        }
    }

    private long _getInterval() {
        int tableType = tableType();
        if (tableType == 1) {
            //月
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(startDate, "yyyy-MM");
            LocalDateTime endDateTime = LocalDateTimeUtil.parse(endDate, "yyyy-MM");
            return ChronoUnit.MONTHS.between(startDateTime, endDateTime) + 1;
        } else if (tableType == 2) {
            LocalDateTime startDateTime = LocalDateTimeUtil.parse(startDate, "yyyy-MM-dd");
            LocalDateTime endDateTime = LocalDateTimeUtil.parse(endDate, "yyyy-MM-dd");
            return ChronoUnit.DAYS.between(startDateTime, endDateTime) + 1;
        }
        return 0;
    }

    public static void main(String[] args) {

        System.out.println(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

        LocalDate date = YearMonth.parse("2025-08", formatter).atDay(1);
        System.out.println(date);


        // 字符串转LocalDate
//        LocalDateTime date = LocalDate.parse("2025-08", formatter);

        TimeCondition timeCondition = new TimeCondition();
        timeCondition.initCurrent(0);
        System.out.println(timeCondition);
        timeCondition.initMom();
        System.out.println(timeCondition);
        timeCondition.initCurrent(1);
        System.out.println(timeCondition);
        timeCondition.initMom();
        System.out.println(timeCondition);
        timeCondition.initCurrent(2);
        System.out.println(timeCondition);
        timeCondition.initMom();
        System.out.println(timeCondition);
        timeCondition.initCurrent(3);
        System.out.println(timeCondition);
        timeCondition.initMom();
        System.out.println(timeCondition);
        timeCondition.initCurrent(4);
        System.out.println(timeCondition);
        timeCondition.initMom();
        System.out.println(timeCondition);
        timeCondition.initCurrent(9);
        System.out.println(timeCondition);
        timeCondition.initMom();
        System.out.println(timeCondition);


        timeCondition.initCurrent(0);
        System.out.println(timeCondition);
        timeCondition.initYoy();
        System.out.println(timeCondition);
        timeCondition.initCurrent(1);
        System.out.println(timeCondition);
        timeCondition.initYoy();
        System.out.println(timeCondition);
        timeCondition.initCurrent(2);
        System.out.println(timeCondition);
        timeCondition.initYoy();
        System.out.println(timeCondition);
        timeCondition.initCurrent(3);
        System.out.println(timeCondition);
        timeCondition.initYoy();
        System.out.println(timeCondition);
        timeCondition.initCurrent(4);
        System.out.println(timeCondition);
        timeCondition.initYoy();
        System.out.println(timeCondition);
        timeCondition.initCurrent(9);
        System.out.println(timeCondition);
        timeCondition.initYoy();
        System.out.println(timeCondition);


    }

    @Override
    public String toString() {
        return "TimeCondition{" +
                "periodType=" + periodType +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                '}';
    }
}
