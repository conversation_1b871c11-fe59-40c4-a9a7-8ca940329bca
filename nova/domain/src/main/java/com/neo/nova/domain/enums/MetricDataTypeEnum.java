package com.neo.nova.domain.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/6/30
 **/
@Getter
public enum MetricDataTypeEnum {
    UNDEFINED(0, "自定义", "", ""),
    GMV(1, "销售额", "元", "#,##0.##"),
    ;
    final int code;
    final String name;
    final String unit;
    final String format;

    MetricDataTypeEnum(int code, String name, String unit, String format) {
        this.code = code;
        this.name = name;
        this.unit = unit;
        this.format = format;
    }

    public static String getNameByCode(Integer code) {
        for (MetricDataTypeEnum metricDataTypeEnum : MetricDataTypeEnum.values()) {
            if (metricDataTypeEnum.code == code) {
                return metricDataTypeEnum.name;
            }
        }
        return null;
    }

    public static boolean isGMV(Integer code) {
        return code != null && MetricDataTypeEnum.GMV.getCode() == code;
    }
}
