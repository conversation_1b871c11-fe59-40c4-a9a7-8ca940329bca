package com.neo.nova.domain.enums;

import lombok.Getter;

import java.util.Comparator;

/**
 * <AUTHOR>
 * @since 2025/7/3
 **/
@Getter
public enum MetricCodeEnum {

    //直接指标，不带后缀默认使用ID

    // 区域类
    MARKET("C_MARKET", "市场", MetricCodeTypeEnum.DIRECT_METRIC.getCode(), "区域类", 1),
    CHANNEL("C_CHANNEL", "渠道", MetricCodeTypeEnum.COMPLEX_METRIC.getCode(), "区域类", 2),
    AREA("C_AREA", "区域", MetricCodeTypeEnum.COMPLEX_METRIC.getCode(), "区域类", 3),
    CUSTOMER_ADMIN_REGION("D_CUSTOMER_ADMIN_REGION", "行政区域", MetricCodeTypeEnum.DIRECT_METRIC.getCode(), "区域类", 4),
    CUSTOMER_SALES_REGION("D_CUSTOMER_SALES_REGION", "销售区域", MetricCodeTypeEnum.DIRECT_METRIC.getCode(), "区域类", 5),

    // 客户类
    CUSTOMER("D_CUSTOMER", "客户", MetricCodeTypeEnum.DIRECT_METRIC.getCode(), "客户类", 14),
    CUSTOMER_TYPE("D_CUSTOMER_TYPE", "客户类型", MetricCodeTypeEnum.DIRECT_METRIC.getCode(), "客户类", 11),
    CUSTOMER_LEVEL("D_CUSTOMER_LEVEL", "客户等级", MetricCodeTypeEnum.DIRECT_METRIC.getCode(), "客户类", 12),
    CUSTOMER_NAME("D_CUSTOMER_NAME", "客户名称", MetricCodeTypeEnum.DIRECT_METRIC.getCode(), "客户类", 13),


    // 产品类
    PRODUCT("D_PRODUCT", "产品", MetricCodeTypeEnum.DIRECT_METRIC.getCode(), "产品类", 24),
    PRODUCT_SOURCE("D_PRODUCT_SOURCE", "产品来源", MetricCodeTypeEnum.DIRECT_METRIC.getCode(), "产品类", 22),
    PRODUCT_TYPE("D_PRODUCT_TYPE", "产品类型", MetricCodeTypeEnum.DIRECT_METRIC.getCode(), "产品类", 21),
    PRODUCT_NAME("D_PRODUCT_NAME", "产品名称", MetricCodeTypeEnum.DIRECT_METRIC.getCode(), "产品类", 23),

    // 人员类
    DEPARTMENT("D_DEPARTMENT", "部门", MetricCodeTypeEnum.DIRECT_METRIC.getCode(), "人员类", 31),
    USER("D_USER", "人员", MetricCodeTypeEnum.DIRECT_METRIC.getCode(), "人员类", 32),
    CUSTOMER_OWNER("D_CUSTOMER_OWNER", "负责人", MetricCodeTypeEnum.DIRECT_METRIC.getCode(), "人员类", 33),

    //代码里要特判的
    ALLCOMPANY("S_ALLCOMPANY", "全公司", MetricCodeTypeEnum.SPECIAL_METRIC.getCode(), "人员类", 34),


    ;
    final String code;
    final String name;
    final int codeType;
    final String sort;
    final int order;

    MetricCodeEnum(String code, String name, int codeType, String sort, int order) {
        this.code = code;
        this.name = name;
        this.codeType = codeType;
        this.sort = sort;
        this.order = order;
    }

    public static String metricCodeMapping(String metricCode) {
        return switch (metricCode) {
            // 区域类
            case "C_MARKET" -> "marketId";
            case "C_CHANNEL" -> "channelId";
            case "C_AREA" -> "supermarketAreaId";
            case "D_CUSTOMER_SALES_REGION" -> "salesRegionId";
            case "D_CUSTOMER_ADMIN_REGION" -> "adminRegionId";

            // 客户类
            case "D_CUSTOMER" -> "customerId";
            case "D_CUSTOMER_TYPE" -> "customerTypeId";
            case "D_CUSTOMER_LEVEL" -> "customerLevel";
            case "D_CUSTOMER_NAME" -> "customerName";
            case "D_CUSTOMER_OWNER" -> "salesId";

            // 产品类
            case "D_PRODUCT" -> "produceId";
            case "D_PRODUCT_SOURCE" -> "produceOemFlag";
            case "D_PRODUCT_TYPE" -> "produceTypeId";
            case "D_PRODUCT_NAME" -> "produceName";

            // 人员类
            case "D_DEPARTMENT" -> "departId";
            case "D_USER" -> "salesId";

            // 保留原有的其他字段
            default -> metricCode;
        };
    }


    public static String tableNameMapping(String metricCode) {
        return switch (metricCode) {
            // 区域类
            case "C_MARKET" -> "marketName";
            case "C_CHANNEL" -> "channelName";
            case "C_AREA" -> "supermarketAreaName";
            case "D_CUSTOMER_SALES_REGION" -> "salesRegionName";
            case "D_CUSTOMER_ADMIN_REGION" -> "adminRegionName";

            // 客户类
            case "D_CUSTOMER" -> "customerId";
            case "D_CUSTOMER_TYPE" -> "customerTypeName";
            case "D_CUSTOMER_LEVEL" -> "customerLevelName";
            case "D_CUSTOMER_NAME" -> "customerName";
            case "D_CUSTOMER_OWNER" -> "salesName";

            // 产品类
            case "D_PRODUCT" -> "produceId";
            case "D_PRODUCT_SOURCE" -> "produceSourceName";
            case "D_PRODUCT_TYPE" -> "produceTypeName";
            case "D_PRODUCT_NAME" -> "produceName";

            // 人员类
            case "D_DEPARTMENT" -> "departName";
            case "D_USER" -> "salesName";

            // 保留原有的其他字段
            default -> metricCode;
        };
    }

    //反向tableName
    public static String tableNameMappingDataColumn(String tableName) {
        return switch (tableName) {
            // 区域类
            case "marketName" -> "marketId";
            case "channelName" -> "channelId";
            case "supermarketAreaName" -> "supermarketAreaId";
            case "salesRegionName" -> "salesRegionId";
            case "adminRegionName" -> "adminRegionId";

            // 客户类
            case "customerId" -> "customerId";
            case "customerTypeName" -> "customerTypeId";
            case "customerLevelName" -> "customerLevelId";
            case "customerName" -> "customerName";

            // 产品类
            case "produceId" -> "produceId";
            case "produceSourceName" -> "produceOemFlag";
            case "produceTypeName" -> "produceTypeId";
            case "produceName" -> "produceName";

            // 人员类
            case "departName" -> "departId";
            case "salesName" -> "salesId";

            // 保留原有的其他字段
            default -> tableName;
        };
    }


    public static String getNameByCode(String code) {
        for (MetricCodeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }
        return null;
    }

    public static MetricCodeEnum queryMetric(String code) {
        for (MetricCodeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isComplexMetric(String metricCode) {
        MetricCodeEnum metric = MetricCodeEnum.queryMetric(metricCode);
        return metric != null && metric.getCodeType() == MetricCodeTypeEnum.COMPLEX_METRIC.getCode();
    }


    public static int comparator(String metricCode1, String metricCode2) {
        MetricCodeEnum metric1 = MetricCodeEnum.queryMetric(metricCode1);
        MetricCodeEnum metric2 = MetricCodeEnum.queryMetric(metricCode2);
        if (metric1 == null && metric2 == null) {
            return 0;
        }
        if (metric1 == null) {
            return 1;
        }
        if (metric2 == null) {
            return -1;
        }
        return metric1.getOrder() - metric2.getOrder();
    }
}
