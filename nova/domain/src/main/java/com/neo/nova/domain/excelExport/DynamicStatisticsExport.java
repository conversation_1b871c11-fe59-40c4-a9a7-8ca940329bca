package com.neo.nova.domain.excelExport;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 动态销售统计导出类
 * 支持根据查询条件动态调整Excel列结构
 */
@Data
public class DynamicStatisticsExport {

    @ExcelProperty(value = "序号", index = 0)
    private Long id;

    @ExcelProperty(value = "统计日期", index = 1)
    private String visitDate;

    @ExcelProperty(value = "销售额", index = 2)
    private Long amount;

    // 动态列数据，在运行时根据查询条件确定
    @ExcelIgnore
    private Map<String, Object> dynamicColumns;

    // 以下字段根据查询条件动态显示
    @ExcelIgnore
    private String produceName;

    @ExcelIgnore
    private String produceCode;

    @ExcelIgnore
    private String customerName;

    @ExcelIgnore
    private String customerCode;

    @ExcelIgnore
    private Integer customerLevel;

    @ExcelIgnore
    private String customerType;

    @ExcelIgnore
    private String salesName;

    @ExcelIgnore
    private String salesCode;

    @ExcelIgnore
    private String channel;

    @ExcelIgnore
    private String supermarketArea;

    @ExcelIgnore
    private String salesRegion;

    @ExcelIgnore
    private String adminRegion;

    /**
     * 获取动态列的值
     */
    public Object getDynamicColumnValue(String columnName) {
        if (dynamicColumns == null) {
            return null;
        }
        return dynamicColumns.get(columnName);
    }

    /**
     * 设置动态列的值
     */
    public void setDynamicColumnValue(String columnName, Object value) {
        if (dynamicColumns == null) {
            dynamicColumns = new java.util.HashMap<>();
        }
        dynamicColumns.put(columnName, value);
    }
}
