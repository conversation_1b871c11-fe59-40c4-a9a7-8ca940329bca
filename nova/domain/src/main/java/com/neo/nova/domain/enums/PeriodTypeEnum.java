package com.neo.nova.domain.enums;

import cn.hutool.core.date.LocalDateTimeUtil;
import lombok.Getter;

import java.time.ZoneId;

/**
 * <AUTHOR>
 * @since 2025/6/30
 **/
@Getter
public enum PeriodTypeEnum {
    YEAR(0, "年度"),
    HALF_YEAR(1, "半年"),
    QUARTER(2, "季"),
    DOUBLE_MONTH(3, "双月"),
    MONTH(4, "月"),
    CUSTOM_DAY(9, "自定义日");;
    final int code;
    final String desc;

    PeriodTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Long convertStartTime(Integer periodType, String timeStr) {
        if (CUSTOM_DAY.getCode() == periodType) {
            return LocalDateTimeUtil.parse(timeStr, "yyyy-MM-dd").atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000;
        } else {
            return LocalDateTimeUtil.parse(timeStr, "yyyy-MM").atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000;
        }
    }

    public static Long convertEndTime(Integer periodType, String timeStr) {
        if (CUSTOM_DAY.getCode() == periodType) {
            return LocalDateTimeUtil.parse(timeStr, "yyyy-MM-dd").plusDays(1).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000 -1;
        } else {
            return LocalDateTimeUtil.parse(timeStr, "yyyy-MM").plusMonths(1).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000 -1;
        }
    }
}
