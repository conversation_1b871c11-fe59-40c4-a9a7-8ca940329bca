package com.neo.nova.domain.enums;

import lombok.Getter;

/**
 * billtype  1订单 2加单 3退货 4差错
 * <AUTHOR>
 * @since 2025/7/29
 **/
@Getter
public enum OrderTypeEnum {

    NORMAL_ORDER(1, "订单"),
    ADD_ORDER(2, "加单"),
    REFUND_ORDER(3, "退货"),
    FIX_ORDER(4, "差错"),
    ;
    final int code;
    final String desc;

    OrderTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
