package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 指标表
 * @TableName Metric
 */
@TableName(value ="Metric")
@Data
public class Metric {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenantId")
    private Long tenantId;

    /**
     * 指标标识
     */
    @TableField(value = "metricCode")
    private String metricCode;

    /**
     * 指标唯一id,用于业务写死调用
     */
    @TableField(value = "metricCodeId")
    private String metricCodeId;

    /**
     * 指标名称
     */
    @TableField(value = "metricName")
    private String metricName;

    /**
     * 指标计算方式 0同类or不同类and
     */
    @TableField(value = "calcType")
    private Integer calcType;

    /**
     * 指标描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 创建人
     */
    @TableField(value = "createdBy")
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private Long created;

    /**
     * 更新人
     */
    @TableField(value = "updatedBy")
    private Long updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private Long updated;

    /**
     * 0正常1已删除
     */
    @TableField(value = "isDeleted")
    private Integer isDeleted;
}
