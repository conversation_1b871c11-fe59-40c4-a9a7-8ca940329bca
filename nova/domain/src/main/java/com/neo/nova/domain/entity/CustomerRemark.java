package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("CustomerRemark")
public class CustomerRemark {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 跟进类型
     */
    @TableField(value = "type")
    private Integer type;


    @TableField(value = "tenantId")
    private Long tenantId;

    /**
     * 跟进记录内容
     */
    @TableField(value = "content")
    private String content;

    @TableField(value = "followUpTime")
    private LocalDateTime followUpTime;

    /**
     * 关联记录ID
     */
    @TableField(value = "recordId")
    private Long recordId;


    @TableField(value = "customerId")
    private Long customerId;

    /**
     * 是否删除（0=未删除，1=已删除）
     */
    @TableField(value = "isDeleted")
    private Boolean isDeleted;

    @TableField(value = " creatorId", fill = FieldFill.INSERT)
    private Long creatorId;
    /**
     * 创建时间
     */
    @TableField(value = "createTime", fill = FieldFill.INSERT)
    private LocalDateTime createTime;


    /**
     * 更新时间
     */
    @TableField(value = "updateTime", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}