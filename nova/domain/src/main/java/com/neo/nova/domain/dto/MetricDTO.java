package com.neo.nova.domain.dto;

import com.neo.nova.domain.enums.MetricCodeIdEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/7/3
 **/
@Data
public class MetricDTO {

    private String id;

    private String metricCode;

    private String metricCodeId;

    private String metricName;

    private String description;

    public MetricDTO() {
    }


    public MetricDTO(MetricCodeIdEnum metricCodeIdEnum) {
        this.id = metricCodeIdEnum.getCode();
        this.metricCode = metricCodeIdEnum.getParentCode().getCode();
        this.metricCodeId = metricCodeIdEnum.getCode();
        this.metricName = metricCodeIdEnum.getName();
    }

}
