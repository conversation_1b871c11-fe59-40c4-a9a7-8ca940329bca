package com.neo.nova.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class SalesUserDTO {
    /**
     * 自增ID
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;
    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门名称
     */

    private String deptName;
    /**
     * 客户名称
     */

    private List<String> customerNames;

    /**
     * 月完成度
     */
    private BigDecimal monthRate;

    /**
     * 年完成度
     */
    private BigDecimal yearRate;

    /**
     * 0在职,1离职
     */
    private Integer isDeleted;
}
