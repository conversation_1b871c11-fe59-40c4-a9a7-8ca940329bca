package com.neo.nova.domain.entity.sqlserver;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @TableName syn_sl_pam_pricetype
 */
@TableName(value = "syn_sl_pam_pricetype")
@Data
public class SynSlPamPricetype {
    /**
     *
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    /**
     *
     */
    @TableField(value = "PriceTypeNo")
    private String pricetypeno;

    /**
     *
     */
    @TableField(value = "PriceTypeName")
    private String pricetypename;

    /**
     *
     */
    @TableField(value = "UState")
    private String ustate;
}