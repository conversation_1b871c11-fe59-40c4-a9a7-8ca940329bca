package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工单主表
 *
 * @TableName WorkOrder
 */
@TableName(value = "WorkOrder")
@Data
public class WorkOrder implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField(value = "tenantId")
    private Long tenantId;

    /**
     * 创建人id
     */
    @TableField(value = "creatorId")
    private Long creatorId;

    /**
     * 执行人id
     */
    @TableField(value = "executorId")
    private Long executorId;

    /**
     * 更新人
     */
    @TableField(value = "updaterId")
    private Long updaterId;

    /**
     * 工单标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 任务描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 状态
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 工单 优先级  一般  紧急
     */
    @TableField(value = "priority")
    private Integer priority;

    /**
     * 工单类型 主工单类型  ActionTypeEnum
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 工单开始时间
     */
    @TableField(value = "workOrderStartTime")
    private LocalDateTime workOrderStartTime;

    /**
     * 结束时间
     */
    @TableField(value = "workOrderEndTime")
    private LocalDateTime workOrderEndTime;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private LocalDateTime created;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private LocalDateTime updated;

    /**
     * 是否删除 0 否 1 是
     */
    @TableField(value = "deleted")
    private Integer deleted;
    /**
     * 工单类型 子工单类型
     */
    @TableField(value = "subType")
    private Integer subType;
    /**
     * 业务参数
     */
    @TableField(value = "bizId")
    private String bizId;

    /**
     * 正常 异常
     */
    @TableField(value = "normal")
    private Integer normal;

    /**
     * 未读 已读
     */
    @TableField(value = "`read`")
    private Integer read;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}