package com.neo.nova.domain.dto;

import lombok.Data;

import java.time.LocalDateTime;
@Data
public class CustomerRemarkDTO {
    /**
     * 主键ID
     */
    private Long id;

    private Long creatorId;

    private LocalDateTime followUpTime;

    /**
     * 跟进类型
     */
    private Integer type;

    /**
     * 跟进记录内容
     */
    private String content;

    /**
     * 关联记录ID
     */
    private Long recordId;


    private Long customerId;

    /**
     * 是否删除（0=未删除，1=已删除）
     */
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
