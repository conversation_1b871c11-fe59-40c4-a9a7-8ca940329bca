package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工单明细
 *
 * @TableName WorkOrderDetail
 */
@TableName(value = "WorkOrderDetail")
@Data
public class WorkOrderDetail implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField(value = "tenantId")
    private Long tenantId;

    /**
     * 创建人id
     */
    @TableField(value = "creatorId")
    private Long creatorId;

    /**
     * 执行人id
     */
    @TableField(value = "executorId")
    private Long executorId;

    /**
     * 动作 签到等
     */
    @TableField(value = "action")
    private String action;

    /**
     * 状态
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 子工单类型 业务字段
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 任务顺序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 完成时间
     */
    @TableField(value = "completeTime")
    private LocalDateTime completeTime;

    /**
     * 主工单id
     */
    @TableField(value = "workOrderId")
    private Long workOrderId;

    /**
     * 开始时间
     */
    @TableField(value = "workOrderStartTime")
    private LocalDateTime workOrderStartTime;

    /**
     * 工单截止时间
     */
    @TableField(value = "workOrderEndTime")
    private LocalDateTime workOrderEndTime;

    /**
     * 动作的extra明细
     */
    @TableField(value = "actionDetail")
    private String actionDetail;

    @TableField(value = "bizId")
    private String bizId;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private LocalDateTime created;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private LocalDateTime updated;

    /**
     * 是否删除 0 否 1 是
     */
    @TableField(value = "deleted")
    private Integer deleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}