package com.neo.nova.domain.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单明细数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderDetailDTO implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 商品ID
     */
    @NotNull(message = "商品ID不能为空")
    private Integer goodsId;

    /**
     * 商品名称（冗余字段，便于显示）
     */
    private String goodsName;

    /**
     * 商品编码（冗余字段，便于显示）
     */
    private String goodsCode;

    /**
     * 销售单价
     */
    @NotNull(message = "销售单价不能为空")
    @DecimalMin(value = "0.00", message = "销售单价不能为负数")
    private BigDecimal salePrice;

    /**
     * 销售数量
     */
    @NotNull(message = "销售数量不能为空")
    @DecimalMin(value = "0.01", message = "销售数量必须大于0")
    private BigDecimal salesQty;

    /**
     * 发货数量
     */
    @DecimalMin(value = "0.00", message = "发货数量不能为负数")
    private BigDecimal delivyQty;

    /**
     * 单据金额（销售单价 * 销售数量）
     */
    private BigDecimal billMoney;

    /**
     * 发货金额（销售单价 * 发货数量）
     */
    private BigDecimal delivMoney;

    /**
     * 销售单据ID
     */
    private Integer billId;

    /**
     * 计算单据金额（仅用于显示，不设置billMoney字段，因为它是数据库生成列）
     */
    public BigDecimal calculateBillMoney() {
        if (salePrice != null && salesQty != null) {
            return salePrice.multiply(salesQty);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 计算发货金额
     */
    public void calculateDelivMoney() {
        if (salePrice != null && delivyQty != null) {
            this.delivMoney = salePrice.multiply(delivyQty);
        }
    }

    /**
     * 获取未发货数量
     */
    public BigDecimal getUndeliveredQty() {
        if (salesQty == null) {
            return BigDecimal.ZERO;
        }
        if (delivyQty == null) {
            return salesQty;
        }
        return salesQty.subtract(delivyQty);
    }

    /**
     * 获取未发货金额
     */
    public BigDecimal getUndeliveredMoney() {
        if (salePrice == null) {
            return BigDecimal.ZERO;
        }
        return salePrice.multiply(getUndeliveredQty());
    }

    /**
     * 检查是否已完全发货
     */
    public boolean isFullyDelivered() {
        return getUndeliveredQty().compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * 检查是否部分发货
     */
    public boolean isPartiallyDelivered() {
        if (delivyQty == null || delivyQty.compareTo(BigDecimal.ZERO) == 0) {
            return false;
        }
        return !isFullyDelivered();
    }
}
