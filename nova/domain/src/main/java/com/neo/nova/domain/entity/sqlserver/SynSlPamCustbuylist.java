package com.neo.nova.domain.entity.sqlserver;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @TableName syn_sl_pam_custbuylist
 */
@TableName(value = "syn_sl_pam_custbuylist")
@Data
public class SynSlPamCustbuylist {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     *
     */
    @TableField(value = "PriceTypeID")
    private Integer PriceTypeID;

    /**
     *
     */
    @TableField(value = "MateID")
    private Integer mateid;

    /**
     *
     */
    @TableField(value = "SellPrice")
    private BigDecimal sellprice;

    /**
     *
     */
    @TableField(value = "PrintPrice")
    private String printprice;
}