package com.neo.nova.domain.enums;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Getter;

import java.util.Arrays;
import java.util.LinkedHashSet;
import java.util.List;

/**
 * 目标数据类型枚举
 * 用于指定查询的数据对象类型，支持多选
 *
 * <AUTHOR>
 * @since 2025/8/14
 **/
@Getter
public enum TargetDataTypeEnum {

    SALES_AMOUNT_ALL("salesAmountAll", "销售额"), //前端传salesAmountAll = 查询salesAmount、salesAmountRatio、salesAmountMomGrowth、salesAmountYoyGrowth
    SALES_AMOUNT("salesAmount", "销售额"),
    SALES_AMOUNT_RATIO("salesAmountRatio", "销售额占比"),
    SALES_AMOUNT_MOM_GROWTH("salesAmountMomGrowth", "销售额环比增长"),
    SALES_AMOUNT_YOY_GROWTH("salesAmountYoyGrowth", "销售额同比增长"),

    GROSS_PROFIT_AMOUNT_ALL("grossProfitAmountAll", "毛利额"),
    GROSS_PROFIT_AMOUNT("grossProfitAmount", "毛利额"),
    GROSS_PROFIT_AMOUNT_RATIO("grossProfitAmountRatio", "毛利额占比"),
    GROSS_PROFIT_AMOUNT_MOM_GROWTH("grossProfitAmountMomGrowth", "毛利额环比增长"),
    GROSS_PROFIT_AMOUNT_YOY_GROWTH("grossProfitAmountYoyGrowth", "毛利额同比增长"),

    GROSS_PROFIT_MARGIN_ALL("grossProfitMarginAll", "毛利率"),
    GROSS_PROFIT_MARGIN("grossProfitMargin", "毛利率"),
    GROSS_PROFIT_MARGIN_MOM_GROWTH("grossProfitMarginMomGrowth", "毛利率环比增长"),
    GROSS_PROFIT_MARGIN_YOY_GROWTH("grossProfitMarginYoyGrowth", "毛利率同比增长"),
    ;

    private final String code;
    private final String name;

    TargetDataTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }


    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举对象，如果不存在则返回null
     */
    public static TargetDataTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据代码获取名称
     *
     * @param code 代码
     * @return 名称，如果不存在则返回null
     */
    public static String getNameByCode(String code) {
        TargetDataTypeEnum enumItem = getByCode(code);
        return enumItem != null ? enumItem.getName() : null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }


    // 0 当前 1 占比 2 环比 3 同比
    public static List<String> listByQueryType(List<String> targetDataTypes, int queryType) {
        if (queryType == 0) {
            return listSelectColumns(targetDataTypes);
        } else if (queryType == 1) {
            return listRatioSelectColumns(targetDataTypes);
        } else if (queryType == 2) {
            return listMomColumns(targetDataTypes);
        } else if (queryType == 3) {
            return listYoyColumns(targetDataTypes);
        }
        return Lists.newArrayList();
    }


    public static List<String> listSelectColumns(List<String> targetDataTypes) {
        List<String> result = Lists.newArrayList();
        if (CollectionUtil.isEmpty(targetDataTypes)) {
            return result;
        }
        for (String targetDataType : targetDataTypes) {
            if (SALES_AMOUNT_ALL.code.equals(targetDataType)
                    || SALES_AMOUNT.code.equals(targetDataType)
                    || SALES_AMOUNT_RATIO.code.equals(targetDataType)
                    || SALES_AMOUNT_MOM_GROWTH.code.equals(targetDataType)
                    || SALES_AMOUNT_YOY_GROWTH.code.equals(targetDataType)) {
                result.add("sum(salesAmount) as salesAmount");
            } else if (GROSS_PROFIT_AMOUNT_ALL.code.equals(targetDataType)
                    || GROSS_PROFIT_AMOUNT.code.equals(targetDataType)
                    || GROSS_PROFIT_AMOUNT_RATIO.code.equals(targetDataType)
                    || GROSS_PROFIT_AMOUNT_MOM_GROWTH.code.equals(targetDataType)
                    || GROSS_PROFIT_AMOUNT_YOY_GROWTH.code.equals(targetDataType)) {
                result.add("(sum(salesAmount) - sum(costAmount)) as grossProfitAmount");
            } else if (GROSS_PROFIT_MARGIN_ALL.code.equals(targetDataType)
                    || GROSS_PROFIT_MARGIN.code.equals(targetDataType)
                    || GROSS_PROFIT_MARGIN_MOM_GROWTH.code.equals(targetDataType)
                    || GROSS_PROFIT_MARGIN_YOY_GROWTH.code.equals(targetDataType)) {
                result.add("case when sum(salesAmount) > 0 then (sum(salesAmount) - sum(costAmount)) / sum(salesAmount) * 100 else 0 end as grossProfitMargin");
            }
        }
        return result;
    }


    public static List<String> listRatioSelectColumns(List<String> targetDataTypes) {
        List<String> result = Lists.newArrayList();
        if (CollectionUtil.isEmpty(targetDataTypes)) {
            return result;
        }
        for (String targetDataType : targetDataTypes) {
            if (SALES_AMOUNT_ALL.code.equals(targetDataType)
                    || SALES_AMOUNT_RATIO.code.equals(targetDataType)) {
                result.add("sum(salesAmount) as salesAmount");
            } else if (GROSS_PROFIT_AMOUNT_ALL.code.equals(targetDataType)
                    || GROSS_PROFIT_AMOUNT_RATIO.code.equals(targetDataType)) {
                result.add("(sum(salesAmount) - sum(costAmount)) as grossProfitAmount");
            }
        }
        return result;
    }

    public static List<String> listMomColumns(List<String> targetDataTypes) {
        List<String> result = Lists.newArrayList();
        if (CollectionUtil.isEmpty(targetDataTypes)) {
            return result;
        }
        for (String targetDataType : targetDataTypes) {
            if (SALES_AMOUNT_ALL.code.equals(targetDataType)
                    || SALES_AMOUNT_MOM_GROWTH.code.equals(targetDataType)) {
                result.add("sum(salesAmount) as salesAmount");
            } else if (GROSS_PROFIT_AMOUNT_ALL.code.equals(targetDataType)
                    || GROSS_PROFIT_AMOUNT_MOM_GROWTH.code.equals(targetDataType)) {
                result.add("(sum(salesAmount) - sum(costAmount)) as grossProfitAmount");
            } else if (GROSS_PROFIT_MARGIN_ALL.code.equals(targetDataType)
                    || GROSS_PROFIT_MARGIN_MOM_GROWTH.code.equals(targetDataType)) {
                result.add("case when sum(salesAmount) > 0 then (sum(salesAmount) - sum(costAmount)) / sum(salesAmount) * 100 else 0 end as grossProfitMargin");
            }
        }
        return result;
    }

    public static List<String> listYoyColumns(List<String> targetDataTypes) {
        List<String> result = Lists.newArrayList();
        if (CollectionUtil.isEmpty(targetDataTypes)) {
            return result;
        }
        for (String targetDataType : targetDataTypes) {
            if (SALES_AMOUNT_ALL.code.equals(targetDataType)
                    || SALES_AMOUNT_YOY_GROWTH.code.equals(targetDataType)) {
                result.add("sum(salesAmount) as salesAmount");
            } else if (GROSS_PROFIT_AMOUNT_ALL.code.equals(targetDataType)
                    || GROSS_PROFIT_AMOUNT_YOY_GROWTH.code.equals(targetDataType)) {
                result.add("(sum(salesAmount) - sum(costAmount)) as grossProfitAmount");
            } else if (GROSS_PROFIT_MARGIN_ALL.code.equals(targetDataType)
                    || GROSS_PROFIT_MARGIN_YOY_GROWTH.code.equals(targetDataType)) {
                result.add("case when sum(salesAmount) > 0 then (sum(salesAmount) - sum(costAmount)) / sum(salesAmount) * 100 else 0 end as grossProfitMargin");
            }
        }
        return result;
    }


    public static List<String> listTableColumns(List<String> targetDataTypes, boolean isTrend) {
        LinkedHashSet<String> result = Sets.newLinkedHashSet();
        if (CollectionUtil.isEmpty(targetDataTypes)) {
            return Lists.newArrayList(result);
        }
        for (String targetDataType : targetDataTypes) {
            if (TargetDataTypeEnum.SALES_AMOUNT_ALL.getCode().equals(targetDataType)) {
                result.add(TargetDataTypeEnum.SALES_AMOUNT.getCode());
                if (!isTrend) result.add(TargetDataTypeEnum.SALES_AMOUNT_RATIO.getCode());
                result.add(TargetDataTypeEnum.SALES_AMOUNT_MOM_GROWTH.getCode());
                result.add(TargetDataTypeEnum.SALES_AMOUNT_YOY_GROWTH.getCode());
            } else if (TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_ALL.getCode().equals(targetDataType)) {
                result.add(TargetDataTypeEnum.GROSS_PROFIT_AMOUNT.getCode());
                if (!isTrend) result.add(TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_RATIO.getCode());
                result.add(TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_MOM_GROWTH.getCode());
                result.add(TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_YOY_GROWTH.getCode());
            } else if (TargetDataTypeEnum.GROSS_PROFIT_MARGIN_ALL.getCode().equals(targetDataType)) {
                result.add(TargetDataTypeEnum.GROSS_PROFIT_MARGIN.getCode());
                result.add(TargetDataTypeEnum.GROSS_PROFIT_MARGIN_MOM_GROWTH.getCode());
                result.add(TargetDataTypeEnum.GROSS_PROFIT_MARGIN_YOY_GROWTH.getCode());
            } else {
                result.add(targetDataType);
            }
        }
        return Lists.newArrayList(result);
    }

    public static String getChartShowKey(List<String> targetDataTypes) {
        if (CollectionUtil.isEmpty(targetDataTypes)) {
            return "";
        }
        if (targetDataTypes.contains(TargetDataTypeEnum.SALES_AMOUNT_ALL.getCode())
                || targetDataTypes.contains(TargetDataTypeEnum.SALES_AMOUNT.getCode())) {
            return TargetDataTypeEnum.SALES_AMOUNT.getCode();
        } else if (targetDataTypes.contains(TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_ALL.getCode())
                || targetDataTypes.contains(TargetDataTypeEnum.GROSS_PROFIT_AMOUNT.getCode())) {
            return TargetDataTypeEnum.GROSS_PROFIT_AMOUNT.getCode();
        } else if (targetDataTypes.contains(TargetDataTypeEnum.GROSS_PROFIT_MARGIN_ALL.getCode())
                || targetDataTypes.contains(TargetDataTypeEnum.GROSS_PROFIT_MARGIN.getCode())) {
            return TargetDataTypeEnum.GROSS_PROFIT_MARGIN.getCode();
        }
        return targetDataTypes.get(0);
    }


    public static boolean isPercent(String targetDataType) {
        return TargetDataTypeEnum.SALES_AMOUNT_RATIO.getCode().equals(targetDataType)
                || TargetDataTypeEnum.SALES_AMOUNT_MOM_GROWTH.getCode().equals(targetDataType)
                || TargetDataTypeEnum.SALES_AMOUNT_YOY_GROWTH.getCode().equals(targetDataType)
                || TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_RATIO.getCode().equals(targetDataType)
                || TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_MOM_GROWTH.getCode().equals(targetDataType)
                || TargetDataTypeEnum.GROSS_PROFIT_AMOUNT_YOY_GROWTH.getCode().equals(targetDataType)
                || TargetDataTypeEnum.GROSS_PROFIT_MARGIN.getCode().equals(targetDataType)
                || TargetDataTypeEnum.GROSS_PROFIT_MARGIN_MOM_GROWTH.getCode().equals(targetDataType)
                || TargetDataTypeEnum.GROSS_PROFIT_MARGIN_YOY_GROWTH.getCode().equals(targetDataType);
    }

}
