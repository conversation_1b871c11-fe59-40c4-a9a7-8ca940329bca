package com.neo.nova.domain.dto;


import java.io.Serializable;
import java.util.*;

/**
 * 图表数据
 *
 * <AUTHOR>
 * @since Aug 23 2016
 */
public class Chart<X, Y> implements Serializable {
    /**
     * x轴,一般来说,一个图表只有一个x轴,这个地方设置成list是为了以后拓展,万一出现多个x轴
     */
    private List<XAxis<X>> xAxis = new LinkedList<>();
    /**
     * 各条曲线
     */
    private List<Series<Y>> series = new LinkedList<>();
    /**
     * 附加数据
     */
    private Map<String, Object> extra;
    /**
     * 标题
     */
    private String title;

    public Chart() {
    }

    public Chart(Map<X, Y> mapData, String xName, String yName) {
        if (mapData == null) {
            return;
        }
        addXAxis(xName, mapData.keySet());
        addSeries(yName, mapData.values());
    }

    public List<XAxis<X>> getxAxis() {
        return xAxis;
    }

    public void setxAxis(List<XAxis<X>> xAxis) {
        this.xAxis = xAxis;
    }

    public List<Series<Y>> getSeries() {
        return series;
    }

    public void setSeries(List<Series<Y>> series) {
        this.series = series;
    }

    public Map<String, Object> getExtra() {
        return extra;
    }

    public void setExtra(Map<String, Object> extra) {
        this.extra = extra;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 添加x坐标内容
     *
     * @param axis
     */
    public void addXAxis(XAxis<X> axis) {
        xAxis.add(axis);
    }

    /**
     * 添加x坐标内容
     *
     * @param name
     * @param xs
     */
    public void addXAxis(String name, X... xs) {
        XAxis<X> axis = new XAxis<>();
        axis.setName(name);
        axis.setData(Arrays.asList(xs));
        addXAxis(axis);
    }

    public void addXAxis(String name, Collection<X> xs) {
        XAxis<X> axis = new XAxis<>();
        axis.setName(name);
        axis.setData(new ArrayList<>(xs));
        addXAxis(axis);
    }


    /**
     * 添加y轴数据
     *
     * @param ser
     */
    public void addSeries(Series<Y> ser) {
        if (ser == null) {
            return;
        }

        for (Series<Y> s : series) {
            if (s.getName() != null && s.getName().equals(ser.getName())) {
                s.addData(ser.getData());
                return;
            }
        }
        series.add(ser);
    }

    /**
     * 添加y轴数据
     *
     * @param name 曲线名称
     * @param ys
     */
    public void addSeries(String name, Y... ys) {
        Series<Y> ser = new Series<>();
        ser.setName(name);
        ser.setData(new ArrayList<>(Arrays.asList(ys)));
        addSeries(ser);
    }

    public void addSeries(String name, Collection<Y> ys) {
        Series<Y> ser = new Series<>();
        ser.setName(name);
        ser.setData(new ArrayList<>(ys));
        addSeries(ser);
    }

    public void addSeries(String name, Collection<Y> ys, Integer yAxisIndex) {
        Series<Y> ser = new Series<>();
        ser.setName(name);
        ser.setData(new ArrayList<>(ys));
        ser.setyAxisIndex(yAxisIndex);
        addSeries(ser);
    }

    /**
     * 添加附加数据
     *
     * @param name
     * @param value
     */
    public void addExtra(String name, Object value) {
        if (extra == null) {
            extra = new HashMap<>();
        }
        extra.put(name, value);
    }


    /**
     * 设置x轴的名称
     *
     * @param names
     */
    public void setxAxisName(String... names) {
        if (names == null) {
            return;
        }
        for (int i = 0; i < names.length; i++) {
            XAxis x = xAxis.get(i);
            if (x != null) {
                x.setName(names[i]);
            }
        }
    }

    /**
     * X轴的值
     *
     * @return
     */
    public List<X> xAxisValueList() {
        if (xAxis == null || xAxis.isEmpty()) {
            return new ArrayList<>();
        }
        return xAxis.get(0).getData();
    }

    /**
     * 图表曲线
     */
    public static class Series<Y> implements Serializable {
        private static final long serialVersionUID = 4914938746318351230L;
        private String name;//曲线名称
        private List<Y> data;//数据y坐标
        private Integer yAxisIndex = 0; //页面展示的样式

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<Y> getData() {
            return data;
        }

        public void setData(List<Y> data) {
            this.data = data;
        }

        public static long getSerialVersionUID() {
            return serialVersionUID;
        }

        public Integer getyAxisIndex() {
            return yAxisIndex;
        }

        public void setyAxisIndex(Integer yAxisIndex) {
            this.yAxisIndex = yAxisIndex;
        }

        /**
         * 添加数据
         *
         * @param dt
         */
        public void addData(List<Y> dt) {
            if (dt == null) return;
            if (data == null) {
                data = new ArrayList<>();
            }
            data.addAll(dt);
        }
    }

    /**
     * x坐标值
     */
    public static class XAxis<X> implements Serializable {
        private static final long serialVersionUID = -3584371575629851128L;
        private String name;
        private List<X> data;//x坐标点

        public List<X> getData() {
            return data;
        }

        public void setData(List<X> data) {
            this.data = data;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
