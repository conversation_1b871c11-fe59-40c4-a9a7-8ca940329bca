package com.neo.nova.domain.entity.sqlserver;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @TableName syn_sl_pam_custarea
 */
@TableName(value = "syn_sl_pam_custarea")
@Data
public class SynSlPamCustarea {
    /**
     * 主键ID
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    /**
     *
     */
    @TableField(value = "CustareaNo")
    private String custareano;

    /**
     *
     */
    @TableField(value = "CustareaName")
    private String custareaname;

    /**
     *
     */
    @TableField(value = "description")
    private String description;

    /**
     *
     */
    @TableField(value = "UState")
    private String ustate;

    /**
     *
     */
    @TableField(value = "Depth")
    private Integer depth;

    /**
     *
     */
    @TableField(value = "ParentID")
    private Integer parentid;

    /**
     *
     */
    @TableField(value = "OrderCode")
    private String ordercode;
}