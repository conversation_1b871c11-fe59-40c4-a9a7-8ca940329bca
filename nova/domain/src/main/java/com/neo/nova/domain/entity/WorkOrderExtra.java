package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 工单关联信息
 * @TableName WorkOrderExtra
 */
@TableName(value ="WorkOrderExtra")
@Data
public class WorkOrderExtra implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 租户id
     */
    @TableField(value = "tenantId")
    private Long tenantId;

    /**
     * 用户id
     */
    @TableField(value = "userId")
    private Long userId;

    /**
     * 键名称
     */
    @TableField(value = "keyName")
    private String keyName;

    /**
     * 键值
     */
    @TableField(value = "keyValue")
    private String keyValue;

    /**
     * 工单id
     */
    @TableField(value = "workOrderId")
    private Long workOrderId;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private LocalDateTime created;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private LocalDateTime updated;

    /**
     * 是否删除 0 否 1 是
     */
    @TableField(value = "deleted")
    private Integer deleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}