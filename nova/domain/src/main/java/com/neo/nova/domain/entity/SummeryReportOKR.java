package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 每日总结报告表
 * @TableName SummeryReportOKR
 */
@TableName(value ="SummeryReportOKR")
@Data
public class SummeryReportOKR {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    @TableField(value = "userId")
    private Long userId;

    /**
     * 用户名称
     */
    @TableField(value = "uname")
    private String uname;

    /**
     * okr日期，如202508w1,202508,2025Q1,2025
     */
    @TableField(value = "okrDate")
    private String okrDate;

    /**
     * okr开始时间
     */
    @TableField(value = "beginDate")
    private String beginDate;

    /**
     * okr结束时间
     */
    @TableField(value = "endDate")
    private String endDate;

    /**
     * 时间类型，1-周, 2-月，3-季度，4-年
     */
    @TableField(value = "dateType")
    private Integer dateType;

    /**
     * 目标类型，1-销售收入, 2-毛利润，3-净利润，4-损耗
     */
    @TableField(value = "objectType")
    private Integer objectType;

    /**
     * 目标值
     */
    @TableField(value = "objectAmount")
    private BigDecimal objectAmount;

    /**
     * 当期值
     */
    @TableField(value = "currentPeriodAmount")
    private BigDecimal currentPeriodAmount;

    /**
     * 环比值
     */
    @TableField(value = "circlePeriodAmount")
    private BigDecimal circlePeriodAmount;

    /**
     * 同比值
     */
    @TableField(value = "samePeriodAmount")
    private BigDecimal samePeriodAmount;

    /**
     * AI报告建议
     */
    @TableField(value = "aiReport")
    private String aiReport;

    /**
     * 删除标记，0-未删除，1-已删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private Long created;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private Long updated;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SummeryReportOKR other = (SummeryReportOKR) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getUname() == null ? other.getUname() == null : this.getUname().equals(other.getUname()))
            && (this.getOkrDate() == null ? other.getOkrDate() == null : this.getOkrDate().equals(other.getOkrDate()))
            && (this.getBeginDate() == null ? other.getBeginDate() == null : this.getBeginDate().equals(other.getBeginDate()))
            && (this.getEndDate() == null ? other.getEndDate() == null : this.getEndDate().equals(other.getEndDate()))
            && (this.getDateType() == null ? other.getDateType() == null : this.getDateType().equals(other.getDateType()))
            && (this.getObjectType() == null ? other.getObjectType() == null : this.getObjectType().equals(other.getObjectType()))
            && (this.getObjectAmount() == null ? other.getObjectAmount() == null : this.getObjectAmount().equals(other.getObjectAmount()))
            && (this.getCurrentPeriodAmount() == null ? other.getCurrentPeriodAmount() == null : this.getCurrentPeriodAmount().equals(other.getCurrentPeriodAmount()))
            && (this.getCirclePeriodAmount() == null ? other.getCirclePeriodAmount() == null : this.getCirclePeriodAmount().equals(other.getCirclePeriodAmount()))
            && (this.getSamePeriodAmount() == null ? other.getSamePeriodAmount() == null : this.getSamePeriodAmount().equals(other.getSamePeriodAmount()))
            && (this.getAiReport() == null ? other.getAiReport() == null : this.getAiReport().equals(other.getAiReport()))
            && (this.getDeleted() == null ? other.getDeleted() == null : this.getDeleted().equals(other.getDeleted()))
            && (this.getCreated() == null ? other.getCreated() == null : this.getCreated().equals(other.getCreated()))
            && (this.getUpdated() == null ? other.getUpdated() == null : this.getUpdated().equals(other.getUpdated()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getUname() == null) ? 0 : getUname().hashCode());
        result = prime * result + ((getOkrDate() == null) ? 0 : getOkrDate().hashCode());
        result = prime * result + ((getBeginDate() == null) ? 0 : getBeginDate().hashCode());
        result = prime * result + ((getEndDate() == null) ? 0 : getEndDate().hashCode());
        result = prime * result + ((getDateType() == null) ? 0 : getDateType().hashCode());
        result = prime * result + ((getObjectType() == null) ? 0 : getObjectType().hashCode());
        result = prime * result + ((getObjectAmount() == null) ? 0 : getObjectAmount().hashCode());
        result = prime * result + ((getCurrentPeriodAmount() == null) ? 0 : getCurrentPeriodAmount().hashCode());
        result = prime * result + ((getCirclePeriodAmount() == null) ? 0 : getCirclePeriodAmount().hashCode());
        result = prime * result + ((getSamePeriodAmount() == null) ? 0 : getSamePeriodAmount().hashCode());
        result = prime * result + ((getAiReport() == null) ? 0 : getAiReport().hashCode());
        result = prime * result + ((getDeleted() == null) ? 0 : getDeleted().hashCode());
        result = prime * result + ((getCreated() == null) ? 0 : getCreated().hashCode());
        result = prime * result + ((getUpdated() == null) ? 0 : getUpdated().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", uname=").append(uname);
        sb.append(", okrDate=").append(okrDate);
        sb.append(", beginDate=").append(beginDate);
        sb.append(", endDate=").append(endDate);
        sb.append(", dateType=").append(dateType);
        sb.append(", objectType=").append(objectType);
        sb.append(", objectAmount=").append(objectAmount);
        sb.append(", currentPeriodAmount=").append(currentPeriodAmount);
        sb.append(", circlePeriodAmount=").append(circlePeriodAmount);
        sb.append(", samePeriodAmount=").append(samePeriodAmount);
        sb.append(", aiReport=").append(aiReport);
        sb.append(", deleted=").append(deleted);
        sb.append(", created=").append(created);
        sb.append(", updated=").append(updated);
        sb.append("]");
        return sb.toString();
    }
}