package com.neo.nova.domain.entity.sqlserver;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @TableName syn_sl_pam_saleser
 */
@TableName(value = "syn_sl_pam_saleser")
@Data
public class SynSlPamSaleser {
    /**
     *
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    /**
     *
     */
    @TableField(value = "SaleserNo")
    private String saleserno;

    /**
     *
     */
    @TableField(value = "SaleserName")
    private String salesername;

    /**
     *
     */
    @TableField(value = "Ustate")
    private String ustate;

    @TableField(value = "PhoneNo")
    private String phoneNo;
}